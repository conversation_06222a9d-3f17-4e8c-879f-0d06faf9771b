<template>
  <div class="relative z-0 flex w-full h-full max-h-full overflow-hidden bg-primary-500">
    <Sidebar />
    <div class="flex flex-col h-full w-2/3 overflow-hidden bg-primary">
      <EmailsSection />
    </div>
  </div>
</template>

<script setup lang="ts">
import Sidebar from "../components/chat/EmailCategorySection.vue";
import EmailsSection from "../components/chat/EmailsSection.vue";
</script>
<style scoped>
.app-background {
  background: linear-gradient(135deg, #ffffff 0%, #ffe4e1 30%, #ffb399 60%, #ff5733 85%, #ff6f00 100%);
}
</style>
