use std::fs;
use std::path::Path;

use diesel::prelude::*;
use diesel::r2d2::{self, ConnectionManager, Pool, PooledConnection};
use diesel::sqlite::SqliteConnection;
use diesel_migrations::{embed_migrations, EmbeddedMigrations, MigrationHarness};
use lazy_static::lazy_static;
use once_cell::sync::Lazy;
use std::sync::Mutex;

const MIGRATIONS: EmbeddedMigrations = embed_migrations!();

pub fn init() {
    if !db_file_exists() {
        create_db_file();
    }
    enable_wal_mode();
    run_migrations();
}

pub type DbPool = Pool<ConnectionManager<SqliteConnection>>;
pub type DbConn = PooledConnection<ConnectionManager<SqliteConnection>>;
pub static DB_POOL: Lazy<DbPool> = Lazy::new(establish_pool);

pub fn establish_pool() -> DbPool {
    let db_path = get_db_path();
    let manager = ConnectionManager::<SqliteConnection>::new(db_path);
    Pool::builder()
        .max_size(15) // You can increase if needed
        .build(manager)
        .expect("Failed to create SQLite connection pool")
}

pub fn get_pooled_connection() -> DbConn {
    DB_POOL.get().expect("Failed to get connection from pool")
}
// pub fn get_pooled_connection() -> DbConn {
//     DB_POOL
//         .lock()
//         .expect("DB pool mutex poisoned")
//         .get()
//         .expect("Failed to get DB connection from pool")
// }

// lazy_static! {
//     static ref DB_POOL: Mutex<DbPool> = Mutex::new(establish_pool());
// }

pub fn establish_db_connection() -> SqliteConnection {
    let db_path = get_db_path().clone();

    SqliteConnection::establish(db_path.as_str())
        .unwrap_or_else(|_| panic!("Error connecting to {}", db_path))
}

fn enable_wal_mode() {
    let connection = &mut get_pooled_connection();
    diesel::sql_query("PRAGMA journal_mode = WAL;")
        .execute(connection)
        .expect("Failed to enable WAL mode");
}

fn run_migrations() {
    // let mut connection = establish_connection();
    let connection = &mut get_pooled_connection();
    connection.run_pending_migrations(MIGRATIONS).unwrap();
}

fn establish_connection() -> SqliteConnection {
    let db_path = "sqlite://".to_string() + get_db_path().as_str();

    SqliteConnection::establish(&db_path)
        .unwrap_or_else(|_| panic!("Error connecting to {}", db_path))
}

fn create_db_file() {
    let db_path = get_db_path();
    let db_dir = Path::new(&db_path).parent().unwrap();

    if !db_dir.exists() {
        fs::create_dir_all(db_dir).unwrap();
    }

    fs::File::create(db_path).unwrap();
}

fn db_file_exists() -> bool {
    let db_path = get_db_path();
    Path::new(&db_path).exists()
}

fn get_db_path() -> String {
    let home_dir = dirs::home_dir().unwrap();
    home_dir.to_str().unwrap().to_string() + "/.config/oway/database.sqlite"
}
