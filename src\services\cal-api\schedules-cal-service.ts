
import CalApi from "./cal-api/cal-api-service";
import { CalSchedule } from "../models/schedule-model";

interface CalSchedulesRes {
  schedules: CalSchedule[]
}

interface CalScheduleRes {
  schedule: CalSchedule
}

async function getUserSchedules(userId: number): Promise<CalSchedule[] | null> {
  try {
    const response = await CalApi.get<CalSchedulesRes>("api/schedules", { params: { userId } });
    //  console.log("CalApi.get =>", response);
    console.log("🚀 ~ getUserSchedules ~ response.data.schedules:", response.data.schedules)
    return response.data.schedules;
  } catch (error) {
    console.error("ERR => ", error);
    return null
  }
}

interface CalUpdateSchedule extends Omit<CalSchedule, "id" | "userId" | "availability"> { }

async function updateSchedule(schId: number, sch: CalUpdateSchedule): Promise<CalSchedule | null> {
  try {
    const response = await CalApi.patch<CalSchedule>(`api/schedules/${schId}`, sch);
    return response.data;
  } catch (error) {
    console.error("ERR => ", error);
    return null
  }
}

interface CalCreateSchedule extends Omit<CalSchedule, "id" | "availability"> { }

async function createSchedule(sch: Partial<CalCreateSchedule>): Promise<CalSchedule | null> {
  try {
    //  console.log("CREATE USER =>", sch);

    const response = await CalApi.post<CalScheduleRes>(`api/schedules`, sch);
    return response.data.schedule;
  } catch (error) {
    console.error("ERR => ", error);
    return null
  }
}



async function deleteSchedule(schId: number): Promise<{ message: string } | null> {
  try {
    const response = await CalApi.delete(`api/schedules/${schId}`);
    return response.data;
  } catch (error) {
    console.error("ERR => ", error);
    return null
  }
}



export const calSchedulesService = {
  getUserSchedules,
  updateSchedule,
  createSchedule,
  deleteSchedule
}
