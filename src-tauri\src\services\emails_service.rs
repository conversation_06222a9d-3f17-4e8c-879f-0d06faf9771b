use crate::google_api_functions::check_and_refresh_token::check_and_refresh_token;
use crate::google_api_functions::fetch_emails::EmailSummary;
use crate::models::app_data::AppData;
use crate::models::email_category::{ EmailCategory, NewEmailCategory };
use crate::schema::emails::{ self, full_domain };
use crate::schema::emails::{ dsl, thread_id };
use crate::schema::email_logs;
use crate::{ db::establish_db_connection, db::get_pooled_connection, models::email::Email };
use chrono::{ NaiveDate, DateTime, NaiveDateTime, Utc };
use diesel::dsl::{ count, date };
use diesel::prelude::*;
use diesel::result::Error;
use diesel::Connection;
use reqwest::Client;
use serde::{ Deserialize, Serialize };
use serde_json::json;
use tokio::sync::RwLock;
use tracing::info;
use std::collections::HashMap;
use std::collections::HashSet;
use std::sync::Arc;
use std::thread;
use std::time::Duration;
use uuid::Uuid;
use diesel::dsl::count_star;

// pub fn list_emails(category_id: &String, full_child_domains: &String) -> Result<Vec<Email>, Error> {
//     let connection = &mut establish_db_connection();

//     // Initialize the vector that will store all fetched emails
//     let mut all_emails: Vec<Email> = Vec::new();
//     // println!("Child domains category: {:?}", full_child_domains);

//     // Check if `full_child_domains` exists and is not empty
//     if let domains = full_child_domains {
//         // Split the full_child_domains by commas into a list of category IDs
//         let category_ids: Vec<&str> = domains.split(',').map(|id| id.trim()).collect();

//         // Loop through each category ID and fetch emails
//         for id in category_ids {
//             let result = connection.transaction::<_, Error, _>(|conn| {
//                 dsl::emails
//                     .filter(dsl::category.eq(id))
//                     .order_by(dsl::date.desc())
//                     .load::<Email>(conn)
//             });

//             // If fetching emails for this category ID succeeded, append to the email list
//             match result {
//                 Ok(mut emails) => {
//                     all_emails.append(&mut emails);
//                 }
//                 Err(ref e) => {
//                     // Log the error and retry the transaction
//                     println!("Transaction failed for category {} due to: {:?}", id, e);
//                     let retry_result = connection.transaction::<_, Error, _>(|conn| {
//                         dsl::emails
//                             .filter(dsl::category.eq(id))
//                             .order_by(dsl::date.desc())
//                             .load::<Email>(conn)
//                     });

//                     if let Ok(mut retry_emails) = retry_result {
//                         all_emails.append(&mut retry_emails);
//                     } else {
//                         println!("Retry failed for category {}", id);
//                     }
//                 }
//             }
//         }
//     }

//     // Fetch emails for the primary category_id passed as an argument
//     let primary_result = connection.transaction::<_, Error, _>(|conn| {
//         dsl::emails
//             .filter(dsl::category.eq(category_id))
//             .order_by(dsl::date.desc())
//             .load::<Email>(conn)
//     });

//     // Append emails from the primary category if successful
//     match primary_result {
//         Ok(mut primary_emails) => {
//             all_emails.append(&mut primary_emails);
//         }
//         Err(ref e) => {
//             println!("Transaction failed for primary category {} due to: {:?}", category_id, e);
//             let retry_result = connection.transaction::<_, Error, _>(|conn| {
//                 dsl::emails
//                     .filter(dsl::category.eq(category_id))
//                     .order_by(dsl::date.desc())
//                     .load::<Email>(conn)
//             });

//             if let Ok(mut retry_emails) = retry_result {
//                 all_emails.append(&mut retry_emails);
//             } else {
//                 println!("Retry failed for primary category {}", category_id);
//             }
//         }
//     }

//     // Return the complete list of emails
//     Ok(all_emails)
// }

pub fn group_emails_by_thread_sorted(emails: &[Email]) -> Vec<Vec<Email>> {
    let mut thread_map: HashMap<String, Vec<Email>> = HashMap::new();

    // Group emails by thread_id
    for email in emails {
        let th_id = email.clone().thread_id;
        if let Some(thr_id) = th_id {
            thread_map.entry(thr_id).or_insert_with(Vec::new).push(email.clone());
        } else {
            thread_map.entry(email.clone().id).or_insert_with(Vec::new).push(email.clone());
        }
    }

    // Sort emails within each thread by date descending (newest first)
    for emails in thread_map.values_mut() {
        emails.sort_by(|a, b| b.date.cmp(&a.date));
    }

    // Convert to Vec and sort thread groups by the newest email date in each thread
    let mut thread_groups: Vec<Vec<Email>> = thread_map.into_values().collect();

    // Sort thread groups by the date of the newest email in each thread (descending)
    thread_groups.sort_by(|a, b| {
        let a_newest = a.iter().max_by_key(|email| &email.date);
        let b_newest = b.iter().max_by_key(|email| &email.date);

        match (a_newest, b_newest) {
            (Some(a_email), Some(b_email)) => b_email.date.cmp(&a_email.date),
            (Some(_), None) => std::cmp::Ordering::Less,
            (None, Some(_)) => std::cmp::Ordering::Greater,
            (None, None) => std::cmp::Ordering::Equal,
        }
    });

    thread_groups
}

pub fn get_emails_by_categorie_id(
    id: String,
    search_query: Option<SearchQuery>,
    page: usize,
    per_page: usize,
    filters: Option<Filters>
) -> Result<Vec<Email>, diesel::result::Error> {
    // let connection = &mut establish_db_connection();
    let mut conn = get_pooled_connection();

    let fixed_categories = [
        "needs_reply",
        "waiting_response",
        "fyi_read_later",
        "delegated_handled",
        "calendar_scheduling",
        "clients_vips",
        "ads_newsletters",
    ];
    conn.transaction::<_, Error, _>(|conn| {
        // Get current timestamp for filtering snoozed emails
        let now = Utc::now().naive_utc();

        // let mut query = dsl::emails.filter(dsl::category.eq(id)).into_boxed();
        let mut query = if fixed_categories.contains(&id.as_str()) {
            dsl::emails.filter(dsl::storage_location.eq(id.clone())).into_boxed()
        } else {
            dsl::emails.filter(dsl::category.eq(id.clone())).into_boxed()
        };

        // Filter out snoozed emails by checking if thread_id is NOT in active snoozes
        // We use NOT EXISTS to check if the thread is currently snoozed
        // Handle nullable thread_id by checking if it's not null and not in snoozed list
        query = query.filter(
            dsl::thread_id
                .is_null()
                .or(
                    diesel::dsl::not(
                        diesel::dsl::exists(
                            email_logs::table
                                .filter(email_logs::email_id.nullable().eq(dsl::thread_id))
                                .filter(email_logs::snoozed_until.gt(now))
                                .filter(
                                    email_logs::resurfaced
                                        .eq(false)
                                        .or(email_logs::resurfaced.is_null())
                                )
                        )
                    )
                )
        );
        if let Some(sq) = &search_query {
            if let Some(sender) = &sq.sender {
                let pattern = format!("%{}%", sender);
                query = query.filter(dsl::from.like(pattern));
            }
            if let Some(recipient) = &sq.recipient {
                let pattern = format!("%{}%", recipient);
                query = query.filter(dsl::to.like(pattern));
            }
            if let Some(body) = &sq.body {
                let pattern = format!("%{}%", body);
                query = query.filter(dsl::snippet.like(pattern));
            }
            if let Some(subject) = &sq.subject {
                let pattern = format!("%{}%", subject);
                query = query.filter(dsl::subject.like(pattern));
            }
        }

        if let Some(f) = &filters {
            if let Some(category) = &f.sub_category {
                let pattern = format!("%{}%", category);
                println!("sub category => {:?}", pattern);
                query = query.filter(dsl::from.like(pattern));
            }
        }

        query
            .order_by(dsl::date.desc())
            .limit(per_page as i64) // Apply pagination
            .offset(((page - 1) * per_page) as i64) // Offset calculation
            .load::<Email>(conn)
    })
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SearchQuery {
    pub sender: Option<String>,
    pub recipient: Option<String>,
    pub subject: Option<String>,
    pub body: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Filters {
    pub sub_category: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct EmailsOutput {
    pub length: usize,
    pub data: Vec<Vec<Email>>,
}

// Helper function to get total email count across multiple categories
fn get_total_email_count_for_categories(
    category_ids: &[&str],
    search_query: &Option<SearchQuery>,
    filters: &Option<Filters>,
    conn: &mut diesel::SqliteConnection
) -> Result<usize, Error> {
    let fixed_categories = [
        "needs_reply",
        "waiting_response",
        "fyi_read_later",
        "delegated_handled",
        "calendar_scheduling",
        "clients_vips",
        "ads_newsletters",
    ];

    let mut total_count = 0i64;

    for &id in category_ids {
        let mut query = if fixed_categories.contains(&id) {
            dsl::emails.filter(dsl::storage_location.eq(id)).into_boxed()
        } else {
            dsl::emails.filter(dsl::category.eq(id)).into_boxed()
        };

        // Apply search filters
        if let Some(sq) = search_query {
            if let Some(sender) = &sq.sender {
                let pattern = format!("%{}%", sender);
                query = query.filter(dsl::from.like(pattern));
            }
            if let Some(recipient) = &sq.recipient {
                let pattern = format!("%{}%", recipient);
                query = query.filter(dsl::to.like(pattern));
            }
            if let Some(body) = &sq.body {
                let pattern = format!("%{}%", body);
                query = query.filter(dsl::snippet.like(pattern));
            }
            if let Some(subject) = &sq.subject {
                let pattern = format!("%{}%", subject);
                query = query.filter(dsl::subject.like(pattern));
            }
        }

        if let Some(f) = filters {
            if let Some(category) = &f.sub_category {
                let pattern = format!("%{}%", category);
                query = query.filter(dsl::from.like(pattern));
            }
        }

        let count = query.select(count_star()).first::<i64>(conn)?;
        total_count += count;
    }

    Ok(total_count as usize)
}

// Helper function to get all emails across multiple categories (no pagination here)
fn get_all_emails_for_categories(
    category_ids: &[&str],
    search_query: &Option<SearchQuery>,
    filters: &Option<Filters>,
    conn: &mut diesel::SqliteConnection
) -> Result<Vec<Email>, Error> {
    let fixed_categories = [
        "needs_reply",
        "waiting_response",
        "fyi_read_later",
        "delegated_handled",
        "calendar_scheduling",
        "clients_vips",
        "ads_newsletters",
    ];

    // Get current timestamp for filtering snoozed emails
    let now = Utc::now().naive_utc();

    let mut all_emails = Vec::new();

    for &id in category_ids {
        let mut query = if fixed_categories.contains(&id) {
            dsl::emails.filter(dsl::storage_location.eq(id)).into_boxed()
        } else {
            dsl::emails.filter(dsl::category.eq(id)).into_boxed()
        };

        // Filter out snoozed emails
        query = query.filter(
            dsl::id.ne_all(
                email_logs::table
                    .select(email_logs::email_id)
                    .filter(email_logs::snoozed_until.gt(now))
                    .filter(email_logs::resurfaced.eq(false).or(email_logs::resurfaced.is_null()))
            )
        );

        // Apply search filters
        if let Some(sq) = search_query {
            if let Some(sender) = &sq.sender {
                let pattern = format!("%{}%", sender);
                query = query.filter(dsl::from.like(pattern));
            }
            if let Some(recipient) = &sq.recipient {
                let pattern = format!("%{}%", recipient);
                query = query.filter(dsl::to.like(pattern));
            }
            if let Some(body) = &sq.body {
                let pattern = format!("%{}%", body);
                query = query.filter(dsl::snippet.like(pattern));
            }
            if let Some(subject) = &sq.subject {
                let pattern = format!("%{}%", subject);
                query = query.filter(dsl::subject.like(pattern));
            }
        }

        if let Some(f) = filters {
            if let Some(category) = &f.sub_category {
                let pattern = format!("%{}%", category);
                query = query.filter(dsl::from.like(pattern));
            }
        }

        let mut emails = query.order_by(dsl::date.desc()).load::<Email>(conn)?;

        all_emails.append(&mut emails);
    }

    // Sort all emails by date
    all_emails.sort_by(|a, b| b.date.cmp(&a.date));

    Ok(all_emails)
}

// Helper function to apply thread-level pagination
fn paginate_thread_groups(
    thread_groups: Vec<Vec<Email>>,
    page: usize,
    per_page: usize
) -> Vec<Email> {
    let offset = (page - 1) * per_page;
    let end = offset + per_page;

    if offset >= thread_groups.len() {
        Vec::new()
    } else {
        // Get the thread groups for this page
        let page_thread_groups = &thread_groups[offset..end.min(thread_groups.len())];

        // Flatten all emails from the selected thread groups
        let mut page_emails = Vec::new();
        for thread_group in page_thread_groups {
            page_emails.extend(thread_group.clone());
        }

        page_emails
    }
}

pub fn list_emails(
    category_id: &String,
    full_child_domains: &String,
    page: usize,
    per_page: usize,
    search_query: Option<SearchQuery>,
    filters: Option<Filters>
) -> Result<EmailsOutput, Error> {
    let mut conn = get_pooled_connection();

    println!(
        "full child domains {:?}, page: {:?} per page: {:?} primary category {:?}",
        full_child_domains,
        page,
        per_page,
        category_id
    );

    // Get all category IDs to query
    let category_ids: Vec<&str> = if !full_child_domains.is_empty() {
        full_child_domains
            .split(',')
            .map(|id| id.trim())
            .collect()
    } else {
        vec![category_id.as_str()]
    };

    // Get ALL emails from categories (no pagination yet)
    let all_emails = get_all_emails_for_categories(
        &category_ids,
        &search_query,
        &filters,
        &mut conn
    )?;

    // Group emails by thread and sort thread groups
    let thread_groups = group_emails_by_thread_sorted(&all_emails);

    // Calculate total count based on thread groups (not individual emails)
    let total_thread_count = thread_groups.len();

    // Apply pagination at thread level to get complete threads for this page
    let page_emails = paginate_thread_groups(thread_groups.clone(), page, per_page);

    // Re-group the paginated emails to maintain the thread structure for frontend
    let paginated_thread_groups = group_emails_by_thread_sorted(&page_emails);

    println!("Total thread count => {:?}", total_thread_count);
    println!("Fetched emails count for page => {:?}", page_emails.len());
    println!("Thread groups on this page => {:?}", paginated_thread_groups.len());

    Ok(EmailsOutput {
        length: total_thread_count,
        data: paginated_thread_groups,
    })
}

pub fn get_emails_by_thread_id(id: String) -> Result<Vec<Email>, Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    // Initialize the vector that will store all fetched emails
    let all_emails = dsl::emails
        .filter(thread_id.eq(id))
        .order_by(dsl::date.asc())
        .load::<Email>(connection);
    all_emails
}

pub fn get_email_by_id(email_id: &String) -> Result<Email, Error> {
    let connection = &mut get_pooled_connection();

    dsl::emails.filter(dsl::id.eq(email_id)).first::<Email>(connection)
}

pub async fn update_processed_email(
    email_id: &str,
    thread_summary: Option<String>,
    urgency_score: Option<i32>,
    sentiment: Option<String>,
    actionable_items: String,
    attachment_types: String,
    language: String,
    classification: String // <-- NEW
) -> Result<(), Error> {
    // println!("Starting the update_processed_email function...");

    // // Establish database connection
    // println!("Database connection established.");

    // // Logging inputs
    // println!("Thread Summary: {:?}", thread_summary);
    // println!("Urgency Score: {:?}", urgency_score);
    // println!("Sentiment: {:?}", sentiment);
    // println!("Actionable Items: {}", actionable_items);
    // println!("Attachment Types: {}", attachment_types);
    // println!("Language: {}", language);

    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    // Update the database with the processed results
    connection.transaction::<_, Error, _>(|conn| {
        diesel
            ::update(dsl::emails.find(email_id))
            .set((
                dsl::thread_summary.eq(thread_summary.unwrap_or_default()),
                dsl::urgency_score.eq(urgency_score.unwrap_or(0)),
                dsl::sentiment.eq(sentiment.unwrap_or_default()),
                dsl::actionable_items.eq(actionable_items),
                dsl::attachment_types.eq(attachment_types),
                dsl::language.eq(language),
                dsl::process_flag.eq(false), // Mark email as processed
                dsl::storage_location.eq(classification),
            ))
            .execute(conn)
    })?;
    Ok(())
}

pub async fn save_email_summary(email: EmailSummary) -> Result<(), Box<dyn std::error::Error>> {
    let client = Client::builder().timeout(Duration::from_secs(30)).build()?;

    let api_url = "http://***************:8080/api/email-summaries";
    // Prepare the body separately so we can print it
    let body =
        serde_json::json!({
    "id": email.id,
    "userId": email.user_id,
    "subject": email.subject,
    "snippet": email.snippet,
    "threadId": email.thread_id,
    "date": email.date,
    "category": email.category,
    "isRead": email.is_read,
    "shard": email.shard,
}).to_string();

    // ✅ Print the outgoing body
    println!("📤 Sending email summary payload:\n{}", body);

    let response = client
        .post(api_url)
        .header("Content-Type", "application/json")
        .body(body)
        .send().await?;

    if response.status().is_success() {
        println!("✅ Email saved successfully");
        Ok(())
    } else {
        let status = response.status();
        let error_body = response.text().await.unwrap_or_default();
        Err(format!("❌ Failed to save email. Status: {}, Body: {}", status, error_body).into())
    }
}
pub fn store_new_email(new_message: &Email) -> Result<(), Error> {
    let max_attempts = 5; // Maximum number of retry attempts
    let mut attempts = 0;
    let mut backoff = 100; // Initial backoff time in milliseconds

    while attempts < max_attempts {
        // let connection = &mut establish_db_connection();
        let connection = &mut get_pooled_connection();

        let result = connection.transaction::<_, Error, _>(|conn| {
            // Check if the email already exists
            let existing_email = emails::dsl::emails
                .filter(emails::dsl::id.eq(&new_message.id))
                .first::<Email>(conn)
                .optional()?;

            if existing_email.is_none() {
                // If the email doesn't exist, insert it
                diesel::insert_into(emails::table).values(new_message).execute(conn)?;
            } else {
                // Optionally, log that the email already exists
                println!("Email with id {} already exists, skipping insertion", new_message.id);
            }

            Ok(())
        });

        match result {
            Ok(_) => {
                return Ok(());
            } // Success, exit the loop
            Err(
                diesel::result::Error::DatabaseError(
                    diesel::result::DatabaseErrorKind::Unknown,
                    info,
                ),
            ) if info.message().contains("database is locked") => {
                // If the database is locked, retry after a delay
                attempts += 1;
                println!(
                    "Database is locked, attempt {}/{}. Inside store_new_email  Retrying in {}ms...",
                    attempts,
                    max_attempts,
                    backoff
                );
                thread::sleep(Duration::from_millis(backoff)); // Wait before retrying
                backoff *= 2; // Exponential backoff
            }
            Err(e) => {
                return Err(e);
            } // For any other error, return immediately
        }
    }

    Err(
        diesel::result::Error::DatabaseError(
            diesel::result::DatabaseErrorKind::Unknown,
            Box::new("Max retry attempts reached, database is locked.".to_string())
        )
    )
}

pub fn delete_emails(email_id: String) -> Result<(), Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        // Delete the email from the emails table
        diesel
            ::delete(dsl::emails.filter(crate::schema::emails::dsl::id.eq(email_id)))
            .execute(conn)
            .map_err(|err| {
                eprintln!("Error deleting messages: {}", err);
                err
            })?;

        Ok(())
    })
}

pub fn delete_all_emails() -> Result<usize, Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        // Delete all emails from the emails table
        let deleted_rows = diesel
            ::delete(dsl::emails)
            .execute(conn)
            .map_err(|err| {
                eprintln!("Error deleting all emails: {}", err);
                err
            })?;

        Ok(deleted_rows)
    })
}

pub fn list_emails_with_process_flag() -> Result<Vec<Email>, Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    // Initialize the vector that will store all fetched emails
    let mut all_emails: Vec<Email> = Vec::new();

    // Fetch all emails where process_flag is true
    let result = connection.transaction::<_, Error, _>(|conn| {
        dsl::emails
            .filter(dsl::process_flag.eq(true)) // Filter for emails with process_flag = true
            .order_by(dsl::date.desc()) // Sort by date in descending order
            .load::<Email>(conn)
    });

    // Append the result to all_emails vector
    match result {
        Ok(mut emails) => {
            all_emails.append(&mut emails);
        }
        Err(ref e) => {
            println!(
                "Transaction failed for fetching emails with process_flag = true due to: {:?}",
                e
            );
            let retry_result = connection.transaction::<_, Error, _>(|conn| {
                dsl::emails
                    .filter(dsl::process_flag.eq(true)) // Retry fetching emails with process_flag = true
                    .order_by(dsl::date.desc())
                    .load::<Email>(conn)
            });

            if let Ok(mut retry_emails) = retry_result {
                all_emails.append(&mut retry_emails);
            } else {
                println!("Retry failed for fetching emails with process_flag = true");
            }
        }
    }

    // Return the complete list of emails with process_flag = true
    Ok(all_emails)
}

// Advanced functions

// •	Total email count
// •	Unread and starred email counts
// •	Emails by category
// •	Latest email date
// •	Unique senders list
// •	Email count within a date range

pub fn count_total_emails() -> Result<i64, Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        // Count all emails in the emails table
        let total_emails = dsl::emails
            .count()
            .get_result::<i64>(conn)
            .map_err(|err| {
                eprintln!("Error counting total emails: {}", err);
                err
            })?;

        Ok(total_emails)
    })
}

pub fn count_unread_emails() -> Result<i64, Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        // Count emails where the unread flag is set (is_read is false or null)
        let unread_emails = dsl::emails
            .filter(dsl::is_read.eq(false).or(dsl::is_read.is_null()))
            .count()
            .get_result::<i64>(conn)
            .map_err(|err| {
                eprintln!("Error counting unread emails: {}", err);
                err
            })?;

        Ok(unread_emails)
    })
}

// Count unread threads (not individual emails) per category
pub fn count_unread_threads_per_category(category_id: &str) -> Result<i64, Error> {
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        let fixed_categories = [
            "needs_reply",
            "waiting_response",
            "fyi_read_later",
            "delegated_handled",
            "calendar_scheduling",
            "clients_vips",
            "ads_newsletters",
        ];

        // Build query based on category type
        let query = if fixed_categories.contains(&category_id) {
            dsl::emails.filter(dsl::storage_location.eq(category_id)).into_boxed()
        } else {
            dsl::emails.filter(dsl::category.eq(category_id)).into_boxed()
        };

        // Get current timestamp for filtering snoozed emails
        let now = chrono::Utc::now().naive_utc();

        // Get all unread emails and then count distinct thread_ids
        let unread_emails = query
            .filter(dsl::is_read.eq(false).or(dsl::is_read.is_null()))
            .filter(
                dsl::id.ne_all(
                    crate::schema::email_logs::table
                        .select(crate::schema::email_logs::email_id)
                        .filter(crate::schema::email_logs::snoozed_until.gt(now))
                        .filter(
                            crate::schema::email_logs::resurfaced
                                .eq(false)
                                .or(crate::schema::email_logs::resurfaced.is_null())
                        )
                )
            )
            .load::<crate::models::email::Email>(conn)
            .map_err(|err| {
                eprintln!("Error loading unread emails for category {}: {}", category_id, err);
                err
            })?;

        // Count distinct thread_ids
        let mut thread_ids = std::collections::HashSet::new();
        for email in unread_emails {
            if let Some(email_thread_id) = email.thread_id {
                thread_ids.insert(email_thread_id);
            }
        }

        Ok(thread_ids.len() as i64)
    })
}

// Internal function to recalculate unread counts within an existing transaction
fn recalculate_category_unread_counts_internal(
    conn: &mut diesel::SqliteConnection
) -> Result<(), Error> {
    use crate::schema::email_categories::dsl as cat_dsl;

    // Get all categories
    let categories = cat_dsl::email_categories
        .select((cat_dsl::category_id, cat_dsl::child_full_domains))
        .load::<(String, Option<String>)>(conn)?;

    for (category_id, child_domains) in categories {
        // Skip child categories (those with empty child_full_domains)
        if child_domains.is_none() || child_domains.as_ref().unwrap().is_empty() {
            continue;
        }

        // Count unread threads for this category
        let unread_thread_count = if !child_domains.as_ref().unwrap().is_empty() {
            // For parent categories, count across all child domains
            let child_category_ids: Vec<&str> = child_domains
                .as_ref()
                .unwrap()
                .split(',')
                .map(|id| id.trim())
                .collect();

            let mut total_threads = 0i64;
            for &child_id in &child_category_ids {
                total_threads += count_unread_threads_per_category_internal(child_id, conn)?;
            }
            total_threads
        } else {
            count_unread_threads_per_category_internal(&category_id, conn)?
        };

        // Update the category's unread_count
        diesel
            ::update(
                cat_dsl::email_categories
                    .filter(cat_dsl::category_id.eq(&category_id))
                    .filter(cat_dsl::child_full_domains.is_not_null())
                    .filter(cat_dsl::child_full_domains.ne(""))
            )
            .set(cat_dsl::unread_count.eq(unread_thread_count as i32))
            .execute(conn)
            .map_err(|err| {
                eprintln!("Error updating unread count for category {}: {}", category_id, err);
                err
            })?;

        println!(
            "Updated category {} unread count to {} threads",
            category_id,
            unread_thread_count
        );
    }

    Ok(())
}

// Count unread threads per category within an existing transaction
fn count_unread_threads_per_category_internal(
    category_id: &str,
    conn: &mut diesel::SqliteConnection
) -> Result<i64, Error> {
    let fixed_categories = [
        "needs_reply",
        "waiting_response",
        "fyi_read_later",
        "delegated_handled",
        "calendar_scheduling",
        "clients_vips",
        "ads_newsletters",
    ];

    // Build query based on category type
    let query = if fixed_categories.contains(&category_id) {
        dsl::emails.filter(dsl::storage_location.eq(category_id)).into_boxed()
    } else {
        dsl::emails.filter(dsl::category.eq(category_id)).into_boxed()
    };

    // Get current timestamp for filtering snoozed emails
    let now = chrono::Utc::now().naive_utc();

    // Get all unread emails and then count distinct thread_ids
    let unread_emails = query
        .filter(dsl::is_read.eq(false).or(dsl::is_read.is_null()))
        .filter(
            dsl::id.ne_all(
                crate::schema::email_logs::table
                    .select(crate::schema::email_logs::email_id)
                    .filter(crate::schema::email_logs::snoozed_until.gt(now))
                    .filter(
                        crate::schema::email_logs::resurfaced
                            .eq(false)
                            .or(crate::schema::email_logs::resurfaced.is_null())
                    )
            )
        )
        .load::<crate::models::email::Email>(conn)
        .map_err(|err| {
            eprintln!("Error loading unread emails for category {}: {}", category_id, err);
            err
        })?;

    // Count distinct thread_ids
    let mut thread_ids = std::collections::HashSet::new();
    for email in unread_emails {
        if let Some(email_thread_id) = email.thread_id {
            thread_ids.insert(email_thread_id);
        }
    }

    Ok(thread_ids.len() as i64)
}

// Recalculate and update unread thread counts for all categories
pub fn recalculate_category_unread_counts() -> Result<(), Error> {
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        recalculate_category_unread_counts_internal(conn)
    })
}

pub fn mark_emails_as_read(email_ids: Vec<String>) -> Result<(), Error> {
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        // Update emails to mark them as read
        diesel
            ::update(dsl::emails.filter(dsl::id.eq_any(&email_ids)))
            .set(dsl::is_read.eq(true))
            .execute(conn)
            .map_err(|err| {
                eprintln!("Error marking emails as read: {}", err);
                err
            })?;

        // After marking emails as read, recalculate category unread counts
        // This ensures the counts reflect thread-based counting
        recalculate_category_unread_counts_internal(conn)?;

        Ok(())
    })
}

pub fn mark_emails_as_unread(email_ids: Vec<String>) -> Result<(), Error> {
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        // Update emails to mark them as unread
        diesel
            ::update(dsl::emails.filter(dsl::id.eq_any(&email_ids)))
            .set(dsl::is_read.eq(false))
            .execute(conn)
            .map_err(|err| {
                eprintln!("Error marking emails as unread: {}", err);
                err
            })?;

        // After marking emails as unread, recalculate category unread counts
        // This ensures the counts reflect thread-based counting
        recalculate_category_unread_counts_internal(conn)?;

        Ok(())
    })
}
// pub fn count_emails_by_category() -> Result<Vec<(String, i64)>, Error> {
//     let connection = &mut establish_db_connection();

//     connection.transaction::<_, Error, _>(|conn| {
//         // Group emails by category and count each group
//         let email_counts = dsl::emails
//             .select((dsl::category()))
//             .group_by(dsl::category)
//             .load::<(String, i64)>(conn)
//             .map_err(|err| {
//                 eprintln!("Error counting emails by category: {}", err);
//                 err
//             })?;

//         Ok(email_counts)
//     })
// }

pub fn get_unique_senders() -> Result<Vec<Option<String>>, Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        // Select distinct sender addresses
        let senders = dsl::emails
            .select(dsl::main_domain)
            .distinct()
            .load::<Option<String>>(conn)
            .map_err(|err| {
                eprintln!("Error fetching unique senders: {}", err);
                err
            })?;

        Ok(senders)
    })
}

// pub fn count_starred_emails() -> Result<i64, Error> {
//     let connection = &mut establish_db_connection();

//     connection.transaction::<_, Error, _>(|conn| {
//         // Count emails where the starred flag is set
//         let starred_emails = dsl::emails
//             .filter(dsl::emails::starred.eq(true))
//             .count()
//             .get_result::<i64>(conn)
//             .map_err(|err| {
//                 eprintln!("Error counting starred emails: {}", err);
//                 err
//             })?;

//         Ok(starred_emails)
//     })
// }

pub fn count_emails_by_date_range(
    start_date: NaiveDateTime,
    end_date: NaiveDateTime
) -> Result<i64, Error> {
    let mut conn = get_pooled_connection(); // 🔁 same pattern as before

    conn.transaction::<_, _, _>(|conn| {
        let emails_in_range = dsl::emails
            .filter(dsl::date.between(start_date, end_date))
            .count()
            .get_result::<i64>(conn)?;

        Ok(emails_in_range)
    })
    // let connection = &mut establish_db_connection();

    // connection.transaction::<_, Error, _>(|conn| {
    //     // Count emails within the specified date range
    //     let emails_in_range = dsl::emails
    //         .filter(dsl::date.between(start_date, end_date))
    //         .count()
    //         .get_result::<i64>(conn)
    //         .map_err(|err| {
    //             eprintln!("Error counting emails by date range: {}", err);
    //             err
    //         })?;

    //     Ok(emails_in_range)
    // })
}

// pub fn get_today_categories() -> Result<Vec<String>, diesel::result::Error> {
//     let mut conn = get_pooled_connection();
//     let fixed_categories = vec![
//         "needs_reply",
//         "waiting_response",
//         "fyi_read_later",
//         "delegated_handled",
//         "calendar_scheduling",
//         "clients_vips",
//         "ads_newsletters",
//     ];

//     let today: NaiveDate = chrono::Local::now().date_naive();

//     // Fetch distinct storage_location where not null and date is today
//     let categories: Vec<String> = dsl::emails
//         .select(dsl::storage_location)
//         .filter(dsl::storage_location.is_not_null())
//         // .filter(date(dsl::date).eq(today))
//         .distinct()
//         .load::<Option<String>>(&mut conn)?
//         .into_iter()
//         .filter_map(|opt| opt) // remove nulls
//         .collect::<HashSet<_>>() // deduplicate
//         .into_iter()
//         .collect();
//     println!("📦 Today’s non-null storage categories: {:?}", categories);

//     Ok(categories)
// }

pub fn cleanup_old_emails_from_today_categories() -> Result<usize, diesel::result::Error> {
    let mut conn = get_pooled_connection();

    let fixed_categories = vec![
        "needs_reply",
        "waiting_response",
        "fyi_read_later",
        "delegated_handled",
        "calendar_scheduling",
        "clients_vips",
        "ads_newsletters"
    ];

    // Calculate cutoff date (5 days ago)
    let now = Utc::now().naive_utc();
    let five_days_ago = now - chrono::Duration::days(5);

    conn.transaction::<_, Error, _>(|conn| {
        let mut total_updated = 0;

        for category in &fixed_categories {
            // Find emails in this category that are older than 5 days and NOT snoozed
            let old_email_ids: Vec<String> = dsl::emails
                .select(dsl::id)
                .filter(dsl::storage_location.eq(category))
                .filter(dsl::date.lt(five_days_ago))
                // Exclude snoozed emails - don't remove them from today's categories
                .filter(
                    dsl::id.ne_all(
                        email_logs::table
                            .select(email_logs::email_id)
                            .filter(email_logs::snoozed_until.gt(now))
                            .filter(
                                email_logs::resurfaced
                                    .eq(false)
                                    .or(email_logs::resurfaced.is_null())
                            )
                    )
                )
                .load::<String>(conn)?;

            if !old_email_ids.is_empty() {
                // Update storage_location to null for these emails
                let updated_count = diesel
                    ::update(dsl::emails.filter(dsl::id.eq_any(&old_email_ids)))
                    .set(dsl::storage_location.eq::<Option<String>>(None))
                    .execute(conn)?;

                total_updated += updated_count;
                println!("🧹 Cleaned up {} old emails from category '{}'", updated_count, category);
            }
        }

        println!("🧹 Total emails removed from today's categories: {}", total_updated);
        Ok(total_updated)
    })
}

pub fn get_today_categories() -> Result<Vec<EmailCategory>, diesel::result::Error> {
    let mut conn = get_pooled_connection();

    let fixed_categories = vec![
        "needs_reply",
        "waiting_response",
        "fyi_read_later",
        "delegated_handled",
        "calendar_scheduling",
        "clients_vips",
        "ads_newsletters"
    ];

    // Fetch distinct storage_location where not null
    let category_ids: Vec<String> = dsl::emails
        .select(dsl::storage_location)
        .filter(dsl::storage_location.is_not_null())
        .distinct()
        .load::<Option<String>>(&mut conn)?
        .into_iter()
        .filter_map(|opt| opt) // remove nulls
        .collect::<HashSet<_>>() // deduplicate
        .into_iter()
        .collect();

    println!("\u{1F4E6} Today’s non-null storage categories: {:?}", category_ids);

    // Map to detailed EmailCategory list
    let results: Vec<EmailCategory> = category_ids
        .into_iter()
        .filter_map(|category_id| {
            if fixed_categories.contains(&category_id.as_str()) {
                Some(EmailCategory {
                    id: Uuid::new_v4().to_string(),
                    category_id: category_id.clone(),
                    child_full_domains: Some(category_id.clone()),
                    name: Some(humanize_category_name(&category_id)),
                    sender_company: Some(humanize_category_name(&category_id)),
                    sender_domain: Some(humanize_category_name(&category_id)),
                    avatar_link: Some(humanize_category_name(&category_id)),
                    created_at: Utc::now().naive_utc(),
                    tags: Some(humanize_category_name(&category_id)),
                    latest_email_at: Some(Utc::now().naive_utc()),
                    description: Some(generate_description(&category_id)),
                    priority: Some(get_priority(&category_id)),
                    color: Some(get_color(&category_id)),
                    unread_count: Some(10),
                    last_accessed_at: None,
                    is_archived: Some(false),
                    custom_data: Some(humanize_category_name(&category_id)),
                    parent_category_id: Some(humanize_category_name(&category_id)),
                    class_category_id: Some(String::new()),
                    visibility: Some("visible".to_string()),
                    is_synced: Some(true),
                })
            } else {
                None
            }
        })
        .collect();

    Ok(results)
}

fn humanize_category_name(id: &str) -> String {
    (
        match id {
            "needs_reply" => "Needs Reply",
            "waiting_response" => "Waiting for Response",
            "fyi_read_later" => "FYI Read Later",
            "delegated_handled" => "Delegated Handled",
            "calendar_scheduling" => "Calendar Scheduling",
            "clients_vips" => "Clients VIPs",
            "ads_newsletters" => "Ads Newsletters",
            _ => id,
        }
    ).to_string()
}

fn generate_description(id: &str) -> String {
    (
        match id {
            "needs_reply" => "Emails that need your immediate reply",
            "waiting_response" => "Emails awaiting reply from others",
            "fyi_read_later" => "Informational emails you might read later",
            "delegated_handled" => "Emails handled or delegated to others",
            "calendar_scheduling" => "Meeting invites and scheduling emails",
            "clients_vips" => "Important clients, VIPs, and partners emails",
            "ads_newsletters" => "Promotions, ads, and newsletters",
            _ => "General category",
        }
    ).to_string()
}

fn get_priority(id: &str) -> i32 {
    match id {
        "clients_vips" => 0,
        "needs_reply" => 1,
        "waiting_response" => 2,
        "calendar_scheduling" => 3,
        "fyi_read_later" => 4,
        "delegated_handled" => 5,
        "ads_newsletters" => 6,
        _ => 9,
    }
}

fn get_color(id: &str) -> String {
    (
        match id {
            "needs_reply" => "#FF4C4C",
            "waiting_response" => "#FFA500",
            "fyi_read_later" => "#5B9BD5",
            "delegated_handled" => "#7FBA00",
            "calendar_scheduling" => "#FFB6C1",
            "clients_vips" => "#FFD700",
            "ads_newsletters" => "#CCCCCC",
            _ => "#DDDDDD",
        }
    ).to_string()
}

pub async fn get_fresh_access_token(
    app_data: tauri::State<'_, Arc<RwLock<AppData>>>
) -> Result<String, String> {
    check_and_refresh_token(&app_data).await.map_err(|e|
        format!("Failed to refresh token: {}", e)
    )?;

    // Step 1: Acquire read lock to retrieve necessary data
    let access_token = {
        let app_data_arc = app_data.read().await; // Acquire read lock for `app_data`
        let user_data = app_data_arc.user_data.read().await; // Acquire read lock for `user_data`

        user_data.access_token.as_ref().map(|t| t.secret().clone())
    }; // Read locks are released here

    // Step 2: Use or log the retrieved data as needed
    println!("Refreshed Access Token: {}", access_token.clone().unwrap());

    // println!("this inside js2rs \n {:#?}", user_data_json_data);

    return Ok(access_token.unwrap());
}

pub fn get_last_received_email_in_thread(
    thread: String
) -> Result<Option<Email>, diesel::result::Error> {
    let connection = &mut get_pooled_connection(); // Replace with actual SQLite connection function

    let result = dsl::emails
        .filter(dsl::thread_id.eq(thread))
        .order(dsl::date.desc()) // Latest first
        .first::<Email>(connection)
        .optional()?; // Return Option<Email>

    Ok(result)
}

pub fn get_all_emails_in_thread(thread: String) -> Result<Vec<Email>, diesel::result::Error> {
    let connection = &mut get_pooled_connection();

    let results = dsl::emails
        .filter(dsl::thread_id.eq(thread))
        .order(dsl::date.asc()) // ASC = same as SQL query
        .load::<Email>(connection)?;

    Ok(results)
}
pub fn is_last_received_email(
    email_id: &str,
    thread_id_str: &str
) -> Result<bool, diesel::result::Error> {
    let connection = &mut get_pooled_connection();

    // Get the latest *received* email in the thread
    let latest_received = dsl::emails
        .filter(dsl::thread_id.eq(thread_id_str))
        .order(dsl::date.desc())
        .first::<Email>(connection)
        .optional()?;

    match latest_received {
        Some(email) => Ok(email.id == email_id),
        None => Ok(false), // No received emails in thread
    }
}
