use reqwest::blocking::Client;
use reqwest::header::{AUTHORIZATION, CONTENT_TYPE};
use std::error::Error;
// mod main::AppData;

// #[tauri::command(async)]
// async fn delete_email_from_gmail(
//     app_data: tauri::State<'_, AppData>,
//     email_id: String,
//     access_token: String,
// ) -> Result<(), Box<dyn Error>> {
//     let client = Client::new();
//     let mut user_data = app_data.user_data.lock().await.clone();

//     // The Gmail API endpoint to delete an email
//     let url = format!(
//         "https://gmail.googleapis.com/gmail/v1/users/me/messages/{}",
//         email_id
//     );

//     let access_tokent = &user_data.refresh_token.as_ref().unwrap().secret();

//     // Make the DELETE request to Gmail API
//     let response = client
//         .delete(&url)
//         .header(AUTHORIZATION, format!("Bearer {}", access_tokent))
//         .header(CONTENT_TYPE, "application/json")
//         .send()?;

//     if response.status().is_success() {
//         println!("Successfully deleted email with ID: {}", email_id);
//         Ok(())
//     } else {
//         eprintln!(
//             "Failed to delete email with ID: {}. Status: {:?}",
//             email_id,
//             response.status()
//         );
//         Err(Box::new(std::io::Error::new(
//             std::io::ErrorKind::Other,
//             "Failed to delete email",
//         )))
//     }
// }
