use std::sync::Arc;

use tauri::{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, State, WebviewWindow };
use tokio::{ sync::RwLock, time::{ sleep, Duration } };

use crate::models::app_data::AppData;

pub fn close_window_in(window: WebviewWindow, time: u64) {
    tokio::spawn(async move {
        println!("Waiting 5 seconds...");
        sleep(Duration::from_secs(time)).await;
        window.close();
        println!("Done waiting. Do something now.");
    });
}

pub async fn open_main_window(handle: &AppHandle) {
    let result = tauri::WebviewWindowBuilder
        ::new(handle, "main", tauri::WebviewUrl::App("/emails".into()))
        .title("Oway")
        .fullscreen(false)
        .decorations(false)
        .inner_size(1200.0, 800.0)
        .transparent(true)
        .center()
        .build();
    if let Err(e) = result {
        println!("Failed to open main window: {:?}", e);
    }
}
#[tauri::command]
pub fn start_main_window(handle: AppHandle) {
    let window = handle.get_webview_window("auth");
    tauri::async_runtime::spawn(async move {
        open_main_window(&handle).await;
    });
    if let Some(auth) = window {
        close_window_in(auth, 2);
    }
}

pub async fn open_auth_window(handle: &AppHandle) {
    let result = tauri::WebviewWindowBuilder
        ::new(handle, "auth", tauri::WebviewUrl::App("/auth/signin".into()))
        .title("Oway")
        .fullscreen(false)
        .decorations(false)
        .inner_size(800.0, 520.0)
        .transparent(true)
        .center()
        .build();
    if let Err(e) = result {
        println!("Failed to open auth window: {:?}", e);
    }
}

#[tauri::command]
pub fn start_auth_window(handle: AppHandle) {
    let window = handle.get_webview_window("main");
    tauri::async_runtime::spawn(async move {
        open_auth_window(&handle).await;
    });
    if let Some(main) = window {
        close_window_in(main, 2);
    }
}

pub async fn open_settings_window(handle: &AppHandle) {
    let result = tauri::WebviewWindowBuilder
        ::new(handle, "settings", tauri::WebviewUrl::App("/settings".into()))
        .title("Oway")
        .fullscreen(false)
        .decorations(false)
        .resizable(false)
        .inner_size(600.0, 820.0)
        .transparent(true)
        .center()
        .build();
    if let Err(e) = result {
        println!("Failed to open settings window: {:?}", e);
    }
}

#[tauri::command]
pub fn start_settings_window(handle: AppHandle) {
    tauri::async_runtime::spawn(async move {
        open_settings_window(&handle).await;
    });
}

#[tauri::command]
pub async fn setup(handle: AppHandle) -> Result<(), Box<dyn std::error::Error>> {
    let (logged_in_status, user_data_snapshot, user_name) = {
        let app_data = handle.state::<Arc<RwLock<AppData>>>();
        let app_data_arc = app_data.read().await; // Acquire read lock on `app_data`

        let mut logged_in = app_data_arc.logged_in.read().await;
        let user_data = app_data_arc.user_data.read().await; // Read `user_data`

        let user_name = user_data.user.name.clone();

        // Clone necessary data to minimize lock hold time
        let user_data_snapshot = user_data.clone();

        (*logged_in, user_data_snapshot, user_name)
    };
    println!("Logged in: {}", logged_in_status);
    println!("User name: {}", user_name);
    println!("User data: {:?}", user_data_snapshot);
    if !user_data_snapshot.user.id.is_empty() && !user_data_snapshot.user.email.is_empty() {
        open_main_window(&handle).await;
        println!("🚀 User logged in successfully, starting cron manager...");
        let cron_control =
            handle.state::<std::sync::Arc<crate::commands::c_mamanger_command::CronControl>>();
        if
            let Err(e) = crate::commands::c_mamanger_command::start_cm_cron(
                handle.clone(),
                cron_control
            )
        {
            eprintln!("❌ Failed to start cron manager after login: {:?}", e);
        } else {
            println!("✅ Cron manager started successfully after login");
        }
    } else {
        open_auth_window(&handle).await;
    }
    Ok(())
}
