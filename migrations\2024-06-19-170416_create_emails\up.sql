CREATE TABLE emails (
    id VARCHAR(50) PRIMARY KEY NOT NULL,  -- Unique email ID
    subject TEXT NOT NULL,  -- Email subject
    snippet TEXT NOT NULL,  -- Snippet or preview of the email content
    "from" TEXT NOT NULL,  -- Sender's email address
    "to" TEXT NOT NULL,  -- Recipient's email addresses (CSV)
    cc TEXT,  -- CC recipients (CSV)
    bcc TEXT,  -- BCC recipients (CSV)
    date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- Email received or sent timestamp
    category TEXT,  -- Category of the email (e.g., Primary, Promotions)
    labels TEXT,  -- Labels associated with the email (CSV)
    attachments TEXT,  -- Attachment filenames (CSV)
    attachment_types TEXT,  -- Types of attachments (CSV)
    total_attachment_size INT,  -- Total size of all attachments in bytes
    metadata_headers TEXT,  -- Raw email metadata headers
    email_body_url TEXT,  -- URL to fetch the full email body
    is_read BO<PERSON>EAN DEFAULT FALSE,  -- Indicates if the email has been read
    thread_id VARCHAR(50),  -- ID of the conversation thread this email belongs to
    thread_summary TEXT,  -- Summary of the thread
    priority TEXT,  -- Priority of the email (e.g., High, Low, Normal)
    urgency_score INT,  -- Urgency score calculated based on email content
    sentiment TEXT,  -- Sentiment analysis result
    actionable_items TEXT,  -- Actionable items identified in the email content (JSON)
    language TEXT,  -- Detected language of the email
    phishing_risk TEXT,  -- Assessed phishing risk level (e.g., Low, Medium, High)
    sender_reputation TEXT,  -- Sender's reputation level (e.g., Trusted, Suspicious)
    full_domain TEXT,  -- Full domain of the sender (e.g., info.twilio.com)
    main_domain TEXT,  -- Main domain extracted (e.g., twilio.com)
    storage_location TEXT,  -- Indicates where the email data is stored (e.g., cloud bucket)
    is_flagged BOOLEAN DEFAULT FALSE,  -- Indicates if the email is marked for follow-up
    process_flag BOOLEAN DEFAULT FALSE,  -- Indicates if the email needs further processing
    email_type TEXT,  -- Type of email (e.g., Incoming, Outgoing, Reply, Forward)
    is_thread_root BOOLEAN DEFAULT FALSE,  -- Indicates if this is the root email of a thread
    received_as TEXT,  -- Indicates how the email was received (e.g., to, cc, bcc)
    parent_email_id VARCHAR(50),  -- Reference to the parent email in the thread

    -- Enhanced Automation Features
    read_receipt_url TEXT,  -- URL to track if the email was opened
    reply_suggestion TEXT,  -- AI-generated reply suggestions (JSON)
    follow_up_date TIMESTAMP,  -- Suggested follow-up date for the email
    meeting_proposed BOOLEAN DEFAULT FALSE,  -- Indicates if the email proposes a meeting
    meeting_link TEXT,  -- Link to the meeting if scheduled
    is_delegated BOOLEAN DEFAULT FALSE,  -- Indicates if the email was delegated to another user
    task_status TEXT,  -- Status of actionable tasks (e.g., Pending, Completed)
    auto_reply_sent BOOLEAN DEFAULT FALSE,  -- Indicates if an auto-reply was sent
    flagged_keywords TEXT,  -- Important keywords flagged in the email (CSV)
    attachments_downloaded BOOLEAN DEFAULT FALSE,  -- Indicates if attachments were downloaded
    shared_with TEXT,  -- List of users or groups the email is shared with (CSV)
    analytics_score INT,  -- Custom score for analyzing email performance
    response_time INT,  -- Time taken to respond to the email (in seconds)
    ai_generated BOOLEAN DEFAULT FALSE,  -- Indicates if the email content was AI-generated
    is_send BOOLEAN DEFAULT FALSE,  -- Indicates if the email content was AI-generated
    source_app TEXT DEFAULT 'email',  -- Source application (e.g., email, chat, notifications)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- Record creation timestamp
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP  -- Record update timestamp
);