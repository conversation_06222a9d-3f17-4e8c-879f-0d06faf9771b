.sx__calendar-wrapper ul,
.sx__date-picker-wrapper ul,
.sx__date-picker-popup ul {
  list-style: none;
  padding: 0;
}
.sx__calendar-wrapper input,
.sx__calendar-wrapper button,
.sx__date-picker-wrapper input,
.sx__date-picker-wrapper button,
.sx__date-picker-popup input,
.sx__date-picker-popup button {
  font-family: inherit;
  outline: none;
}
.sx__calendar-wrapper button,
.sx__date-picker-wrapper button,
.sx__date-picker-popup button {
  background-color: inherit;
  outline: 0;
  border: none;
  cursor: pointer;
}

:root {
  --sx-color-primary: #6750a4;
  --sx-color-on-primary: #fff;
  --sx-color-primary-container: #eaddff;
  --sx-color-on-primary-container: #21005e;
  --sx-color-secondary: #625b71;
  --sx-color-on-secondary: #fff;
  --sx-color-secondary-container: #e8def8;
  --sx-color-on-secondary-container: #1e192b;
  --sx-color-tertiary: #7d5260;
  --sx-color-on-tertiary: #fff;
  --sx-color-tertiary-container: #ffd8e4;
  --sx-color-on-tertiary-container: #370b1e;
  --sx-color-surface: #fef7ff;
  --sx-color-surface-dim: #ded8e1;
  --sx-color-surface-bright: #fef7ff;
  --sx-color-on-surface: #1c1b1f;
  --sx-color-surface-container: #f3edf7;
  --sx-color-surface-container-low: #f7f2fa;
  --sx-color-surface-container-high: #ece6f0;
  --sx-color-background: #922e2e;
  --sx-color-on-background: #1c1b1f;
  --sx-color-outline: #79747e;
  --sx-color-outline-variant: #c4c7c5;
  --sx-color-shadow: #000;
  --sx-color-surface-tint: #6750a4;
  --sx-color-neutral: var(--sx-color-outline);
  --sx-color-neutral-variant: var(--sx-color-outline-variant);
  --sx-internal-color-gray-ripple-background: #e0e0e0;
  --sx-internal-color-light-gray: #fafafa;
  --sx-internal-color-text: #000;
}

.is-dark {
  --sx-color-primary: #d0bcff;
  --sx-color-on-primary: #371e73;
  --sx-color-primary-container: #4f378b;
  --sx-color-on-primary-container: #eaddff;
  --sx-color-secondary: #ccc2dc;
  --sx-color-on-secondary: #332d41;
  --sx-color-secondary-container: #4a4458;
  --sx-color-on-secondary-container: #e8def8;
  --sx-color-tertiary: #efb8c8;
  --sx-color-on-tertiary: #492532;
  --sx-color-tertiary-container: #633b48;
  --sx-color-on-tertiary-container: #ffd8e4;
  --sx-color-surface: #141218;
  --sx-color-surface-dim: #141218;
  --sx-color-surface-bright: #3b383e;
  --sx-color-on-surface: #e6e1e5;
  --sx-color-surface-container: #211f26;
  --sx-color-surface-container-low: #1d1b20;
  --sx-color-surface-container-high: #2b2930;
  --sx-color-background: #5f3da4;
  --sx-color-on-background: #e6e1e5;
  --sx-color-outline: #938f99;
  --sx-color-outline-variant: #444746;
  --sx-color-shadow: #000;
  --sx-color-surface-tint: #d0bcff;
  --sx-internal-color-text: #fff;
}

:root {
  --sx-spacing-padding1: 4px;
  --sx-spacing-padding2: 8px;
  --sx-spacing-padding3: 12px;
  --sx-spacing-padding4: 16px;
  --sx-spacing-padding6: 24px;
  --sx-spacing-modal-padding: 16px;
}

:root {
  --sx-box-shadow-level3: 0 3px 6px 0 rgb(0 0 0 / 16%),
    0 3px 6px 0 rgb(0 0 0 / 23%);
  --sx-rounding-extra-small: 4px;
  --sx-rounding-small: 8px;
  --sx-rounding-extra-large: 28px;
  --sx-border: 1px solid var(--sx-color-outline-variant);
}

.is-dark {
  --sx-border: 1px solid var(--sx-color-outline-variant);
}

:root {
  --sx-font-small: 0.875rem;
  --sx-font-extra-small: 0.75rem;
  --sx-font-large: 1.125rem;
  --sx-font-extra-large: 1.25rem;
}

@keyframes ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 0.16;
  }
  40% {
    width: 100px;
    height: 100px;
    opacity: 0.08;
  }
  100% {
    width: 150px;
    height: 150px;
    opacity: 0;
  }
}
.sx__ripple {
  position: relative;
  overflow: hidden;
}
.sx__ripple::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  background-color: currentcolor;
  opacity: 0.1;
  visibility: hidden;
  z-index: 2;
}
.sx__ripple:active::before {
  visibility: visible;
}
.sx__ripple:not(:active)::before {
  animation: ripple 0.75s cubic-bezier(0, 0.1, 0.8, 1);
  transition: visibility 0.75s step-end;
}

@keyframes ripple-wide {
  0% {
    width: 0;
    height: 0;
    opacity: 0.16;
  }
  40% {
    width: 300px;
    height: 100px;
    opacity: 0.08;
  }
  100% {
    width: 450px;
    height: 150px;
    opacity: 0;
  }
}
.sx__ripple--wide {
  position: relative;
  overflow: hidden;
}
.sx__ripple--wide::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  background-color: currentcolor;
  opacity: 0.1;
  visibility: hidden;
  z-index: 2;
}
.sx__ripple--wide:active::before {
  visibility: visible;
}
.sx__ripple--wide::before {
  border-radius: var(--sx-rounding-small);
}
.sx__ripple--wide:not(:active)::before {
  animation: ripple-wide 0.75s cubic-bezier(0, 0.1, 0.8, 1);
  transition: visibility 0.75s step-end;
}

.sx__chevron-wrapper {
  position: relative;
  border-radius: 50%;
  min-height: 48px;
  min-width: 48px;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
  font-size: 0;
}
.sx__chevron-wrapper:active {
  background-color: var(--sx-internal-color-gray-ripple-background);
}
.sx__chevron-wrapper:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
.sx__chevron-wrapper:hover, .sx__chevron-wrapper:focus {
  background-color: var(--sx-color-surface-dim);
}
.is-dark .sx__chevron-wrapper:hover, .is-dark .sx__chevron-wrapper:focus {
  background-color: var(--sx-color-surface-container-high);
}
.sx__chevron-wrapper .sx__chevron {
  position: absolute;
  top: 50%;
  width: 0.6rem;
  height: 0.6rem;
  border-width: 0.2rem 0.2rem 0 0;
  border-style: solid;
  border-color: var(--sx-internal-color-text);
}

.sx__chevron--previous {
  left: calc(50% + 0.125rem);
  transform: translate(-50%, -50%) rotate(225deg);
}

.sx__chevron--next {
  left: calc(50% - 0.125rem);
  transform: translate(-50%, -50%) rotate(45deg);
}

.sx__date-picker-wrapper {
  position: relative;
  color: var(--sx-color-on-background);
  width: fit-content;
}
.sx__date-picker-wrapper.has-full-width {
  width: 100%;
}
.sx__date-picker-wrapper.is-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.sx__date-picker-wrapper * {
  color: var(--sx-color-on-background);
  box-sizing: border-box;
}

.sx__date-input-wrapper {
  position: relative;
}

.sx__date-input-chevron-wrapper {
  position: absolute;
  top: 50%;
  right: 1rem;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  padding: 0;
  transition: transform 0.2s ease-in-out;
}
.sx__date-input-chevron-wrapper:focus {
  border: 2px solid var(--sx-color-primary);
}
.is-disabled .sx__date-input-chevron-wrapper {
  pointer-events: none;
  cursor: not-allowed;
}
.sx__date-input--active .sx__date-input-chevron-wrapper {
  transform: translateY(-50%) rotate(180deg);
}

.sx__date-input-chevron {
  width: 1rem;
  height: 1rem;
  pointer-events: none;
}

.sx__date-input {
  font-size: 1rem;
  padding: var(--sx-spacing-padding4);
  border: var(--sx-border);
  border-radius: var(--sx-rounding-extra-small);
  cursor: pointer;
  /* background-color: var(--sx-color-background); */
  background-color: transparent;
  width: 100%;
}
.is-disabled .sx__date-input {
  pointer-events: none;
}
.sx__date-input--active .sx__date-input {
  border-color: var(--sx-color-primary);
  outline: 1px solid var(--sx-color-primary);
}

.sx__date-input-label {
  position: absolute;
  top: 0;
  left: 21px;
  padding: 0 var(--sx-spacing-padding1);
  background-color: var(--sx-color-background);
  font-size: 0.75rem;
  color: var(--sx-color-neutral);
  line-height: 1rem;
  transform: translateY(-50%) translateX(-0.5rem);
  transition: transform 0.2s ease-in-out;
  pointer-events: none;
}
.sx__date-input--active .sx__date-input-label {
  color: var(--sx-color-primary);
}
.is-dark .sx__date-input-label {
  display: none;
}

.sx__date-picker-popup {
  position: absolute;
  height: fit-content;
  z-index: 1;
  top: calc(100% + 1px);
  width: 20.75rem;
  max-width: 500px;
  max-height: 400px;
  overflow: scroll;
  box-shadow: var(--sx-box-shadow-level3);
  padding: var(--sx-spacing-modal-padding);
  background-color: var(--sx-color-background);
  color: var(--sx-internal-color-text);
}
.sx__date-picker-popup.is-dark {
  background-color: var(--sx-color-surface-container-high);
}
.sx__date-picker-popup.bottom-end {
  left: auto;
  right: 0;
  transform: translateX(0);
}
.sx__date-picker-popup.top-start {
  inset: auto auto calc(100% + 1rem) 0;
  transform: translateX(0);
}
.sx__date-picker-popup.top-end {
  inset: auto 0 calc(100% + 1rem) auto;
  transform: translateX(0);
}

.sx__date-picker__years-view {
  margin: 0;
}

.sx__date-picker__years-accordion__expand-button {
  width: 100%;
  border-radius: 0;
  background-color: transparent;
  font-size: 1rem;
  padding: 1em;
  transition: background-color 0.2s ease-in-out;
  color: var(--sx-internal-color-text);
}
.sx__is-expanded .sx__date-picker__years-accordion__expand-button {
  background-color: var(--sx-color-surface-container);
}
.sx__date-picker__years-accordion__expand-button:hover {
  background-color: var(--sx-color-surface-dim);
}
.sx__date-picker__years-accordion__expand-button:active {
  background-color: var(--sx-internal-color-gray-ripple-background);
}

.sx__date-picker__years-view-accordion__panel {
  display: flex;
  flex-wrap: wrap;
}

.sx__date-picker__years-view-accordion__month {
  flex: 1 0 33.3333%;
  background-color: transparent;
  border: 0;
  font-size: 0.9rem;
  padding: 0.5em 0;
  border-radius: 25px;
  color: var(--sx-internal-color-text);
}
.sx__date-picker__years-view-accordion__month:hover {
  background-color: var(--sx-color-primary);
  color: var(--sx-color-on-primary);
}

.sx__date-picker__day-names {
  display: flex;
  width: 100%;
  justify-content: space-evenly;
  margin-bottom: 0.5em;
}
.sx__date-picker__day-names .sx__date-picker__day,
.sx__date-picker__day-names .sx__date-picker__day-name {
  flex: 1;
  text-align: center;
}

.sx__date-picker__day-name {
  font-weight: 700;
  color: var(--sx-color-neutral-variant);
}

.sx__date-picker__month-view-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1em;
}
.sx__date-picker__month-view-header .sx__chevron-wrapper:hover {
  background-color: var(--sx-color-surface-dim);
}

.sx__date-picker__month-view-header__month-year {
  font-size: 1.5rem;
  font-weight: 300;
  color: var(--sx-internal-color-text);
}
.sx__date-picker__month-view-header__month-year:hover {
  color: var(--sx-color-primary);
  text-decoration: underline;
}

.sx__date-picker__week {
  display: flex;
  width: 100%;
  justify-content: space-evenly;
  margin-bottom: 0.5em;
}
.sx__date-picker__week .sx__date-picker__day,
.sx__date-picker__week .sx__date-picker__day-name {
  flex: 1;
  text-align: center;
}

.sx__date-picker__day {
  background-color: transparent;
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  color: var(--sx-internal-color-text);
}
.sx__date-picker__day:hover {
  background-color: var(--sx-color-surface-dim);
}
.sx__date-picker__day:focus {
  outline-offset: -2px;
  outline: 2px solid var(--sx-color-primary);
}
.sx__date-picker__day:disabled {
  color: var(--sx-color-neutral-variant);
  cursor: not-allowed;
}
.sx__date-picker__day.is-leading-or-trailing {
  color: var(--sx-color-neutral-variant);
}
.sx__date-picker__day.sx__date-picker__day--selected {
  background-color: var(--sx-color-primary-container);
  color: var(--sx-color-on-primary-container);
}
.sx__date-picker__day.sx__date-picker__day--today {
  background-color: var(--sx-color-primary);
  color: var(--sx-color-on-primary);
}

.sx__calendar-wrapper ul,
.sx__date-picker-wrapper ul,
.sx__date-picker-popup ul {
  list-style: none;
  padding: 0;
}
.sx__calendar-wrapper input,
.sx__calendar-wrapper button,
.sx__date-picker-wrapper input,
.sx__date-picker-wrapper button,
.sx__date-picker-popup input,
.sx__date-picker-popup button {
  font-family: inherit;
  outline: none;
}
.sx__calendar-wrapper button,
.sx__date-picker-wrapper button,
.sx__date-picker-popup button {
  background-color: inherit;
  outline: 0;
  border: none;
  cursor: pointer;
}

:root {
  --sx-color-primary: #6750a4;
  --sx-color-on-primary: #fff;
  --sx-color-primary-container: #eaddff;
  --sx-color-on-primary-container: #21005e;
  --sx-color-secondary: #625b71;
  --sx-color-on-secondary: #fff;
  --sx-color-secondary-container: #e8def8;
  --sx-color-on-secondary-container: #1e192b;
  --sx-color-tertiary: #7d5260;
  --sx-color-on-tertiary: #fff;
  --sx-color-tertiary-container: #ffd8e4;
  --sx-color-on-tertiary-container: #370b1e;
  --sx-color-surface: #fef7ff;
  --sx-color-surface-dim: #ded8e1;
  --sx-color-surface-bright: #fef7ff;
  --sx-color-on-surface: #1c1b1f;
  --sx-color-surface-container: #f3edf7;
  --sx-color-surface-container-low: #f7f2fa;
  --sx-color-surface-container-high: #ece6f0;
  --sx-color-background: #fff;
  --sx-color-on-background: #1c1b1f;
  --sx-color-outline: #79747e;
  --sx-color-outline-variant: #c4c7c5;
  --sx-color-shadow: #000;
  --sx-color-surface-tint: #6750a4;
  --sx-color-neutral: var(--sx-color-outline);
  --sx-color-neutral-variant: var(--sx-color-outline-variant);
  --sx-internal-color-gray-ripple-background: #e0e0e0;
  --sx-internal-color-light-gray: #fafafa;
  --sx-internal-color-text: #000;
}

.is-dark {
  --sx-color-primary: #d0bcff;
  --sx-color-on-primary: #371e73;
  --sx-color-primary-container: #4f378b;
  --sx-color-on-primary-container: #eaddff;
  --sx-color-secondary: #ccc2dc;
  --sx-color-on-secondary: #332d41;
  --sx-color-secondary-container: #4a4458;
  --sx-color-on-secondary-container: #e8def8;
  --sx-color-tertiary: #efb8c8;
  --sx-color-on-tertiary: #492532;
  --sx-color-tertiary-container: #633b48;
  --sx-color-on-tertiary-container: #ffd8e4;
  --sx-color-surface: #141218;
  --sx-color-surface-dim: #141218;
  --sx-color-surface-bright: #3b383e;
  --sx-color-on-surface: #e6e1e5;
  --sx-color-surface-container: #211f26;
  --sx-color-surface-container-low: #1d1b20;
  --sx-color-surface-container-high: #2b2930;
  --sx-color-background: #141218;
  --sx-color-on-background: #e6e1e5;
  --sx-color-outline: #938f99;
  --sx-color-outline-variant: #444746;
  --sx-color-shadow: #000;
  --sx-color-surface-tint: #d0bcff;
  --sx-internal-color-text: #fff;
}

:root {
  --sx-spacing-padding1: 4px;
  --sx-spacing-padding2: 8px;
  --sx-spacing-padding3: 12px;
  --sx-spacing-padding4: 16px;
  --sx-spacing-padding6: 24px;
  --sx-spacing-modal-padding: 16px;
}

:root {
  --sx-box-shadow-level3: 0 3px 6px 0 rgb(0 0 0 / 16%),
    0 3px 6px 0 rgb(0 0 0 / 23%);
  --sx-rounding-extra-small: 4px;
  --sx-rounding-small: 8px;
  --sx-rounding-extra-large: 28px;
  --sx-border: 1px solid var(--sx-color-outline-variant);
}

.is-dark {
  --sx-border: 1px solid var(--sx-color-outline-variant);
}

:root {
  --sx-font-small: 0.875rem;
  --sx-font-extra-small: 0.75rem;
  --sx-font-large: 1.125rem;
  --sx-font-extra-large: 1.25rem;
}

:root {
  --sx-calendar-header-input-font-size: clamp(12px, 0.875rem, 28px);
  --sx-calendar-header-popup-z-index: 3;
  --sx-calendar-week-grid-padding-left: 75px;
}

@keyframes ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 0.16;
  }
  40% {
    width: 100px;
    height: 100px;
    opacity: 0.08;
  }
  100% {
    width: 150px;
    height: 150px;
    opacity: 0;
  }
}
.sx__ripple {
  position: relative;
  overflow: hidden;
}
.sx__ripple::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  background-color: currentcolor;
  opacity: 0.1;
  visibility: hidden;
  z-index: 2;
}
.sx__ripple:active::before {
  visibility: visible;
}
.sx__ripple:not(:active)::before {
  animation: ripple 0.75s cubic-bezier(0, 0.1, 0.8, 1);
  transition: visibility 0.75s step-end;
}

@keyframes ripple-wide {
  0% {
    width: 0;
    height: 0;
    opacity: 0.16;
  }
  40% {
    width: 300px;
    height: 100px;
    opacity: 0.08;
  }
  100% {
    width: 450px;
    height: 150px;
    opacity: 0;
  }
}
.sx__ripple--wide {
  position: relative;
  overflow: hidden;
}
.sx__ripple--wide::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  background-color: currentcolor;
  opacity: 0.1;
  visibility: hidden;
  z-index: 2;
}
.sx__ripple--wide:active::before {
  visibility: visible;
}
.sx__ripple--wide::before {
  border-radius: var(--sx-rounding-small);
}
.sx__ripple--wide:not(:active)::before {
  animation: ripple-wide 0.75s cubic-bezier(0, 0.1, 0.8, 1);
  transition: visibility 0.75s step-end;
}

.sx__calendar-wrapper ul,
.sx__date-picker-wrapper ul,
.sx__date-picker-popup ul {
  list-style: none;
  padding: 0;
}
.sx__calendar-wrapper input,
.sx__calendar-wrapper button,
.sx__date-picker-wrapper input,
.sx__date-picker-wrapper button,
.sx__date-picker-popup input,
.sx__date-picker-popup button {
  font-family: inherit;
  outline: none;
}
.sx__calendar-wrapper button,
.sx__date-picker-wrapper button,
.sx__date-picker-popup button {
  background-color: inherit;
  outline: 0;
  border: none;
  cursor: pointer;
}

:root {
  --sx-color-primary: #6750a4;
  --sx-color-on-primary: #fff;
  --sx-color-primary-container: #eaddff;
  --sx-color-on-primary-container: #21005e;
  --sx-color-secondary: #625b71;
  --sx-color-on-secondary: #fff;
  --sx-color-secondary-container: #e8def8;
  --sx-color-on-secondary-container: #1e192b;
  --sx-color-tertiary: #7d5260;
  --sx-color-on-tertiary: #fff;
  --sx-color-tertiary-container: #ffd8e4;
  --sx-color-on-tertiary-container: #370b1e;
  --sx-color-surface: #fef7ff;
  --sx-color-surface-dim: #ded8e1;
  --sx-color-surface-bright: #fef7ff;
  --sx-color-on-surface: #1c1b1f;
  --sx-color-surface-container: #f3edf7;
  --sx-color-surface-container-low: #f7f2fa;
  --sx-color-surface-container-high: #ece6f0;
  --sx-color-background: #fff;
  --sx-color-on-background: #1c1b1f;
  --sx-color-outline: #79747e;
  --sx-color-outline-variant: #c4c7c5;
  --sx-color-shadow: #000;
  --sx-color-surface-tint: #6750a4;
  --sx-color-neutral: var(--sx-color-outline);
  --sx-color-neutral-variant: var(--sx-color-outline-variant);
  --sx-internal-color-gray-ripple-background: #e0e0e0;
  --sx-internal-color-light-gray: #fafafa;
  --sx-internal-color-text: #000;
}

.is-dark {
  --sx-color-primary: #d0bcff;
  --sx-color-on-primary: #371e73;
  --sx-color-primary-container: #4f378b;
  --sx-color-on-primary-container: #eaddff;
  --sx-color-secondary: #ccc2dc;
  --sx-color-on-secondary: #332d41;
  --sx-color-secondary-container: #4a4458;
  --sx-color-on-secondary-container: #e8def8;
  --sx-color-tertiary: #efb8c8;
  --sx-color-on-tertiary: #492532;
  --sx-color-tertiary-container: #633b48;
  --sx-color-on-tertiary-container: #ffd8e4;
  --sx-color-surface: #141218;
  --sx-color-surface-dim: #141218;
  --sx-color-surface-bright: #3b383e;
  --sx-color-on-surface: #e6e1e5;
  --sx-color-surface-container: #211f26;
  --sx-color-surface-container-low: #1d1b20;
  --sx-color-surface-container-high: #2b2930;
  --sx-color-background: #141218;
  --sx-color-on-background: #e6e1e5;
  --sx-color-outline: #938f99;
  --sx-color-outline-variant: #444746;
  --sx-color-shadow: #000;
  --sx-color-surface-tint: #d0bcff;
  --sx-internal-color-text: #fff;
}

:root {
  --sx-spacing-padding1: 4px;
  --sx-spacing-padding2: 8px;
  --sx-spacing-padding3: 12px;
  --sx-spacing-padding4: 16px;
  --sx-spacing-padding6: 24px;
  --sx-spacing-modal-padding: 16px;
}

:root {
  --sx-box-shadow-level3: 0 3px 6px 0 rgb(0 0 0 / 16%),
    0 3px 6px 0 rgb(0 0 0 / 23%);
  --sx-rounding-extra-small: 4px;
  --sx-rounding-small: 8px;
  --sx-rounding-extra-large: 28px;
  --sx-border: 1px solid var(--sx-color-outline-variant);
}

.is-dark {
  --sx-border: 1px solid var(--sx-color-outline-variant);
}

:root {
  --sx-font-small: 0.875rem;
  --sx-font-extra-small: 0.75rem;
  --sx-font-large: 1.125rem;
  --sx-font-extra-large: 1.25rem;
}

@keyframes ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 0.16;
  }
  40% {
    width: 100px;
    height: 100px;
    opacity: 0.08;
  }
  100% {
    width: 150px;
    height: 150px;
    opacity: 0;
  }
}
.sx__ripple {
  position: relative;
  overflow: hidden;
}
.sx__ripple::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  background-color: currentcolor;
  opacity: 0.1;
  visibility: hidden;
  z-index: 2;
}
.sx__ripple:active::before {
  visibility: visible;
}
.sx__ripple:not(:active)::before {
  animation: ripple 0.75s cubic-bezier(0, 0.1, 0.8, 1);
  transition: visibility 0.75s step-end;
}

@keyframes ripple-wide {
  0% {
    width: 0;
    height: 0;
    opacity: 0.16;
  }
  40% {
    width: 300px;
    height: 100px;
    opacity: 0.08;
  }
  100% {
    width: 450px;
    height: 150px;
    opacity: 0;
  }
}
.sx__ripple--wide {
  position: relative;
  overflow: hidden;
}
.sx__ripple--wide::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  background-color: currentcolor;
  opacity: 0.1;
  visibility: hidden;
  z-index: 2;
}
.sx__ripple--wide:active::before {
  visibility: visible;
}
.sx__ripple--wide::before {
  border-radius: var(--sx-rounding-small);
}
.sx__ripple--wide:not(:active)::before {
  animation: ripple-wide 0.75s cubic-bezier(0, 0.1, 0.8, 1);
  transition: visibility 0.75s step-end;
}

.sx__chevron-wrapper {
  position: relative;
  border-radius: 50%;
  min-height: 48px;
  min-width: 48px;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
  font-size: 0;
}
.sx__chevron-wrapper:active {
  background-color: var(--sx-internal-color-gray-ripple-background);
}
.sx__chevron-wrapper:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
.sx__chevron-wrapper:hover, .sx__chevron-wrapper:focus {
  background-color: var(--sx-color-surface-dim);
}
.is-dark .sx__chevron-wrapper:hover, .is-dark .sx__chevron-wrapper:focus {
  background-color: var(--sx-color-surface-container-high);
}
.sx__chevron-wrapper .sx__chevron {
  position: absolute;
  top: 50%;
  width: 0.6rem;
  height: 0.6rem;
  border-width: 0.2rem 0.2rem 0 0;
  border-style: solid;
  border-color: var(--sx-internal-color-text);
}

.sx__chevron--previous {
  left: calc(50% + 0.125rem);
  transform: translate(-50%, -50%) rotate(225deg);
}

.sx__chevron--next {
  left: calc(50% - 0.125rem);
  transform: translate(-50%, -50%) rotate(45deg);
}

.sx__date-picker-wrapper {
  position: relative;
  color: var(--sx-color-on-background);
  width: fit-content;
}
.sx__date-picker-wrapper.has-full-width {
  width: 100%;
}
.sx__date-picker-wrapper.is-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.sx__date-picker-wrapper * {
  color: var(--sx-color-on-background);
  box-sizing: border-box;
}

.sx__date-input-wrapper {
  position: relative;
}

.sx__date-input-chevron-wrapper {
  position: absolute;
  top: 50%;
  right: 1rem;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  padding: 0;
  transition: transform 0.2s ease-in-out;
}
.sx__date-input-chevron-wrapper:focus {
  border: 2px solid var(--sx-color-primary);
}
.is-disabled .sx__date-input-chevron-wrapper {
  pointer-events: none;
  cursor: not-allowed;
}
.sx__date-input--active .sx__date-input-chevron-wrapper {
  transform: translateY(-50%) rotate(180deg);
}

.sx__date-input-chevron {
  width: 1rem;
  height: 1rem;
  pointer-events: none;
}

.sx__date-input .sx__date-input {
  font-size: 1rem;
  padding: var(--sx-spacing-padding4);
  border: var(--sx-border);
  border-radius: var(--sx-rounding-extra-small);
  cursor: pointer;
  /* background-color: var(--sx-color-background); */
  background-color: transparent;
  width: 100%;
}
.is-disabled .sx__date-input {
  pointer-events: none;
}
.sx__date-input--active .sx__date-input {
  border-color: var(--sx-color-primary);
  outline: 1px solid var(--sx-color-primary);
}

.sx__date-input-label {
  position: absolute;
  top: 0;
  left: 21px;
  padding: 0 var(--sx-spacing-padding1);
  /* background-color: var(--sx-color-background); */
  background-color: transparent;
  font-weight: bold;
  font-size: 0.75rem;
  color: var(--sx-color-neutral);
  line-height: 1rem;
  transform: translateY(-50%) translateX(-0.5rem);
  transition: transform 0.2s ease-in-out;
  pointer-events: none;
}
.sx__date-input--active .sx__date-input-label {
  color: var(--sx-color-primary);
}
.is-dark .sx__date-input-label {
  display: none;
}

.sx__date-picker-popup {
  position: absolute;
  height: fit-content;
  z-index: 1;
  top: calc(100% + 1px);
  width: 20.75rem;
  max-width: 500px;
  max-height: 400px;
  overflow: scroll;
  box-shadow: var(--sx-box-shadow-level3);
  padding: var(--sx-spacing-modal-padding);
  background-color: var(--sx-color-background);
  color: var(--sx-internal-color-text);
}
.sx__date-picker-popup.is-dark {
  background-color: var(--sx-color-surface-container-high);
}
.sx__date-picker-popup.bottom-end {
  left: auto;
  right: 0;
  transform: translateX(0);
}
.sx__date-picker-popup.top-start {
  inset: auto auto calc(100% + 1rem) 0;
  transform: translateX(0);
}
.sx__date-picker-popup.top-end {
  inset: auto 0 calc(100% + 1rem) auto;
  transform: translateX(0);
}

.sx__date-picker__years-view {
  margin: 0;
}

.sx__date-picker__years-accordion__expand-button {
  width: 100%;
  border-radius: 0;
  background-color: transparent;
  font-size: 1rem;
  padding: 1em;
  transition: background-color 0.2s ease-in-out;
  color: var(--sx-internal-color-text);
}
.sx__is-expanded .sx__date-picker__years-accordion__expand-button {
  background-color: var(--sx-color-surface-container);
}
.sx__date-picker__years-accordion__expand-button:hover {
  background-color: var(--sx-color-surface-dim);
}
.sx__date-picker__years-accordion__expand-button:active {
  background-color: var(--sx-internal-color-gray-ripple-background);
}

.sx__date-picker__years-view-accordion__panel {
  display: flex;
  flex-wrap: wrap;
}

.sx__date-picker__years-view-accordion__month {
  flex: 1 0 33.3333%;
  background-color: transparent;
  border: 0;
  font-size: 0.9rem;
  padding: 0.5em 0;
  border-radius: 25px;
  color: var(--sx-internal-color-text);
}
.sx__date-picker__years-view-accordion__month:hover {
  background-color: var(--sx-color-primary);
  color: var(--sx-color-on-primary);
}

.sx__date-picker__day-names {
  display: flex;
  width: 100%;
  justify-content: space-evenly;
  margin-bottom: 0.5em;
}
.sx__date-picker__day-names .sx__date-picker__day,
.sx__date-picker__day-names .sx__date-picker__day-name {
  flex: 1;
  text-align: center;
}

.sx__date-picker__day-name {
  font-weight: 700;
  color: var(--sx-color-neutral-variant);
}

.sx__date-picker__month-view-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1em;
}
.sx__date-picker__month-view-header .sx__chevron-wrapper:hover {
  background-color: var(--sx-color-surface-dim);
}

.sx__date-picker__month-view-header__month-year {
  font-size: 1.5rem;
  font-weight: 300;
  color: var(--sx-internal-color-text);
}
.sx__date-picker__month-view-header__month-year:hover {
  color: var(--sx-color-primary);
  text-decoration: underline;
}

.sx__date-picker__week {
  display: flex;
  width: 100%;
  justify-content: space-evenly;
  margin-bottom: 0.5em;
}
.sx__date-picker__week .sx__date-picker__day,
.sx__date-picker__week .sx__date-picker__day-name {
  flex: 1;
  text-align: center;
}

.sx__date-picker__day {
  background-color: transparent;
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  color: var(--sx-internal-color-text);
}
.sx__date-picker__day:hover {
  background-color: var(--sx-color-surface-dim);
}
.sx__date-picker__day:focus {
  outline-offset: -2px;
  outline: 2px solid var(--sx-color-primary);
}
.sx__date-picker__day:disabled {
  color: var(--sx-color-neutral-variant);
  cursor: not-allowed;
}
.sx__date-picker__day.is-leading-or-trailing {
  color: var(--sx-color-neutral-variant);
}
.sx__date-picker__day.sx__date-picker__day--selected {
  background-color: var(--sx-color-primary-container);
  color: var(--sx-color-on-primary-container);
}
.sx__date-picker__day.sx__date-picker__day--today {
  background-color: var(--sx-color-primary);
  color: var(--sx-color-on-primary);
}

.sx__calendar-wrapper {
  height: 100%;
  display: flex;
  color: var(--sx-internal-color-text);
}
.sx__calendar-wrapper * {
  box-sizing: border-box;
}

.sx__calendar {
  position: relative;
  flex: 1;
  height: 100%;
  /* border: var(--sx-border); */
  /* border-radius: var(--sx-rounding-small); */
  display: flex;
  flex-flow: column;
  /* background-color: var(--sx-color-background); */
  overflow: hidden;
}

.sx__view-container {
  position: relative;
  flex: 1;
  overflow-y: auto;
  scroll-behavior: smooth;
}

.sx__slide-left {
  animation: slide-left 0.3s ease-out;
}

@keyframes slide-left {
  0% {
    transform: translateX(8%);
    filter: blur(0.25rem);
    opacity: 0.1;
  }
  100% {
    transform: translateX(0);
    filter: blur(0);
    opacity: 1;
  }
}
.sx__slide-right {
  animation: slide-right 0.3s ease-out;
}

@keyframes slide-right {
  0% {
    transform: translateX(-8%);
    filter: blur(0.25rem);
    opacity: 0.1;
  }
  100% {
    transform: translateX(0);
    filter: blur(0);
    opacity: 1;
  }
}
.sx__calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--sx-spacing-padding4);
  gap: var(--sx-spacing-padding4);
}
.sx__calendar-header .sx__date-input {
  padding: var(--sx-spacing-padding3) var(--sx-spacing-padding4);
  font-size: var(--sx-calendar-header-input-font-size);
}
.sx__calendar-header .sx__date-picker-popup {
  z-index: var(--sx-calendar-header-popup-z-index);
}

.sx__calendar-header-content {
  display: flex;
  align-items: center;
  gap: var(--sx-spacing-padding4);
}

.sx__forward-backward-navigation {
  height: 45px;
}
.sx__is-calendar-small .sx__forward-backward-navigation {
  display: none;
}

.sx__range-heading {
  font-size: clamp(16px, 1.25rem, 24px);
}
.sx__is-calendar-small .sx__range-heading {
  font-size: 16px;
}

.sx__today-button {
  padding: var(--sx-spacing-padding3) var(--sx-spacing-padding4);
  border-radius: var(--sx-rounding-extra-small);
  font-size: var(--sx-calendar-header-input-font-size);
  color: var(--sx-internal-color-text);
}
.sx__today-button:active {
  background-color: var(--sx-internal-color-gray-ripple-background);
}
.sx__is-calendar-small .sx__today-button {
  display: none;
}
.sx__calendar-header .sx__today-button {
  border: var(--sx-border);
}
.sx__today-button:hover, .sx__today-button:focus {
  background-color: var(--sx-internal-color-light-gray);
}
.is-dark .sx__today-button:hover, .is-dark .sx__today-button:focus {
  background-color: var(--sx-color-surface-container-low);
}

.sx__view-selection {
  position: relative;
  font-size: var(--sx-calendar-header-input-font-size);
}

.sx__view-selection-selected-item {
  height: 100%;
  width: fit-content;
  padding: var(--sx-spacing-padding3) var(--sx-spacing-padding4);
  cursor: pointer;
  border-radius: var(--sx-rounding-extra-small);
  border: var(--sx-border);
}
.sx__view-selection-selected-item:hover {
  background-color: var(--sx-internal-color-light-gray);
}
.is-dark .sx__view-selection-selected-item:hover {
  background-color: var(--sx-color-surface-container-low);
}

.sx__view-selection-items {
  position: absolute;
  top: 100%;
  box-shadow: var(--sx-box-shadow-level3);
  margin: 0;
  background-color: var(--sx-color-background);
  z-index: var(--sx-calendar-header-popup-z-index);
}
.is-dark .sx__view-selection-items {
  background-color: var(--sx-color-surface-container-high);
}

.sx__view-selection-item {
  padding: var(--sx-spacing-padding4) var(--sx-spacing-padding6);
  cursor: pointer;
}
.sx__view-selection-item:hover, .sx__view-selection-item:focus {
  background-color: var(--sx-color-primary);
  color: var(--sx-color-on-primary);
}
.sx__view-selection-item.is-selected {
  background-color: var(--sx-color-surface-dim);
}
.sx__view-selection-item.is-selected:hover, .sx__view-selection-item.is-selected:focus {
  background-color: var(--sx-color-primary);
  color: var(--sx-color-on-primary);
}

.sx__month-grid-wrapper {
  display: flex;
  flex-flow: column;
  height: 100%;
}

.sx__month-grid-week {
  border-top: var(--sx-border);
  flex: 1;
  display: flex;
}

.sx__month-grid-day {
  padding: var(--sx-spacing-padding2) 0;
  flex: 1;
}
.sx__month-grid-day:not(:last-child) {
  border-right: var(--sx-border);
}

.sx__month-grid-day--dragover {
  background-color: var(--sx-color-surface-container);
}

.sx__month-grid-day__header {
  display: flex;
  flex-flow: column;
  align-items: center;
}

.sx__month-grid-day__header-day-name {
  font-size: 11px;
  text-transform: uppercase;
  color: var(--sx-color-neutral);
}

.sx__month-grid-day__header-date {
  font-size: var(--sx-font-extra-small);
  margin-bottom: var(--sx-spacing-padding1);
  border-radius: 50%;
  height: 24px;
  width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.sx__month-grid-day__header-date.sx__is-today {
  background-color: var(--sx-color-primary);
  color: var(--sx-color-on-primary);
}

.sx__month-grid-day__events-more {
  width: calc(100% - 10px);
  font-size: var(--sx-font-extra-small);
  color: var(--sx-color-neutral);
  margin: var(--sx-spacing-padding1) 0;
  padding: var(--sx-spacing-padding1);
  border-radius: var(--sx-rounding-extra-small);
  cursor: pointer;
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
}
.sx__month-grid-day__events-more:hover {
  background-color: var(--sx-color-surface-container);
  color: var(--sx-color-on-surface);
}

.sx__month-grid-day__events {
  display: grid;
  grid-gap: 4px;
}

.sx__month-grid-cell {
  height: clamp(20px, 1.25rem, 24px);
}

.sx__month-grid-event {
  position: relative;
  display: flex;
  align-items: center;
  padding: var(--sx-spacing-padding1);
  border-radius: var(--sx-rounding-extra-small);
  font-size: clamp(12px, var(--sx-font-extra-small), 14px);
  overflow: hidden;
  white-space: nowrap;
}

.sx__month-grid-event-time {
  margin-right: 4px;
}

.sx__month-grid-blocker {
  pointer-events: none;
}

.sx__month-agenda-week {
  display: flex;
}
.sx__month-agenda-week:not(:first-child) {
  border-top: var(--sx-border);
}

.sx__month-agenda-day {
  padding: var(--sx-spacing-padding2);
  flex: 1;
  display: flex;
  flex-flow: column;
  align-items: center;
  height: 3rem;
  border-radius: var(--sx-rounding-extra-small);
}

.sx__month-agenda-day--active {
  box-shadow: inset 0 0 0 3px var(--sx-color-primary);
}

.sx__month-agenda-day__event-icons {
  margin-top: 4px;
  display: flex;
  grid-gap: 3px;
}

.sx__month-agenda-day__event-icon {
  height: 6px;
  width: 6px;
  border-radius: 50%;
}

.sx__month-agenda-day-names {
  display: flex;
  padding: var(--sx-spacing-padding2) 0;
  font-size: var(--sx-font-extra-small);
  color: var(--sx-color-neutral);
}

.sx__month-agenda-day-name {
  flex: 1;
  display: flex;
  justify-content: center;
}

.sx__month-agenda-events {
  padding: 0 var(--sx-spacing-padding2);
}

.sx__month-agenda-event {
  padding: var(--sx-spacing-padding2);
  margin-bottom: var(--sx-spacing-padding2);
  border-radius: var(--sx-rounding-extra-small);
  font-size: var(--sx-font-small);
}
.sx__month-agenda-event:first-child {
  margin-top: var(--sx-spacing-padding2);
}

.sx__month-agenda-event__title {
  font-weight: 600;
}

.sx__month-agenda-event__has-icon {
  display: flex;
  align-items: center;
}

.sx__month-agenda-events__empty {
  margin-top: var(--sx-spacing-padding4);
  display: flex;
  justify-content: center;
}

.sx__week-wrapper {
  position: relative;
}

.sx__week-grid {
  position: relative;
  padding-left: var(--sx-calendar-week-grid-padding-left);
  display: flex;
  height: var(--sx-week-grid-height);
  overflow: hidden;
}

.sx__week-header {
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: var(--sx-color-background);
}

.sx__week-header-content {
  position: relative;
}

.sx__week-header-border {
  position: absolute;
  width: 100%;
  bottom: 0;
  border-bottom: var(--sx-border);
  border-left: 250px solid transparent;
}

.sx__week-grid__time-axis {
  display: flex;
  flex-flow: column;
  position: absolute;
  right: 0;
  top: var(--sx-week-grid-offset-top);
  width: calc(100% - 60px);
}

.sx__week-grid__hour {
  position: relative;
  height: var(--sx-week-grid-hour-height);
  border-top: var(--sx-border);
  font-size: var(--sx-font-extra-small);
}
.sx__week-grid__hour:first-child {
  visibility: hidden;
}

.sx__week-grid__hour-text {
  position: absolute;
  left: -43px;
  top: -0.75em;
  color: var(--sx-color-neutral);
}

.sx__time-grid-day {
  position: relative;
  width: 100%;
  height: 100%;
  border-left: var(--sx-border);
}

.sx__week-grid__date-axis {
  padding-left: var(--sx-calendar-week-grid-padding-left);
  display: flex;
}

.sx__week-grid__date {
  flex: 1;
  display: flex;
  flex-flow: column;
  align-items: center;
  padding: var(--sx-spacing-padding3) 0;
  gap: var(--sx-spacing-padding1);
}

.sx__week-grid__day-name {
  text-transform: uppercase;
  font-size: var(--sx-font-extra-small);
  color: var(--sx-color-neutral);
  font-weight: 500;
}
.sx__week-grid__date--is-today .sx__week-grid__day-name {
  color: var(--sx-color-primary);
  font-weight: 700;
}

.sx__week-grid__date-number {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--sx-font-extra-large);
  font-weight: 500;
  color: var(--sx-color-neutral);
  height: 2em;
  width: 2em;
}
.sx__week-grid__date--is-today .sx__week-grid__date-number {
  background-color: var(--sx-color-primary);
  color: var(--sx-color-on-primary);
  border-radius: 50%;
}

.sx__time-grid-event {
  width: calc(100% - 10px);
  padding: var(--sx-spacing-padding1);
  position: absolute;
  border-radius: var(--sx-rounding-extra-small);
  font-size: var(--sx-font-extra-small);
  overflow: hidden;
  -webkit-user-select: none;
  user-select: none;
}
.sx__time-grid-event.is-event-copy {
  opacity: 0.5;
  box-shadow: var(--sx-box-shadow-level3);
  z-index: 1;
  transition: transform 0.15s ease-in-out;
}

[data-has-dnd=true] .sx__time-grid-event {
  touch-action: none;
}

.sx__is-resizing .sx__time-grid-event:has(+ .is-event-copy) {
  opacity: 0;
}
.sx__is-resizing .is-event-copy {
  opacity: 1;
}

.sx__time-grid-event-inner {
  position: relative;
  height: 100%;
}

.sx__time-grid-event-resize-handle {
  display: none;
}

/** Only display the resize handle on non-touch devices */
@media (hover: hover) {
  .sx__time-grid-event-resize-handle {
    display: block;
    position: absolute;
    width: 100%;
    bottom: 0;
    cursor: ns-resize;
    height: clamp(10px, 20px, 50%);
  }
}
.sx__time-grid-event-title {
  font-weight: 600;
}

.sx__time-grid-event-time,
.sx__time-grid-event-people,
.sx__time-grid-event-location {
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.sx__event-icon {
  min-width: 15px;
  min-height: 15px;
  max-width: 15px;
  max-height: 15px;
  margin-inline-end: var(--sx-spacing-padding2);
}

.sx__date-grid {
  display: flex;
  padding-left: var(--sx-calendar-week-grid-padding-left);
}

.sx__date-grid-day {
  width: 100%;
  display: grid;
  grid-gap: 2px;
}

.sx__date-grid-event {
  position: relative;
  display: flex;
  align-items: center;
  padding: var(--sx-spacing-padding1);
  border-radius: var(--sx-rounding-extra-small);
  font-size: clamp(12px, var(--sx-font-extra-small), 14px);
  font-weight: 600;
  user-select: none;
}
.sx__date-grid-event:has(.sx__date-grid-event--left-overflow) {
  margin-left: 10px;
}
.sx__date-grid-event:has(.sx__date-grid-event--right-overflow) {
  margin-right: 10px;
}
.sx__date-grid-event .sx__date-grid-event--left-overflow {
  position: absolute;
  z-index: 1;
  width: 10px;
  height: 100%;
  left: -10px;
  clip-path: polygon(100% 0, 0 50%, 100% 100%, 100% 0);
}
.sx__date-grid-event .sx__date-grid-event--right-overflow {
  position: absolute;
  z-index: 1;
  width: 10px;
  height: 100%;
  right: -10px;
  clip-path: polygon(0 0, 100% 50%, 0 100%, 0 0);
}
.sx__date-grid-event.sx__date-grid-event--copy {
  z-index: 2;
  box-shadow: var(--sx-box-shadow-level3);
  transition-property: transform, width;
  transition-duration: 0.15s;
  transition-timing-function: ease-in-out;
}

.sx__date-grid-event-text {
  width: calc(100% - var(--sx-spacing-padding1) * 2);
  left: var(--sx-spacing-padding1);
  position: absolute;
  text-overflow: ellipsis;
  overflow-x: hidden;
  white-space: nowrap;
}
.sx__date-grid-event-text .sx__date-grid-event-time {
  font-weight: initial;
}

.sx__date-grid-cell {
  height: clamp(20px, 1.25rem, 24px);
}

.sx__date-grid-event-resize-handle {
  position: absolute;
  right: 0;
  height: 100%;
  width: clamp(10px, 15px, 50%);
  cursor: ew-resize;
  z-index: 1;
}

:root {
  --sx-week-grid-height: 0;
  --sx-time-axis-height: 0;
  --sx-week-grid-hour-height: 0;
  --sx-week-grid-offset-top: 0;
}

.sx__event-modal {
  visibility: hidden;
  position: fixed;
  top: var(--sx-event-modal-top);
  left: var(--sx-event-modal-left);
  width: 400px;
  max-width: 100%;
  height: fit-content;
  background-color: var(--sx-color-background);
  z-index: 2;
}
.sx__event-modal.is-open {
  animation: slide-sideways;
  animation-duration: 0.3s;
  visibility: initial;
}
.is-dark .sx__event-modal {
  background-color: var(--sx-color-surface-container-high);
}

.sx__event-modal-default {
  padding: var(--sx-spacing-padding6);
  background-color: var(--sx-color-background);
  box-shadow: 0 24px 38px 3px rgba(0, 0, 0, 0.14), 0 9px 46px 8px rgba(0, 0, 0, 0.12), 0 11px 15px -7px rgba(0, 0, 0, 0.2);
  border-radius: var(--sx-rounding-small);
  max-height: 250px;
  overflow-y: scroll;
}

@keyframes slide-sideways {
  from {
    opacity: 0;
    transform: translateX(var(--sx-event-modal-animation-start));
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
.sx__event-modal .sx__event-icon {
  min-width: 16px;
  min-height: 16px;
  max-width: 16px;
  max-height: 16px;
  margin-inline-end: var(--sx-spacing-padding2);
}

.sx__event-modal__color-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 25%;
  margin-right: var(--sx-spacing-padding3);
}

.sx__has-icon {
  display: grid;
  align-items: flex-start;
  grid-template-columns: 30px 1fr;
  margin-bottom: var(--sx-spacing-padding2);
}
.sx__has-icon .sx__event-icon {
  margin-top: 2px;
}
.sx__has-icon .sx__event-modal__color-icon {
  margin-top: 4px;
}

.sx__event-modal__title {
  font-size: var(--sx-font-large);
}

.sx__event-modal__time {
  font-size: var(--sx-font-small);
}

.sx__current-time-indicator {
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #f00;
  z-index: 0;
}
.sx__current-time-indicator::before {
  content: "";
  position: absolute;
  left: -5px;
  top: -4px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #f00;
}

.sx__current-time-indicator-full-week {
  width: calc(100% - var(--sx-calendar-week-grid-padding-left));
  position: absolute;
  inset: 0 0 0 var(--sx-calendar-week-grid-padding-left);
  height: 2px;
  background-color: rgba(255, 0, 0, 0.38);
}