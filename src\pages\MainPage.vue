<template>
  <div class="relative flex flex-col h-full bg-primary">
    <div class="relative flex h-screen">
      <!-- Only show SidebarMain if the route is not "/signin" -->
      <SidebarMain v-if="!isSignInPage" />
      <div :class="{ grow: !isSignInPage, 'w-full': isSignInPage }" class="p-4 overflow-y-hidden">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SidebarMain from "../components/chat/EmailMainSideBar.vue";
import { onMounted, computed, watch } from "vue";
import { useRoute } from "vue-router";

import SubscriptionGate from "./pages/SubscriptionGate.vue";

const route = useRoute();

// Computed property to determine if the current page is the sign-in page
const isSignInPage = computed(() => route.path === "/signin");
// Watch for route changes to update `isSignInPage`
// Watch the route path for changes and log them
watch(
  () => route.path,
  (newPath) => {
    //  console.log("Route changed to:", newPath);
    //  console.log("isSignInPage:", isSignInPage.value);
  },
  { immediate: true } // Run this immediately on component load
);

onMounted(async () => {
  //  console.log(isSignInPage);
});
</script>
<style scoped>
h-screen {
  height: 100vh;
}
.flex-grow {
  flex-grow: 1;
}
</style>
