use crate::{models::important_domain::ImportantDomain, services::important_domain_service};

#[tauri::command]
pub fn get_all_important_domains() -> Vec<ImportantDomain> {
    important_domain_service::get_all()
}

#[tauri::command]
pub fn get_important_domain_by_id(id: String) -> Option<ImportantDomain> {
    important_domain_service::get_by_id(id)
}

#[tauri::command]
pub fn add_important_domain(domain: ImportantDomain) -> Result<(), String> {
    important_domain_service::insert(domain)
}

#[tauri::command]
pub fn update_important_domain(domain: ImportantDomain) -> Result<(), String> {
    important_domain_service::update(domain)
}

#[tauri::command]
pub fn delete_important_domain(id: String) -> Result<(), String> {
    important_domain_service::delete(id)
}

#[tauri::command]
pub fn is_important_user(email: String) -> Result<Option<String>, String> {
    match important_domain_service::get_important_domain_by_email(&email) {
        Some(domain) => Ok(Some(domain.category)),
        None => Ok(None),
    }
}
