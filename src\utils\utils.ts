export function getDefinedAttributes<T extends Record<string, any>>(obj: T): Array<keyof T> {
  return (Object.keys(obj) as Array<keyof T>).filter(
    (key) => obj[key] != null // checks for both undefined and null
  );
}


export function removeProperty<T extends Record<string, any>, K extends keyof T>(
  obj: T,
  key: K
): Omit<T, K> {
  const { [key]: _, ...rest } = obj;
  return rest;
}

export type TimeUnit = "Minutes" | "Hours" | "Days";

export function convertTime(value: number, fromUnit: string, toUnit: string) {
  // Conversion factors to seconds
  const unitToSeconds: { [key: string]: number } = {
    milliseconds: 0.001,
    seconds: 1,
    minutes: 60,
    hours: 3600,
    days: 86400,
    weeks: 604800
  };

  // Validate input units
  if (!unitToSeconds.hasOwnProperty(fromUnit) || !unitToSeconds.hasOwnProperty(toUnit)) {
    throw new Error('Invalid time unit');
  }

  // Convert to seconds first
  const valueInSeconds = value * unitToSeconds[fromUnit];

  // Convert from seconds to target unit
  const convertedValue = valueInSeconds / unitToSeconds[toUnit];

  // Return rounded result to handle floating point precision
  return Math.round(convertedValue * 1000) / 1000;
}

export function parseTimeString(timeString: string): Date {
  const date = new Date(); // Get the current date

  const match = timeString.match(/(\d{1,2}):(\d{2}):(\d{2})\s?(AM|PM)/i);
  if (!match) {
    throw new Error("Invalid time format. Expected format: hh:mm:ss AM/PM");
  }

  let [_, hours, minutes, seconds, period] = match;
  let hour = parseInt(hours, 10);
  const minute = parseInt(minutes, 10);
  const second = parseInt(seconds, 10);

  if (period.toUpperCase() === "PM" && hour !== 12) {
    hour += 12;
  } else if (period.toUpperCase() === "AM" && hour === 12) {
    hour = 0;
  }

  date.setHours(hour, minute, second, 0);
  return date;
}

export const slugify = (str: string, forDisplayingInput?: boolean) => {
  if (!str) {
    return "";
  }

  const s = str
    .toLowerCase() // Convert to lowercase
    .trim() // Remove whitespace from both sides
    .normalize("NFD") // Normalize to decomposed form for handling accents
    .replace(/\p{Diacritic}/gu, "") // Remove any diacritics (accents) from characters
    .replace(/[^.\p{L}\p{N}\p{Zs}\p{Emoji}]+/gu, "-") // Replace any non-alphanumeric characters (including Unicode and except "." period) with a dash
    .replace(/[\s_#]+/g, "-") // Replace whitespace, # and underscores with a single dash
    .replace(/^-+/, "") // Remove dashes from start
    .replace(/\.{2,}/g, ".") // Replace consecutive periods with a single period
    .replace(/^\.+/, "") // Remove periods from the start
    .replace(
      /([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g,
      ""
    ) // Removes emojis
    .replace(/\s+/g, " ")
    .replace(/-+/g, "-"); // Replace consecutive dashes with a single dash

  return forDisplayingInput ? s : s.replace(/-+$/, "").replace(/\.*$/, ""); // Remove dashes and period from end
};
export function fullNameAbbr(name: string) {
  const split = name.split(" ");
  return split.length > 1 ? `${split[0][0]}${split[1][0]}` : `${split[0][0]}${split[0][1]}`
}
