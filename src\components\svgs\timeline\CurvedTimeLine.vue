<template>
  <div class="flex-1 overflow-hidden relative">
    <div class="size-full relative overflow-hiddenx" ref="drawing"></div>
    <EventCard v-if="events.length > 0" v-for="event of events" :key="event.data.id" :event="event" />
    <div
      v-if="time && time.show"
      class="absolute w-12 h-6 bg-primary-600/50 rounded text-sm font-bold text-primary-900 flex justify-center items-center"
      :style="{ left: time.x - 24 + 'px', top: time.y - 38 + 'px' }"
    >
      {{ time.value }}
    </div>
    <div
      :key="weatherStore.data.location.name"
      v-if="hoursWeather"
      v-for="t in hourLabels"
      class="absolute top-0 w-48 h-auto flex flex-col justify-start items-start gap- vdrop-shadow-sm"
      :style="{ left: `${t.x - 90}px`, top: `${t.y > yMid ? t.y + 45 : t.y - 100}px` }"
    >
      <!-- <div class="w-full flex justify-center">
          <div class="text-[8px] py-[1px] px-2 bg-secondary-400 text-white rounded-b-md">
            {{ todayIs(weather.date) }}
          </div>
        </div> -->
      <div class="flex justify-center items-center">
        <div class="size-16">
          <img :src="t.weather?.condition.icon" alt="v" />
        </div>
        <div class="flex flex-col">
          <div class="font-poppins">
            <!-- <div class="text-sm font-semibold">{{ todayIs(weather.date) }}</div> -->
            <div class="font-bold text-2xl leading-6x">{{ t.weather?.temp_c }}°C</div>
            <p class="text-xs text-dark-400">{{ t.weather?.condition.text }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { Circle, Path, Svg, SVG, Text } from "@svgdotjs/svg.js";
import EventCard from "./EventCard.vue";
import { amplitude, endStraight, marginLabel, startStraight, startX } from "./params";
import { TimeLineEvent } from "./type";
import { HourWeather } from "../../../models/weather-models";
import { useWeatherStore } from "../../../stores/weatherStore";
import { Booking } from "../../../models/booking-model";
import { useBookingStore } from "../../../stores/bookingsStore";
import { storeToRefs } from "pinia";

interface Event {
  id: number;
  label: string;
  time: string;
}

const events = ref<TimeLineEvent<Booking>[]>([]);

const weatherStore = useWeatherStore();
const bookingStore = useBookingStore();
const { todayBookings } = storeToRefs(bookingStore);

const hoursWeather = ref<HourWeather[]>();

const drawing = ref();
const draw = ref<Svg>(SVG());

const waveWidth = ref<number>(0); // computed(() => (drawing.value.offsetWidth - startStraight - endStraight) / 6);
const yMid = ref<number>(300); //computed(() => 600 / 2);

function convertBookingsToEvents(value: Booking[]) {
  if (value.length > 0) {
    events.value = value.map((v) => ({
      data: v,
    })) as TimeLineEvent<Booking>[];

    //  console.log("@curved@", events.value);
  }
}

watch(todayBookings.value, (value) => {
  convertBookingsToEvents(value);
});
// Params

let pathStringInit = ` M ${startX},${yMid.value} L ${startX + startStraight},${yMid.value}`;
let pathString = ` M ${startX},${yMid.value} L ${startX + startStraight},${yMid.value}`;
let hourLabels = ref<{ time: string; x: number; y: number; weather?: HourWeather }[]>([]); // will store label positions for each hour

watch(weatherStore.data, (value) => {
  // //  console.log("Events", value);
  // const wHours = value.forecast.forecastday[0].hour;
  // hourLabels.value = hourLabels.value.map((h) => ({
  //   ...h,
  //   weather: wHours.find((w) => w.time.split(" ").pop() == h.time),
  // }));
  // //  console.log("hour labels", hourLabels.value);
  //  console.log("WEATHER CHANGED");
  hoursWeather.value = value.forecast.forecastday[0].hour;
  getHoursWeather();
});

function getHoursWeather() {
  if (hoursWeather.value) {
    hourLabels.value = hourLabels.value.map((h) => ({
      ...h,
      weather: hoursWeather.value?.find((w) => {
        const weath = w.time.split(" ").pop() == h.time;
        //  console.log("weath", weath);
        return weath;
      }),
    }));
    //  console.log("hour labels", hourLabels.value);
  }
}

const unsubscribe = weatherStore.$subscribe((_mut, state) => {
  //  console.log("WEATHER CHANGED");
  hoursWeather.value = state.data.forecast.forecastday[0].hour;
});

const path = ref<Path>();
const timeCircle = ref<Circle>();
const timeCircleIcon = ref<Text>();
const time = ref<{ x: number; y: number; value: string; show?: boolean } | null>(null);

const totalLength = computed(() =>
  path.value && path.value.length ? path.value.length() - startStraight - endStraight : 0
);

const curveLength = computed(() => totalLength.value / 24);

function timeToMinutes(timeStr: string) {
  const [hh, mm] = timeStr.split(":");
  return parseInt(hh, 10) * 60 + parseInt(mm, 10);
}

function drawCurvedLine() {
  let xPos = startX + startStraight;
  let directionUp = true;
  const waveNumber = 6;
  const minutesByWave = (60 * 24) / waveNumber;
  //  console.log("MinutesByWave =>", minutesByWave);
  for (let wave = 0; wave < waveNumber; wave++) {
    //  console.log("WaveWidth", waveWidth.value);
    const xEnd = xPos + waveWidth.value;
    const cp1x = xPos + waveWidth.value / 4;
    const cp2x = xPos + (3 * waveWidth.value) / 4;
    const cpY = directionUp ? yMid.value - amplitude : yMid.value + amplitude;
    pathString += ` C ${cp1x} ${cpY}, ${cp2x} ${cpY}, ${xEnd} ${yMid.value}`;
    const labelX = xPos + waveWidth.value / 2;
    const labelY = directionUp
      ? yMid.value - amplitude - marginLabel + (marginLabel + 40)
      : yMid.value + amplitude + marginLabel - (marginLabel + 40);
    const hourStr =
      Math.trunc((minutesByWave * (wave + 1) - 120) / 60)
        .toString()
        .padStart(2, "0") + ":00";
    hourLabels.value?.push({
      time: hourStr,
      x: labelX,
      y: labelY,
    });

    xPos += waveWidth.value;
    directionUp = !directionUp;
  }

  // 3. Append a straight line at the end.
  pathString += ` L ${xPos + endStraight} ${yMid.value}`;
  const totalWidth = xPos + endStraight + startX;
  if (drawing.value) draw.value = SVG().addTo(drawing.value).size(totalWidth, "100%");

  path.value = draw.value.path(pathString).fill("none").stroke({ color: "#C47D65", width: 4, linecap: "round" });
  //  console.log("DRAWED", hourLabels);
  getHoursWeather();
}

function drawHours() {
  hourLabels.value?.forEach((label) => {
    draw.value
      .text(label.time)
      .fill("#CE7F80")
      .font({ size: 15, family: "Arial", weight: "bold" })
      .center(label.x, label.y + (label.y > yMid.value ? marginLabel : -marginLabel));
    draw.value.circle(7).fill("#CE7F80").center(label.x, label.y);
    draw.value.line(label.x, label.y + 10, label.x, label.y - 10).stroke({ color: "#C47D65", width: 2 });
  });
}

function drawClock(x: number, y: number) {
  if (timeCircle.value && timeCircleIcon.value) {
    timeCircle.value.center(x, y);
    timeCircleIcon.value.center(x, y - 1);
  }
}

function updateCurrentTime() {
  if (path.value) {
    const now = new Date();
    //  console.log("Time => ", now);
    const nowMinutes = now.getHours() * 60 + now.getMinutes(); // 0..1440
    const fraction = nowMinutes / 1440;
    // Map current time onto the curved section (offset by the starting straight line).
    const dist = startStraight + fraction * totalLength.value;
    const pt = path.value.node.getPointAtLength(dist);
    drawClock(pt.x, pt.y);
    time.value = {
      x: pt.x,
      y: pt.y,
      value: now.getHours().toString().padStart(2, "0") + ":" + now.getMinutes().toString().padStart(2, "0"),
    };
  }
}

function drawEvents() {
  events.value = events.value.map((evt) => {
    const colors = ["#3a86ff", "#ef233c", "#fb5607", "#ffbe0b", "#8ac926", "#dc3680"];
    const evtStartTime = new Date(evt.data.startTime);
    const evtMinutes = evtStartTime.getHours() * 60 + evtStartTime.getMinutes(); //timeToMinutes(evt.data?.startTime); // 0 ... 1440
    const fraction = evtMinutes / 1440;
    // Offset the distance by the starting straight segment.
    const dist = startStraight + totalLength.value * fraction; //startStraight + fraction * curvedLength;
    const pt = path.value?.node.getPointAtLength(dist);
    // Draw a marker.
    if (pt) {
      const newEvent = {
        ...evt,
        pos: {
          x: pt.x,
          y: pt.y,
        },
        show: false,
        color: evt.color ?? colors[Math.floor(Math.random() * 6)],
      };
      const eventCircle = draw.value.circle(12).fill(newEvent.color).center(pt.x, pt.y);
      eventCircle.mouseover(() => {
        if (!evt.show) {
          const otherEvents = events.value.filter((e) => e.data.id != evt.data.id);
          events.value = [...otherEvents, { ...newEvent, show: true }];
          //  console.log("Events mouseover =>", events.value);
        }
      });

      eventCircle.mouseout(() => {
        // if (evt.show) {
        const otherEvents = events.value.filter((e) => e.data.id != evt.data.id);
        events.value = [...otherEvents, { ...newEvent, show: false }];
        //  console.log("Events mouseover =>", events.value);
      });

      function isBetween(value: number, x1: number, x2: number): boolean {
        return value >= x1 && value <= x2;
      }
      return newEvent;
    }
    return {
      ...evt,
    };
  });
}

function drawTimeLine() {
  if (drawing.value) drawing.value.innerHTML = "";
  // const content = element.innerHTML;
  let timeUpdater = undefined;
  clearInterval(timeUpdater);
  draw.value = draw.value.clear();
  draw.value = SVG();
  path.value?.clear();
  path.value = undefined;
  pathString = pathStringInit;
  hourLabels.value = [];
  //  console.log("Path", path.value);
  drawCurvedLine();
  drawHours();
  drawEvents();

  timeCircle.value = draw.value
    .circle(20)
    .fill("#C47D6550")
    .center(startX + startStraight, yMid.value);

  timeCircleIcon.value = draw.value
    .text("🕔")
    .font({ size: 14 })
    .center(startX + startStraight, yMid.value);

  timeCircleIcon.value.mouseover(() => {
    if (time.value && !time.value.show) {
      time.value = { ...time.value, show: true };
    }
  });

  timeCircleIcon.value.mouseout(() => {
    if (time.value && time.value.show) {
      time.value = { ...time.value, show: false };
    }
  });

  updateCurrentTime();
  timeUpdater = setInterval(updateCurrentTime, 60_000);
}

onMounted(async () => {
  convertBookingsToEvents(todayBookings.value);
  let cancelTimeOut = setTimeout(() => drawTimeLine(), 500);
  const resizeObserver = new ResizeObserver((entries) => {
    for (let entry of entries) {
      clearTimeout(cancelTimeOut);
      // Get the new size of the div
      const { width, height } = entry.contentRect;
      // computed(() => (drawing.value.offsetWidth - startStraight - endStraight) / 6);
      waveWidth.value = (width - startStraight - endStraight) / 6;
      yMid.value = height / 2;
      //  console.log(`Div size changed: Width = ${width}, Height = ${height}`);
      cancelTimeOut = setTimeout(() => drawTimeLine(), 500);
    }
  });

  // Start observing the div
  if (drawing.value) resizeObserver.observe(drawing.value);

  if (weatherStore.data) {
    hoursWeather.value =
      weatherStore.data.forecast && weatherStore.data.forecast.forecastday
        ? weatherStore.data.forecast.forecastday[0].hour
        : undefined;
    getHoursWeather();
  }
});
</script>

<style scoped></style>
