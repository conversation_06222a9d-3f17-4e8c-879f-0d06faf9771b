import { invoke } from "@tauri-apps/api/core";
import { CalUser } from "../models/user";
import { EmailContext, PhoneCallContext, User } from "../types";

export interface UserData {
  user: User; // Define the User interface separately
  calInfo?: CalUser | null;
  refreshToken?: string | null; // oauth2::RefreshToken is usually a string
  expireIn?: number | null; // Duration → number (in ms or seconds, depending on your API)
  accessToken?: string | null; // oauth2::AccessToken is usually a string
  issuedAt?: string | null; // DateTime<Utc> → ISO string
  emailContext?: EmailContext | null; // Define EmailContext separately
  phoneContext?: PhoneCallContext | null; // Define PhoneCallContext separately
  watchExpiration?: number | null; // i64 in ms → number
  historyId?: string | null;
}

async function openSettingsWindow() {
  try {
    await invoke("start_settings_window");
    return true;
  } catch (error) {
    console.error(error);
    return false;
  }
}


const settingsCmd = {
  openSettingsWindow
};

export default settingsCmd;
