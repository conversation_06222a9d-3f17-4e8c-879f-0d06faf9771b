-- Your SQL goes here
CREATE TABLE meetings (
    id VARCHAR(50) PRIMARY KEY NOT NULL,  -- Unique identifier for the meeting, typically a UUID.
    title TEXT NOT NULL,  -- The title or name of the meeting.
    description TEXT,  -- Detailed description or agenda of the meeting.
    location TEXT,  -- Physical or virtual location of the meeting (e.g., meeting room, Zoom link).
    start_time TIMESTAMP NOT NULL,  -- The date and time when the meeting is scheduled to start.
    end_time TIMESTAMP NOT NULL,  -- The date and time when the meeting is scheduled to end.
    organizer TEXT NOT NULL,  -- The email or name of the person who organized the meeting.
    attendees TEXT,  -- List of attendees or participants (stored in a format such as JSON or comma-separated).
    calendar_id VARCHAR(50) NOT NULL,  -- Identifier for the calendar that this meeting belongs to.
    is_all_day BOOLEAN DEFAULT FALSE,  -- Flag indicating if the meeting lasts all day (no specific start or end time).
    recurrence_pattern TEXT,  -- Pattern describing the recurrence of the meeting (e.g., "daily", "weekly").
    is_recurring BOOLEAN DEFAULT FALSE,  -- Flag indicating whether the meeting is part of a recurring series.
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- Timestamp indicating when the meeting record was created.
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- Timestamp indicating the last time the meeting record was updated.
    reminder_settings TEXT,  -- Details of any reminders set for the meeting (e.g., "10 minutes before").
    time_zone TEXT NOT NULL,  -- Time zone in which the meeting times are set.
    visibility TEXT DEFAULT 'default',  -- Visibility of the meeting (e.g., "public", "private").
    color_code TEXT,  -- Custom color code associated with the meeting for UI purposes.
    status TEXT DEFAULT 'confirmed',  -- Current status of the meeting (e.g., "confirmed", "tentative", "cancelled").
    event_url TEXT,  -- URL to view or join the meeting (e.g., video conference link).
    importance INT,  -- Importance level of the meeting (e.g., 1 for high importance).
    category TEXT,  -- Category or type of the meeting (e.g., "work", "personal").
    attachments TEXT,  -- List of file attachments related to the meeting (stored in a format such as JSON).
    is_cancelled BOOLEAN DEFAULT FALSE,  -- Flag indicating if the meeting has been cancelled.
    recurrence_id VARCHAR(50),  -- Identifier used to link recurring meetings together.
    sentiment TEXT,  -- Sentiment analysis result of the meeting description or related content.
    actionable_items TEXT,  -- List of actionable items identified in the meeting (stored in a format such as JSON).
    language TEXT,  -- Language detected in the meeting content.
    translated_title TEXT,  -- Translated title of the meeting (if the original language is not English).
    event_priority INT,  -- Priority score assigned to the meeting.
    custom_fields TEXT,  -- Any additional custom fields relevant to the meeting (stored in a format such as JSON).
    related_email_id VARCHAR(50)  -- Identifier linking the meeting to a related email, if applicable.
);