<template>
    <label :for="props.id" class="pt-1">
      <input
        type="checkbox"
        :id="props.id"
        v-model="props.modelValue"
        :value="props.value"
        class="peer hidden form-checkbox h-4 w-4 text-dark-600 bg-transparent outline-dark-600 cursor-pointer"
        @change="$emit('update:modelValue', $event.target.checked ? value : null)"
      />
      <div class="peer-checked:hidden size-6 rounded-full border border-base-500 cursor-pointer"></div>
      <div
        class="peer-checked:flex justify-center items-center hidden size-6 rounded-full border-2 border-base-500 cursor-pointer"
      >
        <CheckIcon class="size-4" />
      </div>
    </label>
  </template>
  
  <script setup>
  import { computed } from "vue";
  import { CheckIcon } from "@heroicons/vue/24/outline";
  
  const props = defineProps([
    "modelValue",
    {
      value: {
        type: [String, Number],
        required: true,
      },
      itemId: {
        type: [String, Number],
        required: true,
      },
    },
  ]);
  
  const emit = defineEmits(["update:modelValue"]);
  
  const id = computed(() => `checkbox-${props.itemId}`);
  </script>