{"$message_type":"diagnostic","message":"use of deprecated associated function `chrono::NaiveDateTime::from_timestamp_opt`: use `DateTime::from_timestamp` instead","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\emails_functions\\parsing_email.rs","byte_start":2487,"byte_end":2505,"line_start":75,"line_end":75,"column_start":35,"column_end":53,"is_primary":true,"text":[{"text":"    let mut date = NaiveDateTime::from_timestamp_opt(0, 0).unwrap();","highlight_start":35,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(deprecated)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated associated function `chrono::NaiveDateTime::from_timestamp_opt`: use `DateTime::from_timestamp` instead\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\emails_functions\\parsing_email.rs:75:35\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m75\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut date = NaiveDateTime::from_timestamp_opt(0, 0).unwrap();\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(deprecated)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"module `enhancedFunctions` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":190,"byte_end":207,"line_start":8,"line_end":8,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"mod enhancedFunctions;","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(non_snake_case)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":190,"byte_end":207,"line_start":8,"line_end":8,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"mod enhancedFunctions;","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":"enhanced_functions","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: module `enhancedFunctions` should have a snake case name\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mmod enhancedFunctions;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `enhanced_functions`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(non_snake_case)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to `.clone()` on a reference in this situation does nothing","code":{"code":"noop_method_call","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\fetch_unread_emails.rs","byte_start":5695,"byte_end":5703,"line_start":139,"line_end":139,"column_start":26,"column_end":34,"is_primary":true,"text":[{"text":"        subscription_name.clone()","highlight_start":26,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the type `str` does not implement `Clone`, so calling `clone` on `&str` copies the reference, which does not do anything and can be removed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(noop_method_call)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove this redundant call","code":null,"level":"help","spans":[{"file_name":"src\\commands\\fetch_unread_emails.rs","byte_start":5695,"byte_end":5703,"line_start":139,"line_end":139,"column_start":26,"column_end":34,"is_primary":true,"text":[{"text":"        subscription_name.clone()","highlight_start":26,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to `.clone()` on a reference in this situation does nothing\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\fetch_unread_emails.rs:139:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m139\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        subscription_name.clone()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: remove this redundant call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the type `str` does not implement `Clone`, so calling `clone` on `&str` copies the reference, which does not do anything and can be removed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(noop_method_call)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to `.clone()` on a reference in this situation does nothing","code":{"code":"noop_method_call","explanation":null},"level":"warning","spans":[{"file_name":"src\\google_auth\\get_token.rs","byte_start":6822,"byte_end":6830,"line_start":166,"line_end":166,"column_start":60,"column_end":68,"is_primary":true,"text":[{"text":"                println!(\"redirect_url: \\n{}\", redirect_url.clone());","highlight_start":60,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the type `str` does not implement `Clone`, so calling `clone` on `&str` copies the reference, which does not do anything and can be removed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove this redundant call","code":null,"level":"help","spans":[{"file_name":"src\\google_auth\\get_token.rs","byte_start":6822,"byte_end":6830,"line_start":166,"line_end":166,"column_start":60,"column_end":68,"is_primary":true,"text":[{"text":"                println!(\"redirect_url: \\n{}\", redirect_url.clone());","highlight_start":60,"highlight_end":68}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to `.clone()` on a reference in this situation does nothing\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\google_auth\\get_token.rs:166:60\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m166\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                println!(\"redirect_url: \\n{}\", redirect_url.clone());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: remove this redundant call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the type `str` does not implement `Clone`, so calling `clone` on `&str` copies the reference, which does not do anything and can be removed\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"4 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 4 warnings emitted\u001b[0m\n\n"}
