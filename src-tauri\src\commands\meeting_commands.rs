use crate::models::meeting::Meeting;
use crate::services::meetings_service;
use chrono::{DateTime, NaiveDateTime, Utc};
use uuid::Uuid;

#[tauri::command]
pub fn list_meetings(category_id: String) -> Vec<Meeting> {
    meetings_service::list_meetings(&category_id).expect("Failed to load meetings")
}

#[tauri::command]
pub fn delete_meetings(meeting_ids: Vec<String>) {
    for meeting_id in meeting_ids {
        match meetings_service::delete_meeting(meeting_id) {
            Ok(_) => println!("Successfully deleted meeting."),
            Err(err) => eprintln!("Failed to delete meeting: {}", err),
        }
    }
}

#[tauri::command]
pub fn delete_meeting(meeting_id: String) {
    match meetings_service::delete_meeting(meeting_id) {
        Ok(_) => println!("Successfully deleted meeting."),
        Err(err) => eprintln!("Failed to delete meeting: {}", err),
    }
}

#[tauri::command]
pub fn new_meeting(
    title: String,
    description: Option<String>,
    location: Option<String>,
    start_time: NaiveDateTime,
    end_time: NaiveDateTime,
    organizer: String,
    attendees: Option<Vec<String>>,
    calendar_id: String,
    is_all_day: Option<bool>,
    recurrence_pattern: Option<String>,
    is_recurring: Option<bool>,
    reminder_settings: Option<Vec<String>>,
    time_zone: String,
    visibility: Option<String>,
    color_code: Option<String>,
    status: Option<String>,
    event_url: Option<String>,
    importance: Option<i32>,
    category: Option<String>,
    attachments: Option<Vec<String>>,
    is_cancelled: Option<bool>,
    recurrence_id: Option<String>,
    sentiment: Option<String>,
    actionable_items: Option<Vec<String>>,
    language: Option<String>,
    translated_title: Option<String>,
    event_priority: Option<i32>,
    custom_fields: Option<Vec<String>>,
    related_email_id: Option<String>,
) -> Meeting {
    let new_meeting = Meeting {
        id: Uuid::new_v4().to_string(),
        title,
        description,
        location,
        start_time,
        end_time,
        organizer,
        attendees: attendees.map(|att| serde_json::to_string(&att).unwrap()),
        calendar_id,
        is_all_day,
        recurrence_pattern,
        is_recurring,
        created_at: None, // Let the database handle creation timestamp
        updated_at: None, // Let the database handle update timestamp
        reminder_settings: reminder_settings.map(|rs| serde_json::to_string(&rs).unwrap()),
        time_zone,
        visibility,
        color_code,
        status,
        event_url,
        importance,
        category,
        attachments: attachments.map(|att| serde_json::to_string(&att).unwrap()),
        is_cancelled,
        recurrence_id,
        sentiment,
        actionable_items: actionable_items.map(|ai| serde_json::to_string(&ai).unwrap()),
        language,
        translated_title,
        event_priority,
        custom_fields: custom_fields.map(|cf| serde_json::to_string(&cf).unwrap()),
        related_email_id,
    };

    meetings_service::store_new_meeting(&new_meeting);

    new_meeting
}
