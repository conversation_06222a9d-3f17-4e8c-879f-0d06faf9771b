use crate::commands::delete_email::reply_to_email;
use crate::commands::important_domain_command::is_important_user;
use crate::email_generator::generator_entry::ai_generate_email_reply;
use crate::email_generator::generator_entry::check_reply_safety;
use crate::email_generator::generator_entry::ReplyAssessorResponse;
use crate::emails_functions::parsing_email::parse_email_metadata;
use crate::models::app_data::AppData;
use crate::models::draft::Draft;
use crate::models::task::NewTask;
use crate::models::task::Task;
use crate::models::user_data::User;
use crate::models::user_data::UserData;
use crate::models::user_perference::EmailContext;
use crate::schema::emails::actionable_items;
use crate::schema::emails::urgency_score;
use crate::services::drafts_services::store_draft;
use crate::services::emails_service;
use crate::services::emails_service::get_all_emails_in_thread;
use crate::services::emails_service::is_last_received_email;
use crate::services::tasks_services::store_task;
use crate::services::tasks_services::update_task_execution_status;
use crate::services::tasks_services::update_task_status;
use crate::NaiveDateTime;
use chrono::Utc;
use dotenv::dotenv;
use oauth2::basic::BasicTokenType;
use oauth2::{
    basic::BasicClient,
    reqwest::async_http_client,
    revocation::StandardRevocableToken,
    AccessToken,
    AuthUrl,
    AuthorizationCode,
    ClientId,
    ClientSecret,
    CsrfToken,
    EmptyExtraTokenFields,
    PkceCodeChallenge,
    RedirectUrl,
    RefreshToken,
    RevocationUrl,
    Scope,
    StandardTokenResponse,
    TokenResponse,
    TokenUrl,
};
use regex::Regex;
use reqwest::Client;
use serde_json::from_str;
// use rust_bert::pipelines::ner::NERModel;
// use rust_bert::pipelines::sentiment::{SentimentModel, SentimentPolarity};
// use rust_bert::pipelines::summarization::SummarizationModel;
use serde::Deserialize;
use serde::Serialize;
use serde_json::json;
use serde_json::Value;
use std::collections::HashSet;
use std::error::Error;
use std::sync::Arc;
use tokio::sync::RwLock;
use uuid::Uuid;

use std::time::{ Duration, Instant };
use tauri::{ Manager, RunEvent, WindowEvent };
use tokio::sync::Mutex;
use tokio::sync::MutexGuard;
use tokio_util::sync::CancellationToken;
use tracing::{ error, info, warn };
use chrono::{ Duration as ChronoDuration };

use super::refresh_access_token;
use crate::commands::email_commands;
use crate::emails_service::save_email_summary;
use crate::emails_service::update_processed_email;

use crate::enhancedFunctions::email_priority::analyze_attachments;
use crate::enhancedFunctions::email_priority::detect_language;
use crate::enhancedFunctions::email_priority::determine_urgency;
use crate::enhancedFunctions::email_priority::summarize_thread;
use crate::google_api_functions::refresh_access_token::refresh_access_token;
use crate::models::email::Email;
use crate::models::email_category::EmailCategory;
use std::fs;
use std::io::Write;
use std::path::Path;
use dirs;
use tokio::task;

use crate::enhancedFunctions::email_priority::analyze_sentiment_advanced;
use crate::enhancedFunctions::email_priority::translate_snippet;

// Utility function to get the progress file path in app data directory
fn get_progress_file_path() -> Result<std::path::PathBuf, String> {
    let home_dir = dirs::home_dir().unwrap();
    let config_dir = home_dir.to_str().unwrap().to_string() + "/.config/oway/";

    // Create the directory if it doesn't exist
    if let Err(e) = fs::create_dir_all(&config_dir) {
        return Err(format!("Failed to create config directory: {}", e));
    }

    let file_path = Path::new(&config_dir).join("email_fetch_progress.json");
    println!("Progress file will be stored at: {:?}", file_path);
    Ok(file_path)
}

// Test function to verify the progress file path creation
#[allow(dead_code)]
pub fn test_progress_file_path() -> Result<(), String> {
    match get_progress_file_path() {
        Ok(path) => {
            println!("✅ Progress file path created successfully: {:?}", path);
            println!("✅ Directory exists: {}", path.parent().unwrap().exists());
            Ok(())
        }
        Err(e) => {
            eprintln!("❌ Failed to create progress file path: {}", e);
            Err(e)
        }
    }
}

use crate::enhancedFunctions::extract_actionable_items::extract_actionable_items;
use std::collections::HashMap;

// use std::clone;
// use tch::nn::Module;
// use tch::Device;
// struct AnalyzeEmailResponse {
//     summary: String,
//     priority: String,
//     actionable_items: String,
//     sentiment: Sentiment,
//     urgency_score: i32,
// }

#[derive(Debug, Deserialize)]
pub struct AnalyzeEmailResponse {
    pub classification: String,
    pub summary: String,
    pub priority: String,
    pub actionable_items: Vec<String>,
    pub sentiment: String,
    pub urgency_score: f32,
}

#[derive(Debug, Serialize)]
pub struct EmailSummary {
    pub id: String,
    pub user_id: String,
    pub subject: String,
    pub snippet: String,
    pub thread_id: String,
    pub date: String, // ISO8601 string (e.g., "2024-05-28T06:30:00Z")
    pub category: String,
    pub is_read: bool,
    pub shard: String, // Optional, server will calculate it
}

#[derive(Deserialize, Debug)]
struct Sentiment {
    label: String,
    score: f32,
}

pub async fn fetch_emails(
    app_data: Arc<RwLock<AppData>>,
    refresh_token: &str,
    mut access_token: &str,
    url: String,
    cancel_token: Arc<CancellationToken>
) -> Result<String, String> {
    println!("Let's start fetching emails with access token: {:#?}", access_token);

    let base_url = url.clone();
    let mut access_token = access_token.to_string(); // Initial assignment

    let progress_data = load_progress().unwrap_or_else(|_| json!({}));
    let saved_next_page_token = progress_data.get("nextPageToken").and_then(|v| v.as_str());
    let saved_all_emails_fetched = progress_data
        .get("allEmailsFetched")
        .and_then(|v| v.as_bool())
        .unwrap_or(false);

    // Construct the Gmail API URL, including the nextPageToken if present
    let mut gmail_api_url = if let Some(token) = saved_next_page_token {
        format!("{}?pageToken={}", url, token) // Append nextPageToken to the given URL
    } else {
        url // Start from the beginning if no nextPageToken is saved
    };

    // // Initialize the summarization model using spawn_blocking
    // let summarization_model = task::spawn_blocking(|| SummarizationModel::new(Default::default()))
    //     .await?
    //     .map(|model| Arc::new(Mutex::new(model)))
    //     .map_err(|e| {
    //         eprintln!("Failed to initialize the summarization model: {:?}", e);
    //         Box::new(e) as Box<dyn Error>
    //     })?;

    // println!("Summarization model initialized successfully.");

    // // Initialize the sentiment analysis model using spawn_blocking
    // let sentiment_model = task::spawn_blocking(|| SentimentModel::new(Default::default()))
    //     .await?
    //     .map(|model| Arc::new(Mutex::new(model)))
    //     .map_err(|e| {
    //         eprintln!("Failed to initialize the sentiment model: {:?}", e);
    //         Box::new(e) as Box<dyn Error>
    //     })?;

    // println!("Sentiment model initialized successfully.");

    // // Initialize the NER model using spawn_blocking
    // let ner_model = task::spawn_blocking(|| NERModel::new(Default::default()))
    //     .await?
    //     .map(|model| Arc::new(Mutex::new(model)))
    //     .map_err(|e| {
    //         eprintln!("Failed to initialize the NER model: {:?}", e);
    //         Box::new(e) as Box<dyn Error>
    //     })?;

    let mut blacklisted_domains: HashSet<String> = HashSet::new(); // Initialize with an empty HashSet

    // let mut gmail_api_url = url;
    let client = Client::builder()
        .timeout(Duration::from_secs(180)) // Set a timeout to prevent hanging
        .build()
        .expect("Failed to build client"); // Or handle the error properly

    let mut latest_history_id = String::new(); // Store the latest historyId
    // Check if all emails have been fetched
    // if saved_all_emails_fetched {
    //     println!(
    //         "All emails have already been fetched. Exiting fetch. {}",
    //         saved_all_emails_fetched.clone()
    //     );

    //     // process_emails_with_flag(
    //     //     // summarization_model.clone(),
    //     //     // sentiment_model.clone(),
    //     //     // ner_model.clone(),
    //     // ).await;
    //     return Ok("All emails fetched previously".to_string());
    // }

    loop {
        // println!("Fetching email list from URL: {}", gmail_api_url);

        let response = client.get(&gmail_api_url).bearer_auth(access_token.clone()).send().await;

        match response {
            Ok(resp) => {
                println!("Received response for email list request");

                // Print the raw response for debugging
                let status = resp.status();
                let headers = resp.headers();
                // println!("Response Status: {:?}", status);
                // println!("Response Headers: {:?}", headers);

                // Attempt to parse the response body as JSON
                let json_resp: Value = match resp.json().await {
                    Ok(json) => {
                        // Print the parsed JSON response for debugging
                        // println!("Parsed JSON Response: {:#?}", json);
                        json
                    }
                    Err(e) => {
                        eprintln!("Error parsing JSON response: {:?}", e);
                        break; // Exit the loop on parsing error
                    }
                };

                // Check if there is an error message indicating "Invalid pageToken"
                if let Some(error) = json_resp["error"]["message"].as_str() {
                    if error.contains("Invalid pageToken") {
                        // eprintln!("Invalid pageToken detected. Resetting pagination. Base URL: {}", base_url);
                        // Reset pagination and restart loop
                        gmail_api_url = base_url.clone();
                        continue;
                    }
                }

                // Save progress
                // let history_id = json_resp["historyId"].as_str().unwrap_or("");
                let next_page_token = json_resp["nextPageToken"].as_str();
                // save_progress(history_id, next_page_token, next_page_token.is_none());

                // Save the latest historyId from the response
                if let Some(history_id) = json_resp["historyId"].as_str() {
                    latest_history_id = history_id.to_string();
                }

                if let Some(messages) = json_resp["messages"].as_array() {
                    for message in messages.iter() {
                        if cancel_token.is_cancelled() {
                            println!("Fetch emails cancelled.");
                            return Ok("All emails are fetched".to_string());
                        }
                        if let Some(message_id) = message["id"].as_str() {
                            println!("Fetching details for Message ID: {}", message_id);

                            let email_response = match
                                client
                                    .get(format!("{}/{}", base_url, message_id))
                                    .bearer_auth(access_token.clone())
                                    .send().await
                            {
                                Ok(resp) => {
                                    if resp.status().is_success() {
                                        match resp.json::<Value>().await {
                                            Ok(json) => {
                                                // Log the full response for debugging
                                                // println!(
                                                //     "Full email response for Message ID {}: {:?}",
                                                //     message_id, json["historyId"]
                                                // );
                                                let history_id = json["historyId"]
                                                    .as_str()
                                                    .unwrap_or("");
                                                save_progress_all_emails(
                                                    history_id,
                                                    next_page_token,
                                                    next_page_token.is_none()
                                                );

                                                json
                                            }
                                            Err(e) => {
                                                eprintln!(
                                                    "Error parsing email response for Message ID {}: {:?}",
                                                    message_id,
                                                    e
                                                );
                                                continue; // Skip this email
                                            }
                                        }
                                    } else {
                                        let error_json: Value = resp
                                            .json().await
                                            .unwrap_or_default();
                                        eprintln!(
                                            "API Error Response for Message ID {}: {}",
                                            message_id,
                                            serde_json
                                                ::to_string_pretty(&error_json)
                                                .unwrap_or_else(|_| {
                                                    "Failed to parse error response".to_string()
                                                })
                                        );
                                        if
                                            let Some(error) =
                                                error_json["error"]["message"].as_str()
                                        {
                                            if error.contains("Invalid pageToken") {
                                                println!("Updating gmail_api_url to base URL {}", gmail_api_url);
                                                gmail_api_url = base_url.clone();
                                                println!("New gmail_api_url: {}", gmail_api_url); // Reset pagination by starting from the initial URL without the pageToken
                                                continue;
                                            } else if
                                                error.contains("Invalid Credentials") ||
                                                error_json["error"]["status"] == "UNAUTHENTICATED"
                                            {
                                                // If authentication failed, attempt to refresh the access token
                                                println!(
                                                    "Invalid credentials detected, refreshing access token..."
                                                );
                                                // match refresh_access_token(refresh_token).await {
                                                //     Ok(new_token) => {
                                                //         access_token = new_token.secret().clone(); // Update `access_token` safely
                                                //         println!(
                                                //             "Access token refreshed successfully."
                                                //         );
                                                //     }
                                                //     Err(e) => {
                                                //         eprintln!(
                                                //             "Failed to refresh access token: {:?}",
                                                //             e
                                                //         );
                                                //         break; // Exit loop if token refresh fails
                                                //     }
                                                // }
                                                match refresh_access_token(&refresh_token).await {
                                                    Ok(new_token) => {
                                                        // Step 4: Acquire write lock to update user data
                                                        let mut app_data_write =
                                                            app_data.write().await;
                                                        let mut user_data =
                                                            app_data_write.user_data.write().await;

                                                        user_data.access_token = Some(
                                                            oauth2::AccessToken::new(
                                                                new_token.clone()
                                                            )
                                                        );
                                                        user_data.set_issued_at(); // Update issued_at time
                                                        user_data.save_me(); // Save changes
                                                        access_token = new_token.clone();
                                                        println!(
                                                            "Access token refreshed and updated."
                                                        );
                                                    }
                                                    Err(err) => {
                                                        println!(
                                                            "Failed to refresh access token: {:?}",
                                                            err
                                                        );
                                                    }
                                                }
                                                continue; // Retry the request with the new token
                                            }
                                        }
                                        continue; // Skip to the next message
                                    }
                                }
                                Err(e) => {
                                    eprintln!(
                                        "Request error for Message ID {}: {:?}",
                                        message_id,
                                        e
                                    );
                                    continue; // Skip this email and move to the next one
                                }
                            };

                            // Check if the "id" field is present in the email response
                            if email_response.get("id").is_none() {
                                eprintln!(
                                    "ID not found in email response for Message ID {}: {:?}",
                                    message_id,
                                    email_response
                                );
                                continue; // Skip this email if the ID is not found
                            }

                            // Proceed with parsing the valid email response...
                            let email = match
                                parse_email_metadata(
                                    &email_response // summarization_model.clone(),
                                    // sentiment_model.clone(),
                                    // ner_model.clone(), // &blacklisted_domains,
                                ).await
                            {
                                Ok(email) => email,
                                Err(e) => {
                                    eprintln!(
                                        "Error parsing email metadata for Message ID {}: {:?}",
                                        message_id,
                                        e
                                    );
                                    continue; // Skip this email
                                }
                            };

                            println!(
                                "before store_email_metadata - Email sender: \n{:#?}",
                                &email.from
                            );

                            // emails.push(email);

                            // if emails.len() > 100 {
                            //     store_and_reset_emails(&mut emails);
                            //     emails.clear();
                            // }
                        } else {
                            eprintln!("Error: message['id'] is None");
                        }
                    }
                } else {
                    println!("No messages found in the current response");
                }

                // Handle pagination
                if let Some(next_page_token) = json_resp["nextPageToken"].as_str() {
                    gmail_api_url =
                        format!("https://www.googleapis.com/gmail/v1/users/me/messages?pageToken={}", next_page_token);
                } else {
                    println!("No more pages to fetch");
                    break;
                }
            }
            Err(e) => {
                eprintln!("Error sending request to Gmail API: {:?}", e);
                // Implement a retry mechanism with exponential backoff
                tokio::time::sleep(Duration::from_secs(2)).await; // Wait before retrying
                continue; // Retry the request
            }
        }
    }

    // process_emails_with_flag(
    //     // summarization_model.clone(),
    //     // sentiment_model.clone(),
    //     // ner_model.clone(),
    // ).await;
    // Return the latest historyId
    return Ok("All emails are fetched".to_string());
}

pub async fn fetch_fresh_emails(
    app_data: Arc<RwLock<AppData>>,
    refresh_token: &str,
    mut access_token: &str,
    url: String,
    cancel_token: Arc<CancellationToken>
) -> Result<String, String> {
    println!("Let's start fetching emails with access token: {:#?}", access_token);

    let base_url = url.clone();
    let mut access_token = access_token.to_string(); // Initial assignment
    let progress_data = load_progress().unwrap_or_else(|_| json!({}));
    let saved_next_page_token = progress_data.get("nextPageToken").and_then(|v| v.as_str());

    let mut latest_history_id = String::new(); // Store the latest historyId

    // Construct the Gmail API URL, including the nextPageToken if present
    let mut gmail_api_url = if let Some(token) = saved_next_page_token {
        format!("{}&pageToken={}", url, token) // Append nextPageToken to the given URL
    } else {
        url // Start from the beginning if no nextPageToken is saved
    };

    println!("🔍 Gmail API URL: {}", gmail_api_url);

    let mut blacklisted_domains: HashSet<String> = HashSet::new(); // Initialize with an empty HashSet

    // let mut gmail_api_url = url;
    let client = Client::builder()
        .timeout(Duration::from_secs(180)) // Set a timeout to prevent hanging
        .build()
        .expect("Failed to build client"); // Or handle the error properly

    loop {
        // println!("Fetching email list from URL: {}", gmail_api_url);

        let response = client.get(&gmail_api_url).bearer_auth(access_token.clone()).send().await;

        match response {
            Ok(resp) => {
                println!("Received response for email list request");

                // Print the raw response for debugging
                let status = resp.status();
                let headers = resp.headers();
                // println!("Response Status: {:?}", status);
                // println!("Response Headers: {:?}", headers);

                // Attempt to parse the response body as JSON
                let json_resp: Value = match resp.json().await {
                    Ok(json) => {
                        // Print the parsed JSON response for debugging
                        // println!("Parsed JSON Response: {:#?}", json);
                        json
                    }
                    Err(e) => {
                        eprintln!("Error parsing JSON response: {:?}", e);
                        break; // Exit the loop on parsing error
                    }
                };

                // Check if there is an error message indicating "Invalid pageToken"
                if let Some(error) = json_resp["error"]["message"].as_str() {
                    if error.contains("Invalid pageToken") {
                        // eprintln!("Invalid pageToken detected. Resetting pagination. Base URL: {}", base_url);
                        // Reset pagination and restart loop
                        gmail_api_url = base_url.clone();
                        continue;
                    }
                }

                // Save progress
                let history_id = json_resp["historyId"].as_str().unwrap_or("");
                let next_page_token = json_resp["nextPageToken"].as_str();
                // save_progress(history_id, next_page_token, next_page_token.is_none());

                // Save the latest historyId from the response
                if let Some(history_id) = json_resp["historyId"].as_str() {
                    latest_history_id = history_id.to_string();
                }
                if let Some(history_array) = json_resp["history"].as_array() {
                    for history_item in history_array {
                        if let Some(messages) = history_item["messagesAdded"].as_array() {
                            for message in messages.iter() {
                                if cancel_token.is_cancelled() {
                                    println!("Fetch emails cancelled.");
                                    return Ok("All emails are fetched".to_string());
                                }
                                if let Some(message_id) = message["message"]["id"].as_str() {
                                    println!("Fetching details for Message ID: {}", message_id);

                                    let email_response = match
                                        client
                                            .get(
                                                format!("https://www.googleapis.com/gmail/v1/users/me/messages/{}", message_id)
                                            ) // ✅ correct
                                            // .get(format!("{}/{}", base_url, message_id))
                                            .bearer_auth(access_token.clone())
                                            .send().await
                                    {
                                        Ok(resp) => {
                                            if resp.status().is_success() {
                                                match resp.json::<Value>().await {
                                                    Ok(json) => {
                                                        // Log the full response for debugging
                                                        // println!(
                                                        //     "Full email response for Message ID {}: {:?}",
                                                        //     message_id, json
                                                        // );
                                                        json
                                                    }
                                                    Err(e) => {
                                                        eprintln!(
                                                            "Error parsing email response for Message ID {}: {:?}",
                                                            message_id,
                                                            e
                                                        );
                                                        continue; // Skip this email
                                                    }
                                                }
                                            } else {
                                                let error_json: Value = resp
                                                    .json().await
                                                    .unwrap_or_default();
                                                eprintln!(
                                                    "API Error Response for Message ID {}: {}",
                                                    message_id,
                                                    serde_json
                                                        ::to_string_pretty(&error_json)
                                                        .unwrap_or_else(|_| {
                                                            "Failed to parse error response".to_string()
                                                        })
                                                );
                                                if
                                                    let Some(error) =
                                                        error_json["error"]["message"].as_str()
                                                {
                                                    if error.contains("Invalid pageToken") {
                                                        println!("Updating gmail_api_url to base URL {}", gmail_api_url);
                                                        gmail_api_url = base_url.clone();
                                                        println!("New gmail_api_url: {}", gmail_api_url); // Reset pagination by starting from the initial URL without the pageToken
                                                        continue;
                                                    } else if
                                                        error.contains("Invalid Credentials") ||
                                                        error_json["error"]["status"] ==
                                                            "UNAUTHENTICATED"
                                                    {
                                                        // If authentication failed, attempt to refresh the access token
                                                        println!(
                                                            "Invalid credentials detected, refreshing access token..."
                                                        );
                                                        // match refresh_access_token(refresh_token).await {
                                                        //     Ok(new_token) => {
                                                        //         access_token = new_token.secret().clone(); // Update `access_token` safely
                                                        //         println!(
                                                        //             "Access token refreshed successfully."
                                                        //         );
                                                        //     }
                                                        //     Err(e) => {
                                                        //         eprintln!(
                                                        //             "Failed to refresh access token: {:?}",
                                                        //             e
                                                        //         );
                                                        //         break; // Exit loop if token refresh fails
                                                        //     }
                                                        // }
                                                        match
                                                            refresh_access_token(
                                                                &refresh_token
                                                            ).await
                                                        {
                                                            Ok(new_token) => {
                                                                // Step 4: Acquire write lock to update user data
                                                                let mut app_data_write =
                                                                    app_data.write().await;
                                                                let mut user_data =
                                                                    app_data_write.user_data.write().await;

                                                                user_data.access_token = Some(
                                                                    oauth2::AccessToken::new(
                                                                        new_token.clone()
                                                                    )
                                                                );
                                                                user_data.set_issued_at(); // Update issued_at time
                                                                user_data.save_me(); // Save changes
                                                                access_token = new_token.clone();
                                                                println!(
                                                                    "Access token refreshed and updated."
                                                                );
                                                            }
                                                            Err(err) => {
                                                                println!(
                                                                    "Failed to refresh access token: {:?}",
                                                                    err
                                                                );
                                                            }
                                                        }
                                                        continue; // Retry the request with the new token
                                                    }
                                                }
                                                continue; // Skip to the next message
                                            }
                                        }
                                        Err(e) => {
                                            eprintln!(
                                                "Request error for Message ID {}: {:?}",
                                                message_id,
                                                e
                                            );
                                            continue; // Skip this email and move to the next one
                                        }
                                    };

                                    // Check if the "id" field is present in the email response
                                    if email_response.get("id").is_none() {
                                        eprintln!(
                                            "ID not found in email response for Message ID {}: {:?}",
                                            message_id,
                                            email_response
                                        );
                                        continue; // Skip this email if the ID is not found
                                    }

                                    // Proceed with parsing the valid email response...
                                    let email = match
                                        parse_email_metadata(
                                            &email_response // summarization_model.clone(),
                                            // sentiment_model.clone(),
                                            // ner_model.clone(), // &blacklisted_domains,
                                        ).await
                                    {
                                        Ok(email) => email,
                                        Err(e) => {
                                            eprintln!(
                                                "Error parsing email metadata for Message ID {}: {:?}",
                                                message_id,
                                                e
                                            );
                                            continue; // Skip this email
                                        }
                                    };

                                    println!(
                                        "before store_email_metadata - Email sender: \n{:#?}",
                                        &email.from
                                    );

                                    // emails.push(email);

                                    // if emails.len() > 100 {
                                    //     store_and_reset_emails(&mut emails);
                                    //     emails.clear();
                                    // }
                                } else {
                                    eprintln!("Error: message['id'] is None");
                                }
                            }
                        } else {
                            println!("No messages found in the current response");
                        }
                    }
                }
                // Handle pagination
                if let Some(next_page_token) = json_resp["nextPageToken"].as_str() {
                    gmail_api_url =
                        format!("https://www.googleapis.com/gmail/v1/users/me/messages?pageToken={}", next_page_token);
                } else {
                    println!("No more pages to fetch");
                    break;
                }
            }
            Err(e) => {
                eprintln!("Error sending request to Gmail API: {:?}", e);
                // Implement a retry mechanism with exponential backoff
                tokio::time::sleep(Duration::from_secs(2)).await; // Wait before retrying
                continue; // Retry the request
            }
        }
    }

    // process_emails_with_flag(
    //     // summarization_model.clone(),
    //     // sentiment_model.clone(),
    //     // ner_model.clone(),
    // ).await;
    // Return the latest historyId
    return Ok("All emails are fetched".to_string());
}

pub async fn process_emails_with_flag(
    // summarization_model: Arc<Mutex<SummarizationModel>>,
    // sentiment_model: Arc<Mutex<SentimentModel>>,
    // ner_model: Arc<Mutex<NERModel>>,
    app_data: tauri::State<'_, Arc<RwLock<AppData>>>
) -> Option<()> {
    // Fetch emails with process_flag = true
    let emails_with_process_flag = email_commands::list_emails_with_process_flag_true();

    let mut email_context: Option<EmailContext> = None;
    let mut username = String::new();

    let mut username_email = String::new();

    {
        let app_data_arc = app_data.read().await; // Acquire read lock
        let user_data = app_data_arc.user_data.read().await;
        username = user_data.user.email.split('@').next().unwrap_or("").to_string();
        username_email = user_data.user.email.clone(); // Clone
        email_context = user_data.email_context.clone(); // Clone if `Duration` is needed later
    }

    // Loop through each email and process it
    for email in emails_with_process_flag {
        // Extract relevant data from the email
        println!("Starting email processing...");

        // Step 1: Extract thread_id, snippet, subject, etc.
        let thread_id = email.thread_id.as_deref().unwrap_or("");
        println!("Thread ID: {}", thread_id);

        let snippet = email.snippet.as_str();
        println!("Snippet: {}", snippet);

        let thread_content = snippet; // Assuming snippet as the content
        let subject = email.subject.as_str();
        println!("Subject: {}", subject);
        let sender = email.from.as_str();

        let max_summary_length = Some(100);

        // Step 2: Execute summarization, urgency, sentiment, and NER analysis concurrently
        println!("Starting summarization for thread...");

        // let analyze_attachment = analyze_email(subject, snippet, sender).await.unwrap();
        // let fixed_categories = vec![
        //     "needs_reply",
        //     "waiting_response",
        //     "fyi_read_later",
        //     "delegated_handled",
        //     "calendar_scheduling",
        //     "clients_vips",
        //     "ads_newsletters",
        // ];
        // let thread_summary = analyze_attachment.summary;
        // let urscore: i32 = analyze_attachment.urgency_score;
        // let sentiment = analyze_attachment.sentiment.label;
        // let actionable = analyze_attachment.actionable_items;

        // println!("Analysis results obtained:");
        // println!("Thread Summary: {:?}", thread_summary);
        // println!("Urgency Score: {:?}", urgency_score);
        // println!("Sentiment: {:?}", sentiment);
        // println!("Actionable Items: {:?}", actionable_items);

        // // Step 4: Analyze attachments (if any)
        // println!("Analyzing attachments...");
        // let attachments: Vec<HashMap<String, String>> = vec![]; // Replace with actual attachment data
        // let (attachment_count, attachment_types, total_attachment_size) =
        //     analyze_attachments(attachments);
        // // Step 5: Detect language and translate snippet if necessary
        // println!("Detecting language...");
        // let language = detect_language(&snippet);
        // println!("Language Detected: {}", language);

        // let translated_snippet = if language != "en" {
        //     println!("Translating snippet...");
        //     let translation = translate_snippet(&snippet, "en");
        //     println!("Translated Snippet: {:?}", translation);
        //     translation
        // } else {
        //     println!("No translation needed.");
        //     None
        // };

        // // Done
        // println!("Email processing completed.");

        // // Call the separate function to update the database
        // update_processed_email(
        //     &email.id,
        //     Some(thread_summary),
        //     Some(urscore),
        //     Some(sentiment),
        //     actionable,
        //     attachment_types.join(", "),
        //     language
        // ).await;

        let analyze_result = if is_hard_blocked_sender(sender) {
            AnalyzeEmailResponse {
                classification: "ads_newsletters".to_string(),
                summary: "No summary available".to_string(),
                priority: "Low".to_string(),
                actionable_items: vec![],
                sentiment: "Neutral".to_string(),
                urgency_score: 0.0,
            }
        } else {
            match analyze_email(subject, snippet, sender).await {
                Ok(result) => result,
                Err(err) => {
                    println!("Error calling analyze_email: {:?}", err);
                    AnalyzeEmailResponse {
                        classification: "Unknown".to_string(),
                        summary: "No summary available".to_string(),
                        priority: "Low".to_string(),
                        actionable_items: vec![],
                        sentiment: "Neutral".to_string(),
                        urgency_score: 0.0,
                    }
                }
            }
        };

        use tokio::spawn;

        // Your fixed categories in canonical format
        let fixed_categories = vec![
            "needs_reply",
            "waiting_response",
            "fyi_read_later",
            "delegated_handled",
            "calendar_scheduling",
            "clients_vips",
            "ads_newsletters"
        ];

        // Map LLM classification → internal fixed_categories

        let mut mapped_category = match analyze_result.classification.to_lowercase().as_str() {
            "needs reply" => "needs_reply",
            "waiting for response" => "waiting_response",
            "fyi / read later" => "fyi_read_later",
            "delegated / handled" => "delegated_handled",
            "calendar / scheduling" => "calendar_scheduling",
            "vips" => "clients_vips",
            "ads / newsletters" => "ads_newsletters",
            _ => "fyi_read_later", // fallback default
        };

        let from = email.from.as_str();
        println!("from: {}", from);

        // Extract email from string like "Name <<EMAIL>>"
        let re = Regex::new(r"<([^>]+)>").unwrap();

        if let Some(caps) = re.captures(from) {
            let extracted_email = caps.get(1).unwrap().as_str();
            println!("Extracted email: {}", extracted_email);

            // Check if this email is marked as important
            match is_important_user(extracted_email.to_string()) {
                Ok(Some(category)) => {
                    println!("🚨 Important user category: {}", category);
                    // Overwrite mapped_category if important
                    mapped_category = "clients_vips";
                }
                Ok(None) => println!("ℹ️ Not an important user"),
                Err(err) => eprintln!("❌ Error checking importance: {}", err),
            }
        } else {
            println!("⚠️ Could not extract email from: {}", from);
        }
        let from = email.from.as_str();
        println!("from: {}", from);

        // Regex to extract email from "Name <<EMAIL>>"
        let re = Regex::new(r"<([^>]+)>").unwrap();

        // Step 3: Check if user is in the CC list
        if let Some(cc_list) = &email.cc {
            if let Ok(cc_emails) = serde_json::from_str::<Vec<String>>(cc_list) {
                if cc_emails.iter().any(|e| e.contains(&username_email)) {
                    println!("📩 User is in CC list – overriding to fyi_read_later");
                    mapped_category = "fyi_read_later";
                }
            } else {
                println!("⚠️ Failed to parse cc JSON list: {}", cc_list);
            }
        }

        if let Some(bcc_list) = &email.bcc {
            if let Ok(bcc_emails) = serde_json::from_str::<Vec<String>>(bcc_list) {
                if bcc_emails.iter().any(|e| e.contains(&username_email)) {
                    println!("📩 User is in BCC list – overriding to fyi_read_later");
                    mapped_category = "fyi_read_later";
                }
            } else {
                println!("⚠️ Failed to parse bcc JSON list: {}", bcc_list);
            }
        }

        let thread_summary = analyze_result.summary;
        let urgencyscore = (analyze_result.urgency_score * 100.0).round() as i32; // Convert 0.00–0.99 to 0–99 or scale as needed
        let sentiment = analyze_result.sentiment;
        let actionableitems = analyze_result.actionable_items.join(", "); // convert Vec<String> to comma-separated string for DB

        println!("Analysis results obtained:");
        println!("Classification: {:?}", mapped_category);
        println!("Thread Summary: {:?}", thread_summary);
        println!("Urgency Score: {:?}", urgency_score);
        println!("Sentiment: {:?}", sentiment);
        println!("Actionable Items: {:?}", actionable_items);
        let formatted_date = email.date.format("%Y-%m-%dT%H:%M:%SZ").to_string();
        let email_summary = EmailSummary {
            id: email.id.clone(),
            user_id: username.to_string(),
            subject: subject.to_string(),
            snippet: thread_summary.to_string(),
            thread_id: email.thread_id.clone().unwrap_or_default().clone(),
            date: formatted_date,
            category: mapped_category.to_string(),
            is_read: false,
            shard: "".to_string(),
        };

        // Spawn and log any errors
        // spawn(async move {
        //     if let Err(err) = save_email_summary(email_summary).await {
        //         eprintln!("🔥 Failed to send email summary to server: {:?}", err);
        //     }
        // });
        // Step 4: Analyze attachments
        println!("Analyzing attachments...");
        let attachments: Vec<HashMap<String, String>> = vec![]; // Replace with actual attachment data
        let (attachment_count, attachment_types, total_attachment_size) =
            analyze_attachments(attachments);

        // Step 5: Detect language and translate snippet if needed
        println!("Detecting language...");
        let language = detect_language(&snippet);
        println!("Language Detected: {}", language);

        let translated_snippet = if language != "en" {
            println!("Translating snippet...");
            let translation = translate_snippet(&snippet, "en");
            println!("Translated Snippet: {:?}", translation);
            translation
        } else {
            println!("No translation needed.");
            None
        };

        println!("Email processing completed.");
        let email_date_str = email.date.to_string(); // e.g. "2022-03-30 12:06:51"
        let email_date_str = email_date_str.as_str(); // Convert String to &str

        // Parse the email date string into NaiveDateTime
        let parsed_date = match NaiveDateTime::parse_from_str(email_date_str, "%Y-%m-%d %H:%M:%S") {
            Ok(dt) => dt,
            Err(e) => {
                eprintln!("Failed to parse email date: {:?}", e);
                continue; // or return a default/fallback NaiveDateTime
            }
        };

        let two_weeks_ago = Utc::now().naive_utc() - ChronoDuration::days(14);
        // Save results to DB
        update_processed_email(
            &email.id,
            Some(thread_summary),
            Some(urgencyscore),
            Some(sentiment),
            actionableitems,
            attachment_types.join(", "),
            language,
            mapped_category.to_string() // <-- NEW
        ).await;
        if
            parsed_date < two_weeks_ago &&
            (urgencyscore as f32) > 90.0 &&
            !matches!(
                mapped_category,
                "fyi_read_later" | "ads_newsletters" | "calendar_scheduling" | "clients_vips"
            )
        {
            if !email.thread_id.is_none() {
                println!("#############################################");

                let new_task = NewTask {
                    id: rand::random::<i32>(),
                    title: format!("Reply to : {}", email.subject),
                    category: Some(String::from("email")),
                    sub_type: Some(String::from("reply_email")),
                    status: Some(String::from("in_progress")),
                    priority: None,
                    linked_entity: Some(email.id.clone()),
                    context_type: None,
                    context_data: None,
                    due_date: None,
                    assigned_to: None,
                    requires_sync: None,
                    queue_position: None,
                    snoozed_until: None,
                    auto_executable: None,
                    execution_status: Some(String::from("in_progress")),
                    execution_attempts: None,
                    last_attempted_at: None,
                    reschedule_count: None,
                    created_at: None,
                };

                let task = store_task(&new_task);
                tokio::spawn({
                    let task = task.clone();
                    let username = username.clone();
                    async move {
                        if let Err(err) = save_task(task, username).await {
                            eprintln!("🔥 Failed to save task to server: {:?}", err);
                        }
                    }
                });

                if let Some(ref context) = email_context {
                    let thread_conversation = if let Some(thread_id) = &email.thread_id {
                        get_all_emails_in_thread(thread_id.clone()).unwrap_or_default()
                        // ✅ Handles error or empty case
                    } else {
                        vec![] // ✅ No thread_id provided
                    };
                    let reply = ai_generate_email_reply(
                        context,
                        &email.snippet,
                        thread_conversation.clone()
                    ).await
                        .map_err(|e| {
                            eprintln!("Error generating email: {:?}", e);
                            e.to_string() // Convert the error into a string
                        })
                        .unwrap();
                    println!("Reply => {:?}", reply);

                    let assessor: ReplyAssessorResponse = check_reply_safety(
                        context,
                        &email.snippet,
                        &reply.body,
                        thread_conversation.clone()
                    ).await
                        .map_err(|e| {
                            eprintln!("Error generating email: {:?}", e);
                            e.to_string() // Convert the error into a string
                        })
                        .unwrap();
                    println!("Assessor => {:?}", assessor);

                    if !assessor.requires_user_input && assessor.confidence_score > 1.0 {
                        println!("Sent Email");
                        match
                            reply_to_email(
                                app_data,
                                email.id.clone(),
                                email.from,
                                None,
                                None,
                                reply.subject,
                                reply.body,
                                Vec::new()
                            ).await
                        {
                            Ok(_) => {
                                println!("Email Sent Successfully");
                                update_task_execution_status(task.id, "completed");
                                update_task_status(task.id, "completed");
                            }
                            Err(e) => {
                                update_task_execution_status(task.id, "failed");
                                update_task_status(task.id, "pending");
                                eprintln!("Error in replying to an email, {}", e);
                            }
                        }
                    } else {
                        println!("Create the draft and wait the use validation");
                        let new_draft = Draft {
                            id: Uuid::new_v4().to_string(),
                            from: email.to,
                            to: email.from,
                            subject: reply.subject,
                            body: reply.body,
                            cc: None,
                            bcc: None,
                            thread_id: email.thread_id.clone(),
                            parent_email_id: Some(email.id),
                            is_send: None,
                            status: String::from("waiting_user_action"),
                            task_id: Some(task.id),
                            created_at: None,
                            updated_at: None,
                        };
                        let draft = store_draft(&new_draft);
                        if draft.id.is_empty() {
                            update_task_status(task.id, "waiting_for_response");
                            update_task_execution_status(task.id, "completed");
                        } else {
                            update_task_status(task.id, "pending");
                            update_task_execution_status(task.id, "failed");
                        }
                    }
                } else {
                    eprintln!("Can't get the email context");
                    update_task_execution_status(task.id, "failed");
                    update_task_status(task.id, "pending");
                }

                println!("Task {:?} title {:?}", task.id, task.title);
                tokio::time::sleep(Duration::from_millis(300)).await;
                break;
            } else if let Some(thread_id) = &email.thread_id {
                if let Ok(true) = is_last_received_email(&email.id, thread_id) {
                    println!("#############################################");

                    let thread_conversation = if let Some(thread_id) = &email.thread_id {
                        get_all_emails_in_thread(thread_id.clone()).unwrap_or_default()
                        // ✅ Handles error or empty case
                    } else {
                        vec![] // ✅ No thread_id provided
                    };

                    let new_task = NewTask {
                        id: rand::random::<i32>(),
                        title: format!("Reply to : {}", email.subject),
                        category: Some(String::from("email")),
                        sub_type: Some(String::from("reply_email")),
                        status: Some(String::from("in_progress")),
                        priority: None,
                        linked_entity: Some(email.id.clone()),
                        context_type: None,
                        context_data: None,
                        due_date: None,
                        assigned_to: None,
                        requires_sync: None,
                        queue_position: None,
                        snoozed_until: None,
                        auto_executable: None,
                        execution_status: Some(String::from("in_progress")),
                        execution_attempts: None,
                        last_attempted_at: None,
                        reschedule_count: None,
                        created_at: None,
                    };

                    let task = store_task(&new_task);
                    tokio::spawn({
                        let task = task.clone();
                        let username = username.clone();
                        async move {
                            if let Err(err) = save_task(task, username).await {
                                eprintln!("🔥 Failed to save task to server: {:?}", err);
                            }
                        }
                    });

                    if let Some(ref context) = email_context {
                        let reply = ai_generate_email_reply(
                            context,
                            &email.snippet,
                            thread_conversation.clone()
                        ).await
                            .map_err(|e| {
                                eprintln!("Error generating email: {:?}", e);
                                e.to_string() // Convert the error into a string
                            })
                            .unwrap();
                        println!("Reply => {:?}", reply);

                        let assessor: ReplyAssessorResponse = check_reply_safety(
                            context,
                            &email.snippet,
                            &reply.body,
                            thread_conversation.clone()
                        ).await
                            .map_err(|e| {
                                eprintln!("Error generating email: {:?}", e);
                                e.to_string() // Convert the error into a string
                            })
                            .unwrap();
                        println!("Assessor => {:?}", assessor);

                        if !assessor.requires_user_input && assessor.confidence_score > 1.0 {
                            println!("Sent Email");
                            match
                                reply_to_email(
                                    app_data,
                                    email.id.clone(),
                                    email.from,
                                    None,
                                    None,
                                    reply.subject,
                                    reply.body,
                                    Vec::new()
                                ).await
                            {
                                Ok(_) => {
                                    println!("Email Sent Successfully");
                                    update_task_execution_status(task.id, "completed");
                                    update_task_status(task.id, "completed");
                                }
                                Err(e) => {
                                    update_task_execution_status(task.id, "failed");
                                    update_task_status(task.id, "pending");
                                    eprintln!("Error in replying to an email, {}", e);
                                }
                            }
                        } else {
                            println!("Create the draft and wait the use validation");
                            let new_draft = Draft {
                                id: Uuid::new_v4().to_string(),
                                from: email.to,
                                to: email.from,
                                subject: reply.subject,
                                body: reply.body,
                                cc: None,
                                bcc: None,
                                thread_id: email.thread_id.clone(),
                                parent_email_id: Some(email.id),
                                is_send: None,
                                status: String::from("waiting_user_action"),
                                task_id: Some(task.id),
                                created_at: None,
                                updated_at: None,
                            };
                            let draft = store_draft(&new_draft);
                            if draft.id.is_empty() {
                                update_task_status(task.id, "waiting_for_response");
                                update_task_execution_status(task.id, "completed");
                            } else {
                                update_task_status(task.id, "pending");
                                update_task_execution_status(task.id, "failed");
                            }
                        }
                    } else {
                        eprintln!("Can't get the email context");
                        update_task_execution_status(task.id, "failed");
                        update_task_status(task.id, "pending");
                    }

                    println!("Task {:?} title {:?}", task.id, task.title);
                    tokio::time::sleep(Duration::from_millis(300)).await;
                    break;
                }
            }
        }
    }

    None
}

fn extract_email_address(from_field: &str) -> String {
    // Extracts the actual email address from strings like: "Twitter <<EMAIL>>"
    if let Some(start) = from_field.find('<') {
        if let Some(end) = from_field.find('>') {
            return from_field[start + 1..end].to_lowercase();
        }
    }
    from_field.to_lowercase()
}

fn is_hard_blocked_sender(from_field: &str) -> bool {
    let email = extract_email_address(from_field);
    let hard_blocked_prefixes = vec![
        "noreply",
        "no-reply",
        "donotreply",
        "newsletter",
        "mailer",
        "notifications",
        "digest",
        "promo",
        "ads",
        "bot",
        "bounce",
        "postmaster",
        "mailer-daemon"
    ];

    hard_blocked_prefixes
        .iter()
        .any(|prefix| (email.starts_with(prefix) || email.contains(&format!("{}@", prefix))))
}

pub async fn save_task(task: Task, username: String) -> Result<(), Box<dyn std::error::Error>> {
    let client = Client::builder().timeout(Duration::from_secs(30)).build()?;

    let api_url = "http://localhost:8080/api/tasks";

    let response = client
        .post(api_url)
        .header("Content-Type", "application/json")
        .body(
            json!({
                "id": task.id,
                "userId": username,
                "title": task.title,
                "category": task.category,
                "status": task.status,
                "priority": task.priority,
                "dueDate": task.due_date,
                "requiresSync": task.requires_sync,
                "queuePosition": task.queue_position,
                "executionAttempts": task.execution_attempts,
                "lastAttemptedAt": task.last_attempted_at,
                "rescheduleCount": task.reschedule_count,
                "createdAt": task.created_at,
                "shard": ""
            }).to_string()
        )
        .send().await?;

    if response.status().is_success() {
        println!("✅ Task saved successfully");
        Ok(())
    } else {
        let status = response.status();
        let error_body = response.text().await.unwrap_or_default();
        Err(format!("❌ Failed to save task. Status: {}, Body: {}", status, error_body).into())
    }
}

// // Function to call the Flask API
// pub async fn analyze_email(
//     subject: &str,
//     snippet: &str,
//     sender: &str,
// ) -> Result<AnalyzeEmailResponse, Box<dyn std::error::Error>> {
//     let client = Client::builder()
//         .timeout(Duration::from_secs(60)) // Ensures the request doesn't hang forever
//         .build()?;
//     // let api_url = "http://localhost:5000/analyze/email"; // Adjust to your Flask app's URL and port
//     let api_url = "http://ana.oway.life:8080/analyze/email"; // Updated API URL

//     // Prepare the payload
//     let payload = json!({
//         "subject": subject,
//         "snippet": snippet,
//     });

//     // Send the POST request
//     let response = client
//         .post(api_url)
//         .header("Content-Type", "application/json")
//         .json(&payload)
//         .send()
//         .await // Properly propagate the error using `?`
//         .map_err(|err| format!("Error sending request: {:?}", err))?;

//     // Check if the response was successful
//     if response.status().is_success() {
//         // Deserialize the JSON response into the struct
//         let response_json: serde_json::Value = response.json().await?;

//         // Manually construct AnalyzeEmailResponse
//         let analyze_response = AnalyzeEmailResponse {
//             summary: response_json["summary"]
//                 .as_str()
//                 .unwrap_or("Default summary")
//                 .to_string(),
//             priority: response_json["priority"]
//                 .as_str()
//                 .unwrap_or("Low")
//                 .to_string(),
//             actionable_items: response_json["actionable_items"]
//                 .as_str()
//                 .unwrap_or("No actionable items")
//                 .to_string(),
//             sentiment: Sentiment {
//                 label: response_json["sentiment"]["label"]
//                     .as_str()
//                     .unwrap_or("NEUTRAL")
//                     .to_string(),
//                 score: response_json["sentiment"]["score"].as_f64().unwrap_or(0.0) as f32,
//             },
//             urgency_score: response_json["urgency_score"].as_i64().unwrap_or(0) as i32,
//         };
//         Ok(analyze_response)
//     } else {
//         // Handle errors if the response is not successful
//         let status = response.status();
//         let body = response
//             .text()
//             .await
//             .unwrap_or_else(|_| "No body".to_string());
//         Err(format!(
//             "Failed to analyze email. Status: {}, Body: {}",
//             status, body
//         )
//         .into())
//     }
// }

pub async fn analyze_email(
    subject: &str,
    body: &str,
    sender_email: &str
) -> Result<AnalyzeEmailResponse, Box<dyn std::error::Error>> {
    let client = Client::builder().timeout(Duration::from_secs(60)).build()?;

    let api_url = "http://69.197.187.76:8000/email/classify_email";

    let payload =
        json!({
        "subject": subject,
        "email": body,
        "sender_email": sender_email
    });

    let response = client
        .post(api_url)
        .header("Content-Type", "application/json")
        .json(&payload)
        .send().await
        .map_err(|err| format!("Error sending request: {:?}", err))?;

    if response.status().is_success() {
        let response_json: serde_json::Value = response.json().await?;

        let analyze_response = AnalyzeEmailResponse {
            classification: response_json["classification"]
                .as_str()
                .unwrap_or_default()
                .to_string(),
            summary: response_json["summary"].as_str().unwrap_or_default().to_string(),
            priority: response_json["priority"].as_str().unwrap_or("Low").to_string(),
            actionable_items: response_json["actionable_items"]
                .as_array()
                .unwrap_or(&vec![])
                .iter()
                .filter_map(|item| item.as_str().map(|s| s.to_string()))
                .collect(),
            sentiment: response_json["sentiment"].as_str().unwrap_or("Neutral").to_string(),
            urgency_score: response_json["urgency_score"].as_f64().unwrap_or(0.0) as f32,
        };

        Ok(analyze_response)
    } else {
        let status = response.status();
        let body = response.text().await.unwrap_or_else(|_| "No body".to_string());
        Err(format!("Failed to analyze email. Status: {}, Body: {}", status, body).into())
    }
}

pub async fn fetch_all_unread_emails(
    app_data: Arc<RwLock<AppData>>,
    refresh_token: &str,
    mut access_token: &str,
    url: String,
    cancel_token: Arc<CancellationToken>
) -> Result<String, String> {
    println!("Let's start fetching emails with access token: {:#?}", access_token);

    let base_url = url.clone();
    let mut access_token = access_token.to_string(); // Initial assignment

    let progress_data = load_progress().unwrap_or_else(|_| json!({}));

    let saved_next_page_token = progress_data
        .get("unread_resume")
        .and_then(|v| v.get("nextPageToken"))
        .and_then(|v| v.as_str());

    // Construct the Gmail API URL, including the nextPageToken if present
    let mut gmail_api_url = if let Some(token) = saved_next_page_token {
        format!("{}?pageToken={}", url, token) // Append nextPageToken to the given URL
    } else {
        url // Start from the beginning if no nextPageToken is saved
    };

    let mut blacklisted_domains: HashSet<String> = HashSet::new(); // Initialize with an empty HashSet

    // let mut gmail_api_url = url;
    let client = Client::builder()
        .timeout(Duration::from_secs(180)) // Set a timeout to prevent hanging
        .build()
        .expect("Failed to build client"); // Or handle the error properly

    let mut latest_history_id = String::new(); // Store the latest historyId
    // Check if all emails have been fetched
    // if saved_all_emails_fetched {
    //     println!(
    //         "All emails have already been fetched. Exiting fetch. {}",
    //         saved_all_emails_fetched.clone()
    //     );

    //     // process_emails_with_flag(
    //     //     // summarization_model.clone(),
    //     //     // sentiment_model.clone(),
    //     //     // ner_model.clone(),
    //     // ).await;
    //     return Ok("All emails fetched previously".to_string());
    // }

    loop {
        // println!("Fetching email list from URL: {}", gmail_api_url);

        let response = client.get(&gmail_api_url).bearer_auth(access_token.clone()).send().await;

        match response {
            Ok(resp) => {
                println!("Received response for email list request");

                // Print the raw response for debugging
                let status = resp.status();
                let headers = resp.headers();
                // println!("Response Status: {:?}", status);
                // println!("Response Headers: {:?}", headers);

                // Attempt to parse the response body as JSON
                let json_resp: Value = match resp.json().await {
                    Ok(json) => {
                        // Print the parsed JSON response for debugging
                        println!("Parsed JSON Response: {:#?}", json);
                        json
                    }
                    Err(e) => {
                        eprintln!("Error parsing JSON response: {:?}", e);
                        break; // Exit the loop on parsing error
                    }
                };

                // Check if there is an error message indicating "Invalid pageToken"
                if let Some(error) = json_resp["error"]["message"].as_str() {
                    if error.contains("Invalid pageToken") {
                        // eprintln!("Invalid pageToken detected. Resetting pagination. Base URL: {}", base_url);
                        // Reset pagination and restart loop
                        gmail_api_url = base_url.clone();
                        continue;
                    }
                }

                // Save progress
                // let history_id = json_resp["historyId"].as_str().unwrap_or("");
                let next_page_token = json_resp["nextPageToken"].as_str();
                // save_progress(history_id, next_page_token, next_page_token.is_none());

                // Save the latest historyId from the response
                if let Some(history_id) = json_resp["historyId"].as_str() {
                    latest_history_id = history_id.to_string();
                }

                if let Some(messages) = json_resp["messages"].as_array() {
                    for message in messages.iter() {
                        if cancel_token.is_cancelled() {
                            println!("Fetch emails cancelled.");
                            return Ok("All emails are fetched".to_string());
                        }
                        if let Some(message_id) = message["id"].as_str() {
                            println!("Fetching details for Message ID: {}", message_id);

                            let email_response = match
                                client
                                    .get(format!("{}/{}", base_url, message_id))
                                    .bearer_auth(access_token.clone())
                                    .send().await
                            {
                                Ok(resp) => {
                                    if resp.status().is_success() {
                                        match resp.json::<Value>().await {
                                            Ok(json) => {
                                                // Log the full response for debugging
                                                println!(
                                                    "Full email response for Message ID {}: {:?}",
                                                    message_id,
                                                    json["historyId"]
                                                );
                                                let history_id = json["historyId"]
                                                    .as_str()
                                                    .unwrap_or("");
                                                save_progress_all_emails(
                                                    history_id,
                                                    next_page_token,
                                                    !next_page_token.is_none()
                                                );

                                                json
                                            }
                                            Err(e) => {
                                                eprintln!(
                                                    "Error parsing email response for Message ID {}: {:?}",
                                                    message_id,
                                                    e
                                                );
                                                continue; // Skip this email
                                            }
                                        }
                                    } else {
                                        let error_json: Value = resp
                                            .json().await
                                            .unwrap_or_default();
                                        eprintln!(
                                            "API Error Response for Message ID {}: {}",
                                            message_id,
                                            serde_json
                                                ::to_string_pretty(&error_json)
                                                .unwrap_or_else(|_| {
                                                    "Failed to parse error response".to_string()
                                                })
                                        );
                                        if
                                            let Some(error) =
                                                error_json["error"]["message"].as_str()
                                        {
                                            if error.contains("Invalid pageToken") {
                                                println!("Updating gmail_api_url to base URL {}", gmail_api_url);
                                                gmail_api_url = base_url.clone();
                                                println!("New gmail_api_url: {}", gmail_api_url); // Reset pagination by starting from the initial URL without the pageToken
                                                continue;
                                            } else if
                                                error.contains("Invalid Credentials") ||
                                                error_json["error"]["status"] == "UNAUTHENTICATED"
                                            {
                                                // If authentication failed, attempt to refresh the access token
                                                println!(
                                                    "Invalid credentials detected, refreshing access token..."
                                                );
                                                // match refresh_access_token(refresh_token).await {
                                                //     Ok(new_token) => {
                                                //         access_token = new_token.secret().clone(); // Update `access_token` safely
                                                //         println!(
                                                //             "Access token refreshed successfully."
                                                //         );
                                                //     }
                                                //     Err(e) => {
                                                //         eprintln!(
                                                //             "Failed to refresh access token: {:?}",
                                                //             e
                                                //         );
                                                //         break; // Exit loop if token refresh fails
                                                //     }
                                                // }
                                                match refresh_access_token(&refresh_token).await {
                                                    Ok(new_token) => {
                                                        // Step 4: Acquire write lock to update user data
                                                        let mut app_data_write =
                                                            app_data.write().await;
                                                        let mut user_data =
                                                            app_data_write.user_data.write().await;

                                                        user_data.access_token = Some(
                                                            oauth2::AccessToken::new(
                                                                new_token.clone()
                                                            )
                                                        );
                                                        user_data.set_issued_at(); // Update issued_at time
                                                        user_data.save_me(); // Save changes
                                                        access_token = new_token.clone();
                                                        println!(
                                                            "Access token refreshed and updated."
                                                        );
                                                    }
                                                    Err(err) => {
                                                        println!(
                                                            "Failed to refresh access token: {:?}",
                                                            err
                                                        );
                                                    }
                                                }
                                                continue; // Retry the request with the new token
                                            }
                                        }
                                        continue; // Skip to the next message
                                    }
                                }
                                Err(e) => {
                                    eprintln!(
                                        "Request error for Message ID {}: {:?}",
                                        message_id,
                                        e
                                    );
                                    continue; // Skip this email and move to the next one
                                }
                            };

                            // Check if the "id" field is present in the email response
                            if email_response.get("id").is_none() {
                                eprintln!(
                                    "ID not found in email response for Message ID {}: {:?}",
                                    message_id,
                                    email_response
                                );
                                continue; // Skip this email if the ID is not found
                            }

                            // Proceed with parsing the valid email response...
                            let email = match
                                parse_email_metadata(
                                    &email_response // summarization_model.clone(),
                                    // sentiment_model.clone(),
                                    // ner_model.clone(), // &blacklisted_domains,
                                ).await
                            {
                                Ok(email) => email,
                                Err(e) => {
                                    eprintln!(
                                        "Error parsing email metadata for Message ID {}: {:?}",
                                        message_id,
                                        e
                                    );
                                    continue; // Skip this email
                                }
                            };

                            println!(
                                "before store_email_metadata - Email sender: \n{:#?}",
                                &email.from
                            );

                            // emails.push(email);

                            // if emails.len() > 100 {
                            //     store_and_reset_emails(&mut emails);
                            //     emails.clear();
                            // }
                        } else {
                            eprintln!("Error: message['id'] is None");
                        }
                    }
                } else {
                    println!("No messages found in the current response");
                }

                // Handle pagination
                if let Some(next_page_token) = json_resp["nextPageToken"].as_str() {
                    gmail_api_url =
                        format!("https://www.googleapis.com/gmail/v1/users/me/messages?pageToken={}", next_page_token);
                } else {
                    println!("No more pages to fetch");
                    break;
                }
            }
            Err(e) => {
                eprintln!("Error sending request to Gmail API: {:?}", e);
                // Implement a retry mechanism with exponential backoff
                tokio::time::sleep(Duration::from_secs(2)).await; // Wait before retrying
                continue; // Retry the request
            }
        }
    }

    // process_emails_with_flag(
    //     // summarization_model.clone(),
    //     // sentiment_model.clone(),
    //     // ner_model.clone(),
    // ).await;
    // Return the latest historyId
    return Ok("All emails are fetched".to_string());
}

// Function to save progress in a single JSON file
pub fn save_progress_all_unread_emails(
    history_id: &str,
    next_page_token: Option<&str>,
    all_emails_fetched: bool
) {
    let progress_data =
        json!({
        "historyId": history_id,
        "nextPageToken": next_page_token.clone(),
        "allEmailsFetched": true,
         "unread_resume": {
            "nextPageToken": next_page_token,       // String
            "lastUpdated": chrono::Utc::now().to_rfc3339(), // Timestamp
            "inProgress": all_emails_fetched                // bool
        }
    });

    println!(
        "Saving progress - historyId: {}, nextPageToken: {:?}, allEmailsFetched: {}",
        history_id,
        next_page_token,
        all_emails_fetched
    );

    match get_progress_file_path() {
        Ok(file_path) => {
            match fs::File::create(&file_path) {
                Ok(mut file) => {
                    if let Err(e) = writeln!(file, "{}", progress_data.to_string()) {
                        eprintln!("Failed to write to progress file: {}", e);
                    } else {
                        println!("Progress saved to: {:?}", file_path);
                    }
                }
                Err(e) => {
                    eprintln!("Failed to create progress file at {:?}: {}", file_path, e);
                }
            }
        }
        Err(e) => {
            eprintln!("Failed to get progress file path: {}", e);
        }
    }
}

// Function to save progress in a single JSON file
pub fn save_progress_all_emails(
    history_id: &str,
    next_page_token: Option<&str>,
    all_emails_fetched: bool
) {
    let progress_data =
        json!({
        "historyId": history_id,
        "nextPageToken": next_page_token,
        "allEmailsFetched": all_emails_fetched,
         "unread_resume": {
            "nextPageToken": "".to_string(),       // String
            "lastUpdated": chrono::Utc::now().to_rfc3339(), // Timestamp
            "inProgress": !all_emails_fetched                // bool
        }
    });

    println!(
        "Saving progress - historyId: {}, nextPageToken: {:?}, allEmailsFetched: {}",
        history_id,
        next_page_token,
        all_emails_fetched
    );

    match get_progress_file_path() {
        Ok(file_path) => {
            match fs::File::create(&file_path) {
                Ok(mut file) => {
                    if let Err(e) = writeln!(file, "{}", progress_data.to_string()) {
                        eprintln!("Failed to write to progress file: {}", e);
                    } else {
                        println!("Progress saved to: {:?}", file_path);
                    }
                }
                Err(e) => {
                    eprintln!("Failed to create progress file at {:?}: {}", file_path, e);
                }
            }
        }
        Err(e) => {
            eprintln!("Failed to get progress file path: {}", e);
        }
    }
}

// Function to load progress from the JSON file
pub fn load_progress() -> Result<Value, Box<dyn Error>> {
    match get_progress_file_path() {
        Ok(file_path) => {
            // Check if the file exists
            if !file_path.exists() {
                println!("Progress file not found at {:?}. Skipping progress load.", file_path);
                return Ok(json!({})); // Return an empty JSON object if the file does not exist
            }

            // Read the file if it exists
            let data = fs::read_to_string(&file_path)?;
            let progress_data: Value = serde_json::from_str(&data)?;
            println!("Progress loaded from: {:?}", file_path);
            Ok(progress_data)
        }
        Err(e) => {
            eprintln!("Failed to get progress file path: {}", e);
            Ok(json!({})) // Return empty JSON on error
        }
    }
}

// Function to store emails and reset the list
fn store_and_reset_emails(emails: &mut Vec<Email>) {
    for email in emails.iter() {
        store_email_metadata(email);
    }
    // Clear the emails list after storing
}

// Function to store email metadata
fn store_email_metadata(email: &Email) -> Result<(), Box<dyn std::error::Error>> {
    // Insert the email metadata into the database
    emails_service::store_new_email(&email);

    Ok(())
}
