<template>
  <div
    ref="cardEl"
    v-if="props.event && props.event.pos && props.event.show"
    class="w-[140px] h-[118px] absolute bg-white shadow-[0px_0px_10px_0.1px_#805ad5] rounded z-20"
    :style="{
      top: `${Math.max(props.event.pos.y - 140, 10)}px`,
      left: `${Math.max(props.event.pos.x - 70, 10)}px`,
      boxShadow: `0px 0px 10px 0.1px ${props.event.color}`,
    }"
  >
    <div class="size-full flex flex-nowrap py-4 px-4 gap-2">
      <div
        class="w-0.5 h-full shadow-[0px_0px_10px_0.1px_#805ad5]"
        :style="{
          backgroundColor: props.event.color,
          boxShadow: `0px 0px 10px 0.1px ${props.event.color}`,
        }"
      ></div>
      <div>
        <div class="flex items-center gap-0.5">
          <Icon class="size-4" icon="solar:watch-square-minimalistic-linear" />
          <div class="text-[8px] text-nowrap">
            {{
              dayjs(props.event.data.startTime).format("hh:mm A") +
              "-" +
              dayjs(props.event.data.endTime).format("hh:mm A")
            }}
          </div>
        </div>
        <div class="text-xs font-semibold">{{ props.event.data.title }}</div>
        <div class="line-clamp-2 text-[10px]">
          {{ props.event.data.description }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { Icon } from "@iconify/vue";
import dayjs from "dayjs";

const props = defineProps({
  event: {
    type: Object,
    required: true,
  },
});

const cardEl = ref<HTMLDivElement>();

function getX() {
  return props.event.x - 60;
}

function getY() {
  return props.event.y - 190;
}
</script>

<style scoped></style>
