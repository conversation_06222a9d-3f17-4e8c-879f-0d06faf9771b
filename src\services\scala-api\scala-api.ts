import axios from "axios";


// Create axios instance with custom config
const ScalaApi = axios.create({
  baseURL: import.meta.env.VITE_SCALA_API_HOST,
  timeout: 100000,
  headers: {
    'Content-Type': 'application/json',
  }
});

// Add a request interceptor
// ScalaApi.interceptors.request.use(
//   (config) => {
//     const params = new URLSearchParams(config.params || {});

//     // params.append("apiKey", import.meta.env.VITE_CAL_API_KEY);
//     config.params = params;
//     //  console.table(params);
//     return config;
//   },
//   (error) => {
//     return Promise.reject(error);
//   }
// );

export default ScalaApi;
