import { invoke } from "@tauri-apps/api/core";


async function getFreshAccessToken() {
  try {
    return await invoke<string>("get_fresh_access_token");
  } catch (error) {
    console.error(error);
    return null;
  }
}

async function cleanupOldEmailsFromTodaysCategories(): Promise<number> {
  try {
    const result = await invoke<number>("cleanup_old_emails_from_today_categories");
    console.log(`🧹 Successfully cleaned up ${result} old emails from today's categories`);
    return result;
  } catch (error) {
    console.error("❌ Failed to cleanup old emails:", error);
    throw error;
  }
}

export interface SendEmail {
  recipient: string,
  cc?: string,
  bcc?: string,
  subject: string,
  body: string,
  attachments: string[],
  in_reply_to?: string,
  references?: string,
  thread_id?: string
}

async function sendEmail(email: SendEmail) {
  try {
    await invoke("send_email", email as any);
    return true;
  } catch (error) {
    console.error(error);
    return false;
  }
}


const emailCommands = {
  getFreshAccessToken,
  cleanupOldEmailsFromTodaysCategories,
  sendEmail,
};

export default emailCommands;
