<template>
  <label :for="props.id">
    <div
      :class="currentValue ? 'bg-highlight-500' : 'bg-secondary-300'"
      class="w-10 h-5 rounded-full cursor-pointer transition-colors duration-200"
    >
      <input type="checkbox" :id="props.id" class="hidden peer" v-model="currentValue" />
      <div
        :class="currentValue ? 'translate-x-full bg-primary' : 'translate-x-0 bg-secondary-600'"
        class="size-5 rounded-full transition-all duration-200"
      ></div>
    </div>
  </label>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  value: {
    type: <PERSON>olean,
  },
  // modelValue: {
  //   type: Boolean,
  // },
});

const currentValue = ref<boolean>(props.value);
const emit = defineEmits<{
  (e: "changed", value: boolean): void;
}>();
// const emit = defineEmits(["changed", "update:modelValue"]);

watch(currentValue, (value) => {
  emit("changed", value);
  // emit("update:modelValue", value);
});
</script>

<style scoped></style>
