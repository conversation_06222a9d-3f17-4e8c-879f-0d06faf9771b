<script lang="ts">
import { defineComponent, ref, onMounted, Transition } from "vue";
import { useRouter } from "vue-router";
import { invoke } from "@tauri-apps/api/core";
import { getCurrentWebviewWindow } from "@tauri-apps/api/webviewWindow";
import PageHeader from "@/components/PageHeader.vue";
import { CalCreateUser } from "../../models/user";
import { calUsersService } from "../../services/user-cal-service";
const appWindow = getCurrentWebviewWindow();

export default defineComponent({
  components: { PageHeader },

  setup() {
    const loading = ref(false);
    const me = ref({ email: "", name: "", picture: "" });
    const title = ref("Tari + OAuth Example");
    const router = useRouter();
    const email = ref("");
    const showEmailInput = ref(false);
    const emailError = ref("");
    const isEmailValid = ref(false);
    const username = ref("");
    const password = ref("");
    const showAuthForm = ref(false);
    const usernameError = ref("");
    const passwordError = ref("");
    const isFormValid = ref(false);

    // Validation functions
    const validateUsername = () => {
      const trimmedUsername = username.value.trim();
      usernameError.value = !trimmedUsername
        ? "Username is required."
        : trimmedUsername.length < 3
        ? "Username must be at least 3 characters."
        : "";
      checkFormValidity();
    };

    const validateEmail = () => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      isEmailValid.value = emailRegex.test(email.value);
      emailError.value = isEmailValid.value ? "" : "Please enter a valid email address.";
    };

    const validatePassword = () => {
      passwordError.value = !password.value
        ? "Password is required."
        : password.value.length < 6
        ? "Password must be at least 6 characters."
        : "";
      checkFormValidity();
    };

    const checkFormValidity = () => {
      isFormValid.value = !usernameError.value && !passwordError.value;
    };

    // Sign-in functions
    const signIn = async () => {
      const sanitizedUsername = username.value.trim();
      const sanitizedPassword = password.value;
      try {
        loading.value = true;

        // Call the backend for authentication
        const result = await invoke("authenticate_user", {
          username: sanitizedUsername,
          password: sanitizedPassword,
        });
        const data = JSON.parse(result);
        //  console.log(data);

        // Handle success and various error scenarios
        if (data.success) {
          alert("Authentication successful!");
          //  console.log("Authentication successful!");
          //  console.log(result);
          // Fetch emails and redirect
          // await invoke("fetch_all_emails", { message: "fetch_emails" });
          //  console.log("DEBUG:", result);

          const parsed = JSON.parse(result); // result is a JSON string

          //  console.log("Parsed:", parsed);

          if (parsed.is_paid) {
            //  console.log("✅ User has paid. Redirecting to /");
            router.replace("/");
          } else {
            //  console.log("🚫 User has NOT paid. Redirecting to /subscribe");
            router.replace("/subscribe");
          }
        } else {
          // Handle specific errors based on backend response
          switch (data.error_code) {
            case "USER_NOT_FOUND":
              alert("The username does not exist. Please check and try again.");
              break;
            case "INCORRECT_PASSWORD":
              alert("The password is incorrect. Please try again.");
              break;
            case "ACCOUNT_LOCKED":
              alert("Your account is locked due to multiple failed attempts. Please contact support.");
              break;
            case "SERVER_ERROR":
              alert("A server error occurred. Please try again later.");
              break;
            default:
              alert(`Authentication failed: ${data.error || "Unknown error."}`);
          }
        }
      } catch (error) {
        console.error("Error during authentication:", error);
        alert("An unexpected error occurred. Please try again.");
      } finally {
        loading.value = false;
      }
    };

    const signInWithEmail = async () => {
      if (!isEmailValid.value) {
        emailError.value = "Please provide a valid email.";
        return;
      }

      try {
        const response = await fetch("http://127.0.0.1:8000/api/login/", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ email: email.value }),
        });

        if (response.ok) {
          const data = await response.json();
          //  console.log("Email sent:", data.message);
        } else {
          const errorData = await response.json();
          emailError.value = errorData.error || "Failed to send email.";
        }
      } catch (error) {
        console.error("Error signing in with email:", error);
        emailError.value = "An error occurred. Please try again later.";
      }
    };

    const on_login = async () => {
      loading.value = true;

      try {
        const data: any = await invoke("js2rs", { message: "get_user" });
        const meData = JSON.parse(data);
        //  console.log("JS2RS SIGNIN=>", meData);

        meData.value = {
          name: meData.name || "",
          email: meData.email || "",
        };
        const calUser: CalCreateUser = {
          email: meData.email,
          username: meData.name,
        };

        // const userIfExist = await calUsersService.getUserByEmail(meData.email);
        // if (userIfExist) {
        //   //  console.table(userIfExist);
        // } else {
        //   const createdUser = await calUsersService.createUser({ email: meData.email, username: meData.username });

        //   // const createdUser = await calUsersService.createUser({ email: "<EMAIL>", username: "test1" });
        //   if (createdUser) //  console.table(createdUser);
        //   else console.error("Error creating user");
        // }

        router.push({
          path: "password",
          query: {
            name: meData.name || "",
            email: meData.email || "",
          },
        });
      } catch (err) {
        console.error("Error during login:", err);
      } finally {
        loading.value = false;
      }
    };

    const on_logout = async () => {
      try {
        const data: any = await invoke("js2rs", { message: "logout" });
        me.value = JSON.parse(data);
      } catch (err) {
        console.error("Error during logout:", err);
      } finally {
        loading.value = false;
      }
    };

    const on_end = async () => {
      await appWindow.close();
    };

    onMounted(() => {
      //  console.log("MainLayout mounted()");
      // Uncomment the line below if you want to call on_login on mount
      // on_login();
    });

    return {
      loading,
      me,
      title,
      email,
      showEmailInput,
      emailError,
      isEmailValid,
      username,
      password,
      usernameError,
      passwordError,
      isFormValid,
      showAuthForm,
      validateUsername,
      validateEmail,
      validatePassword,
      checkFormValidity,
      signIn,
      signInWithEmail,
      on_login,
      on_logout,
      on_end,
    };
  },
});
</script>

<style scoped>
/* Button styling */
.button {
  border: none;
  color: white;
  padding: 15px 32px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 4px 2px;
  cursor: pointer;
}

/* Background styling */
.app-background {
  background-color: #ede6dc;
}

/* Auth container styling */
.auth-container {
  max-width: 400px;
  margin: 0 auto;
}

/* Transition styling for slide-fade */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateY(-10px);
  opacity: 0;
}

.slide-fade-enter-to,
.slide-fade-leave-from {
  transform: translateY(0);
  opacity: 1;
}

/* Email input section styling */
.email-input-section {
  display: flex;
  flex-direction: column;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>

<template>
  <div class="size-full flex flex-row items-center justify-center">
    <!-- Login Box -->
    <div class="size-full relative">
      <div class="text-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">
          Sign in to <span style="font-weight: bold; color: #1c3d48">O</span><span style="color: #5a6e7c">WAY</span>
        </h2>
        <p class="text-sm text-gray-500">OWAY.AI</p>
      </div>

      <button
        type="button"
        @click="on_login()"
        class="w-full flex justify-center items-center gap-x-2 mb-4 rounded-md bg-white border border-gray-300 px-4 py-2.5 text-sm font-semibold text-gray-600 shadow-sm hover:bg-gray-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
      >
        <img
          src="https://www.google.com/images/branding/product/ico/googleg_lodp.ico"
          class="h-5 w-5"
          alt="Google Logo"
        />
        Sign in with Google
      </button>

      <div class="auth-container">
        <!-- Sign-in Button -->
        <button
          type="button"
          @click="showAuthForm = !showAuthForm"
          class="w-full rounded-md bg-purple-700 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-purple-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-purple-700"
        >
          Sign in with Username & Password
        </button>

        <!-- Collapsible Auth Form Section -->
        <transition name="slide-fade">
          <div v-if="showAuthForm" class="auth-form mt-4">
            <!-- Username Input -->
            <input
              v-model="username"
              type="text"
              placeholder="Enter your username"
              class="w-full rounded-md border px-4 py-2 text-sm"
              @input="validateUsername"
            />
            <p v-if="usernameError" class="text-red-600 text-sm mt-2">{{ usernameError }}</p>

            <!-- Password Input -->
            <input
              v-model="password"
              type="password"
              placeholder="Enter your password"
              class="w-full rounded-md border px-4 py-2 text-sm mt-4"
              @input="validatePassword"
            />
            <p v-if="passwordError" class="text-red-600 text-sm mt-2">{{ passwordError }}</p>

            <!-- Submit Button -->
            <button
              type="button"
              @click="signIn"
              :disabled="!isFormValid || loading"
              class="mt-4 w-full rounded-md bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:opacity-50"
            >
              Continue
            </button>
          </div>
        </transition>
      </div>

      <div class="text-center mt-6 text-sm text-gray-500">
        We’ll email you a magic code for a password-free sign in. Or you can
        <a href="#" class="text-indigo-600 hover:underline">sign in with a password instead.</a>
      </div>
    </div>
  </div>
  <div>
    <!-- <button
      v-if="me.email == ''"
      type="button"
      @click="on_login()"
      class="relative min-w-[86px] min-h-[36px] inline-flex justify-center items-center gap-x-2 rounded-md bg-indigo-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
    >
      Log in
    </button> -->
  </div>
</template>
