<template>
  <div
    :class="`relative flex flex-col h-full overflow-hidden transition-all duration-300 min-w-12 w-12 border-r-[1px] border-r-black/5`"
  >
    <div class="w-full flex justify-center p-4 items-center">
      <h1 class="text-5xl font-bold text-gray-700 cursor-pointer">O</h1>
    </div>

    <nav class="flex flex-col">
      <ul class="w-full flex flex-col gap-4">
        <li
          v-for="link in links"
          :key="link.path"
          :class="`py-1 px-2 transition-colors duration-300  ${isActive(link.path) ? 'bg-primary/5' : ''}`"
        >
          <router-link :to="link.path" class="w-full flex gap-2 items-center" :class="{ active: isActive(link.path) }">
            <div
              :class="`min-w-8 min-h-8  flex justify-center items-center rounded-md transition-colors duration-300 inset-shadow-sm ${
                isActive(link.path)
                  ? 'bg-primary-600  text-white'
                  : 'bg-primary/5x shadow-[inset_0px_2px_8px_8px_#9E9E9E1A] text-dark-500 xtext-base'
              }`"
            >
              <component :is="link.icon" class="size-5" />
            </div>
            <div
              :class="`overflow-hidden text-nowrap transition-all duration-300 ${
                isActive(link.path) ? 'text-dark-500 xtext-primary-900 font-bold' : 'text-dark-500 xtext-base'
              } ${isOpen ? 'w-auto' : 'w-0'}`"
            >
              {{ link.name }}
            </div>
          </router-link>
        </li>
      </ul>
    </nav>

    <!-- Bottom Icons -->
    <div class="absolute inset-x-0 bottom-0 flex flex-col items-center justify-between px-2 pb-3 gap-3">
      <router-link
        to="/userprofile"
        class="w-full flex gap-2 items-center"
        :class="{ active: isActive('/userprofile') }"
      >
        <div
          :class="`min-w-8 min-h-8  flex justify-center items-center rounded-full transition-colors duration-300 ${
            isActive('/userprofile') ? 'bg-primary-700 text-primary-500' : 'bg-primary-600/20 text-base'
          }`"
        >
          <UserCircleIcon v-if="!currentUserStore.currentUser?.picture" class="size-7" />
          <img
            v-if="currentUserStore.currentUser?.picture"
            :src="currentUserStore.currentUser?.picture"
            class="size-8 rounded-full"
          />
        </div>
        <div
          :class="`overflow-hidden text-nowrap transition-all duration-300 font-semibold ${
            isActive('/userprofile') ? 'text-primary-900 font-bold' : 'text-dark-700 xtext-base'
          } ${isOpen ? 'w-auto' : 'w-0'}`"
          v-if="currentUserStore.currentUser?.name"
        >
          {{ currentUserStore.currentUser?.name }}
        </div>
      </router-link>

      <button
        @click="openSettings"
        class="size-8 rounded-full flex justify-center items-center transition-colors duration-300 bg-primary/5x shadow-[inset_0px_2px_8px_8px_#9E9E9E1A] text-dark-500 hover:bg-secondary/20"
      >
        <Cog8ToothIcon class="w-6 h-6" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import utc from "dayjs/plugin/utc";
import { computed, onMounted, ref } from "vue";
import {
  CalendarDateRangeIcon,
  ListBulletIcon,
  PaperAirplaneIcon,
  PhoneIcon,
  UserCircleIcon,
} from "@heroicons/vue/24/outline";
import { InboxStackIcon, Cog8ToothIcon } from "@heroicons/vue/24/outline";
import { useRoute } from "vue-router";
import { useCurrentUserStore } from "../../stores/currentUser";
import settingsCmd from "../../commands/settings";

dayjs.extend(relativeTime);
dayjs.extend(utc);

const route = useRoute();
const currentUserStore = useCurrentUserStore();

const links = ref([
  {
    name: "Emails",
    icon: InboxStackIcon,
    path: "/emails",
  },
  {
    name: "Send",
    icon: PaperAirplaneIcon,
    path: "/send",
  },
  {
    name: "Calendar",
    icon: CalendarDateRangeIcon,
    path: "/calendar",
  },
  // {
  //   name: "Tasks",
  //   icon: ListBulletIcon,
  //   path: "/calendar/tasks",
  // },
  {
    name: "Operator",
    icon: PhoneIcon,
    path: "/operator",
  },
]);

const currentPath = computed(() => route.path);
const isOpen = ref(false);

const isActive = (path: string) => {
  return currentPath.value === path;
};

const toggleSidebarOpen = () => {
  isOpen.value = !isOpen.value;
};

const openSettings = async () => await settingsCmd.openSettingsWindow();
onMounted(async () => {});
</script>
<style scoped></style>
