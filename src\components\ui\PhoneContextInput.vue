<template>
  <div class="py-1 flex flex-col gap-2 max-h-full overflow-y-auto">
    <div
      v-for="([key, value], index) in Object.entries(phoneCallContext!)"
      :key="index"
      class="flex justify-between items-center"
    >
      <div v-if="!['tonePreference', 'keyPoints'].includes(key)" class="flex justify-between items-end w-full">
        <div class="flex flex-col grow">
          <div class="text-xs text-primary-900">{{ getNameFromAttr(key) }}</div>
          <div class="text-sm font-semibold" v-if="!keysOnEdit.includes(key)">{{ getValue(value) }}</div>
          <div v-else>
            <input
              type="text"
              class="h-6 bg-secondary-200 border-none rounded-md p-1 drop-shadow-sm w-full"
              :value="value"
              @input="(e:any) => updatePhoneCallContext(key, value, e.target.value)"
            />
          </div>
        </div>
        <div class="flex items-end justify-center h-full">
          <div v-if="!keysOnEdit.includes(key)">
            <button
              @click="() => keysOnEdit.push(key)"
              class="hover:bg-secondary-200 size-6 flex justify-center items-center rounded-md transition-colors duration-200"
            >
              <PencilIcon class="size-4" />
            </button>
          </div>
          <div class="flex gap-0.5" v-else>
            <button
              @click="() => removeEditKey(key)"
              class="hover:bg-green-100 text-green-700 size-6 flex justify-center items-center rounded-md transition-colors duration-200"
            >
              <CheckIcon class="size-4" />
            </button>
            <button
              @click="
                () => {
                  updatePhoneCallContext(key, value, getValue(props.data![key]));
                  removeEditKey(key);
                }
              "
              class="hover:bg-red-100 text-red-700 size-6 flex justify-center items-center rounded-md transition-colors duration-200"
            >
              <XMarkIcon class="size-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from "vue";
import { PhoneCallContext } from "../../models/phone-context";
import { CheckIcon, PencilIcon, XMarkIcon } from "@heroicons/vue/24/outline";

// Props
const props = defineProps({
  data: Object,
});

// Reactive data
const phoneCallContext = ref<PhoneCallContext>(props.data as PhoneCallContext);
const keysOnEdit = ref<string[]>([]);

// Watch for changes
watch(
  phoneCallContext,
  (newVal) => {
    //  console.log("Phone Call Context updated:", newVal);
  },
  { deep: true }
);

// Helpers
function getNameFromAttr(attr: string) {
  const splitedName = attr.toLowerCase().split("_");
  splitedName[0] = splitedName[0][0].toUpperCase() + splitedName[0].substring(1);
  return splitedName.join(" ");
}

function getValue(value: any) {
  if (Array.isArray(value)) return value.join(", ");
  else return value;
}

function removeEditKey(key: string) {
  keysOnEdit.value = keysOnEdit.value.filter((k) => k !== key);
}

function updatePhoneCallContext(key: keyof PhoneCallContext, oldValue: any, newValue: any) {
  //  console.log("Updating key:", key, "from", oldValue, "to", newValue);
  phoneCallContext.value = {
    ...phoneCallContext.value,
    [key]: Array.isArray(oldValue) ? newValue.split(",") : newValue,
  };
}
</script>

<style scoped>
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: var(--tw-color-gray-300) var(--tw-color-gray-100);
}
</style>
