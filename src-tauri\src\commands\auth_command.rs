use crate::models::{ cal_user::<PERSON><PERSON><PERSON>, user_data::UserData, app_data::AppData };
use std::sync::Arc;
use tauri::Emitter;
use tokio::sync::RwLock;

#[tauri::command]
pub async fn get_local_user_info() -> Result<UserData, String> {
    Ok(UserData::init_user_data().await)
}

#[tauri::command]
pub async fn update_cal_info(
    handle: tauri::AppHandle,
    app_data: tauri::State<'_, Arc<RwLock<AppData>>>,
    info: CalUser
) -> Result<UserData, String> {
    // Acquire write lock on app_data
    let mut app_data_arc = app_data.write().await;

    // Acquire write lock on user_data
    let mut user_data = app_data_arc.user_data.write().await;

    // Update cal_info using the set_cal_info method
    user_data.set_cal_info(Some(info));

    handle.emit("user:update", "").unwrap();

    // Return the updated user_data
    Ok(user_data.clone())
}
