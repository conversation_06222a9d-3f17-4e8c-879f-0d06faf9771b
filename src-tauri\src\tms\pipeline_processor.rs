use diesel::prelude::*;
use crate::db::establish_db_connection;
use crate::models::task::Task;
use crate::schema::tasks::dsl::*;
use crate::services::tasks_services::{task_services, agent_services, ai_services};
use chrono::Utc;

/// Process and assign pending tasks
pub fn process_pending_tasks() {
    // let connection = &mut establish_db_connection();
    let  connection = &mut get_pooled_connection();


    let pending_tasks = tasks
        .filter(status.eq("pending"))
        .order_by((
            queue_position.asc(), // Prioritize by queue position
            priority.desc(), // Urgent tasks first
            due_date.asc(), // Earliest due date first
        ))
        .load::<Task>(connection)
        .expect("Error fetching pending tasks");

    for task in pending_tasks {
        if ai_services::can_execute(&task) {
            ai_services::execute_task(&task);
            task_services::mark_task_completed(task.id);
        } else {
            if let Some(agent) = agent_services::find_available_agent(&task) {
                task_services::assign_task_to_agent(task.id, &agent);
            } else {
                task_services::enqueue_task(task.id, false);
            }
        }
    }
}

pub fn can_execute(task: &Task) -> bool {
    match task.category.as_deref() {
        Some("email") if task.sub_type.as_deref() == Some("reply_email") => true,
        Some("calendar") if task.sub_type.as_deref() == Some("schedule_meeting") => true,
        Some("phone_call") if task.sub_type.as_deref() == Some("initiate_call") => true,
        _ => false,
    }
}

pub fn find_available_agent(task: &Task) -> Option<String> {
    // let connection = &mut establish_db_connection();
    let  connection = &mut get_pooled_connection();


    let agent = agents::table
        .filter(availability.eq(true))
        .filter(skillset.like(format!("%{}%", task.category.as_deref().unwrap_or_default())))
        .order_by(current_task_count.asc())
        .select(agent_id)
        .first::<String>(connection)
        .ok();

    agent
}
pub fn enqueue_task(task_id: i32, is_subtask: bool) {
    // let connection = &mut establish_db_connection();
    let  connection = &mut get_pooled_connection();


    let max_position: Option<i32> = tasks
        .select(diesel::dsl::max(queue_position))
        .first(connection)
        .ok();

    let new_position = max_position.unwrap_or(0) + 1;

    diesel::update(tasks.filter(id.eq(task_id)))
        .set((
            queue_position.eq(new_position),
            status.eq("queued"),
        ))
        .execute(connection)
        .expect("Error updating queue position");
}

pub fn escalate_task(task_id: i32) {
    // let connection = &mut establish_db_connection();
    let  connection = &mut get_pooled_connection();


    let task = tasks
        .filter(id.eq(task_id))
        .first::<Task>(connection)
        .expect("Task not found");

    if let Some(agent) = agent_services::find_available_agent(&task) {
        task_services::assign_task_to_agent(task.id, &agent);
    } else if ai_services::can_execute(&task) {
        ai_services::execute_task(&task);
    } else {
        println!("Escalation required: Task {} has no available agent or AI execution.", task.id);
    }
}

pub fn send_heartbeat(agent_id: i32) {
    // let connection = &mut establish_db_connection();
    let  connection = &mut get_pooled_connection();


    let current_time = Utc::now().to_rfc3339();

    diesel::update(tasks.filter(assigned_to.eq(agent_id.to_string())))
        .set(last_attempted_at.eq(Some(current_time)))
        .execute(connection)
        .expect("Error updating agent heartbeat");
}

pub fn check_agent_status() {
    // let connection = &mut establish_db_connection();
    let  connection = &mut get_pooled_connection();

    let five_minutes_ago = Utc::now() - chrono::Duration::minutes(5);

    let inactive_tasks: Vec<i32> = tasks
        .filter(last_attempted_at.is_null().or(last_attempted_at.lt(Some(five_minutes_ago.to_rfc3339()))))
        .select(id)
        .load::<i32>(connection)
        .expect("Error fetching tasks from inactive agents");

    for task_id in inactive_tasks {
        reassign_task(task_id);
    }
}

pub fn reassign_task(task_id: i32) {
    // let connection = &mut establish_db_connection();
    let  connection = &mut get_pooled_connection();


    if let Some(new_agent) = find_available_agent() {
        diesel::update(tasks.filter(id.eq(task_id)))
            .set(assigned_to.eq(new_agent))
            .execute(connection)
            .expect("Error reassigning task");
    } else {
        println!("No available agents. Task {} remains unassigned.", task_id);
    }
}

pub fn find_available_agent() -> Option<String> {
    // let connection = &mut establish_db_connection();
    let  connection = &mut get_pooled_connection();


    let available_agent = tasks
        .filter(status.eq("in_progress")) // Find active agents
        .group_by(assigned_to)
        .order_by(diesel::dsl::count(assigned_to).asc()) // Find least busy agent
        .select(assigned_to)
        .first::<Option<String>>(connection)
        .ok()
        .flatten();

    available_agent
}

pub fn start_agent_monitoring() {
    thread::spawn(|| loop {
        check_agent_status();
        thread::sleep(Duration::from_secs(60));
    });
}