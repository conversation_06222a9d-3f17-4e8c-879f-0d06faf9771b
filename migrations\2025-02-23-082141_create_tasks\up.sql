-- Create Tasks table
CREATE TABLE IF NOT EXISTS tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
    title TEXT NOT NULL,
    category TEXT CHECK( category IN ('email', 'calendar', 'phone_call') ),
    sub_type TEXT CHECK( sub_type IN ('reply_email', 'follow_up_meeting', 'call_back') ),
    status TEXT CHECK( status IN ('pending', 'waiting_for_response', 'in_progress', 'completed', 'overdue', 'extended') ),
    priority TEXT CHECK( priority IN ('low', 'medium', 'high', 'urgent') ),
    linked_entity TEXT, -- E.g., Email ID, Meeting ID, Call ID
    context_type TEXT, -- Context of the task (JSON type support in Rust)
    context_data TEXT, -- JSON-encoded metadata (email thread, meeting notes)
    due_date TEXT, -- Task deadline
    assigned_to TEXT, -- User/AI assigned to task
    requires_sync BOOLEAN DEFAULT FALSE, -- If it needs to sync with a server
    queue_position INTEGER DEFAULT NULL, -- Task queue position for prioritization
    snoozed_until TEXT DEFAULT NULL, -- If snoozed, store when it will resume
    auto_executable BOOLEAN DEFAULT FALSE, -- If AI can auto-execute this task
    execution_status TEXT CHECK( execution_status IN ('not_started', 'in_progress', 'failed', 'completed') ) DEFAULT 'not_started', -- Tracks execution process
    execution_attempts INTEGER DEFAULT 0, -- How many times AI tried to execute this
    last_attempted_at TEXT DEFAULT NULL, -- Last AI execution attempt timestamp
    reschedule_count INTEGER DEFAULT 0, -- Number of times a task was rescheduled
    created_at TEXT DEFAULT CURRENT_TIMESTAMP -- Task creation timestamp
);

-- Create subtasks table
CREATE TABLE IF NOT EXISTS subtasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
    parent_task_id INTEGER NOT NULL, -- Links to a task
    title TEXT NOT NULL,
    status TEXT CHECK( status IN ('pending', 'in_progress', 'completed', 'overdue') ),
    priority TEXT CHECK( priority IN ('low', 'medium', 'high', 'urgent') ),
    assigned_to TEXT,
    due_date TEXT,
    requires_sync BOOLEAN DEFAULT FALSE,
    queue_position INTEGER DEFAULT NULL, -- Prioritization in subtask queue
    snoozed_until TEXT DEFAULT NULL, -- Snooze specific subtasks
    auto_executable BOOLEAN DEFAULT FALSE, -- AI execution capability
    execution_status TEXT CHECK( execution_status IN ('not_started', 'in_progress', 'failed', 'completed') ) DEFAULT 'not_started', -- Execution tracking
    execution_attempts INTEGER DEFAULT 0, -- AI retry attempts
    last_attempted_at TEXT DEFAULT NULL, -- Last execution attempt
    reschedule_count INTEGER DEFAULT 0, -- Number of times deferred
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_task_id) REFERENCES tasks(id) ON DELETE CASCADE
);