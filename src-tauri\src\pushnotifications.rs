use serde::Serialize;
use tauri::Manager;

#[derive(<PERSON><PERSON><PERSON>, <PERSON>lone)]
pub struct NotificationPush {
    pub r#type: NotificationPushType,
    pub title: String,
    pub body: String,
}

#[derive(Clone)]
pub enum NotificationPushType {
    Info,
    Success,
    Warning,
    Error,
}

impl NotificationPush {
    pub fn info(app_handle: tauri::AppHandle, title: &str, body: &str) {
        NotificationPush::notify(app_handle, NotificationPushType::Info, title, body);
    }

    pub fn success(app_handle: tauri::AppHandle, title: &str, body: &str) {
        NotificationPush::notify(app_handle, NotificationPushType::Success, title, body);
    }

    pub fn warning(app_handle: tauri::AppHandle, title: &str, body: &str) {
        NotificationPush::notify(app_handle, NotificationPushType::Warning, title, body);
    }

    pub fn error(app_handle: tauri::AppHandle, title: &str, body: &str) {
        NotificationPush::notify(app_handle, NotificationPushType::Error, title, body);
    }

    fn notify(app_handle: tauri::AppHandle, r#type: NotificationPushType, title: &str, body: &str) {
        app_handle
            .emit("NotificationPush", Self {
                r#type,
                title: title.to_string(),
                body: body.to_string(),
            })
            .unwrap();
    }
}

impl NotificationPushType {
    pub fn as_str(&self) -> &str {
        match self {
            NotificationPushType::Info => "info",
            NotificationPushType::Success => "success",
            NotificationPushType::Warning => "warning",
            NotificationPushType::Error => "error",
        }
    }
}

impl Serialize for NotificationPushType {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error> where S: serde::Serializer {
        serializer.serialize_str(self.as_str())
    }
}
