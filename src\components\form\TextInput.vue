<script setup lang="ts">
interface Props {
  label: string;
  name: string;
  id: string;
  placeholder: string;
  required?: boolean;
  modelValue: string;
}

const props = withDefaults(defineProps<Props>(), {
  required: false
})
</script>


<template>
  <div>
    <label :for="props.id" class="block text-sm font-medium leading-6 text-gray-900">
      {{ label }}
    </label>
    <div class="mt-2">
      <input
        :name="name"
        :id="props.id"
        :required="required"
        class="block px-2 w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
        :placeholder="placeholder"
        :value="modelValue"
        @input="$emit('update:modelValue', ($event.target as HTMLInputElement).value)"
      />
    </div>
  </div>
</template>