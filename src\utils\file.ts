const extensionIconMap: { [ext: string]: string } = {
  // Documents
  'pdf': 'fa-file-pdf text-red-500',
  'doc': 'fa-file-word text-blue-500',
  'docx': 'fa-file-word text-blue-500',
  'txt': 'fa-file-lines text-gray-500',
  'rtf': 'fa-file-lines text-gray-500',

  // Spreadsheets
  'xls': 'fa-file-excel text-green-500',
  'xlsx': 'fa-file-excel text-green-500',
  'csv': 'fa-file-csv text-green-500',

  // Presentations
  'ppt': 'fa-file-powerpoint text-orange-500',
  'pptx': 'fa-file-powerpoint text-orange-500',

  // Images
  'jpg': 'fa-file-image text-purple-500',
  'jpeg': 'fa-file-image text-purple-500',
  'png': 'fa-file-image text-purple-500',
  'gif': 'fa-file-image text-purple-500',
  'svg': 'fa-file-image text-purple-500',

  // Audio
  'mp3': 'fa-file-audio text-pink-500',
  'wav': 'fa-file-audio text-pink-500',
  'm4a': 'fa-file-audio text-pink-500',

  // Video
  'mp4': 'fa-file-video text-indigo-500',
  'mov': 'fa-file-video text-indigo-500',
  'avi': 'fa-file-video text-indigo-500',

  // Code
  'html': 'fa-file-code text-amber-500',
  'css': 'fa-file-code text-amber-500',
  'js': 'fa-file-code text-amber-500',
  'jsx': 'fa-file-code text-amber-500',
  'vue': 'fa-file-code text-amber-500',
  'php': 'fa-file-code text-amber-500',
  'py': 'fa-file-code text-amber-500',

  // Archives
  'zip': 'fa-file-zipper text-yellow-500',
  'rar': 'fa-file-zipper text-yellow-500',
  '7z': 'fa-file-zipper text-yellow-500',
  'tar': 'fa-file-zipper text-yellow-500',
  'gz': 'fa-file-zipper text-yellow-500'
}

export const getFileIcon = (filename: string) => {
  if (!filename) return 'fa-file' // Default icon

  // Get the file extension
  const extension = filename.split('.').pop()?.toLowerCase()

  // Return the mapped icon or default if extension not found
  return extension ? extensionIconMap[extension] || 'fa-file' : 'fa-file'
}


export interface AttachmentFile {
  name: string,
  extension: string,
  path: string
}

export function parseFilePath(filePath: string): AttachmentFile {
  const normalizedPath = filePath.replace(/\\/g, '/');

  // Extract the file name
  const name = normalizedPath.split('/').pop() || '';

  // Extract the file extension
  const extension = name.split('.').pop() || '';

  return {
    name,
    path: filePath,
    extension,
  };
}
