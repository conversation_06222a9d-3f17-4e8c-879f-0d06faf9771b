


// interface Location {
//   name: string,
//   region: string,
//   country: string,
//   localtime: string
// }

// interface CurrentWeather {
//   humidity: number,
//   cloud: 89,
//   temp_c: number,
//   temp_f: number,
//   condition: {
//     text: string,
//     icon: string
//   },
//   is_day: number,
// }

// interface Forecastday {
//   date: string,
//   day: {
//     maxtemp_c: number,
//     maxtemp_f: number,
//     mintemp_c: number,
//     mintemp_f: number,
//     avgtemp_c: number,
//     avgtemp_f: number,
//     condition: {
//       text: string,
//       icon: string
//     },
//   }
// }


// export interface WeatherForecast {
//   location: Location,
//   current: CurrentWeather,
//   forcast: {
//     forecastday: Forecastday[]
//   }
// }


class Location {
  name: string;
  region: string;
  country: string;
  localtime: string;

  constructor(name: string, region: string, country: string, localtime: string) {
    this.name = name;
    this.region = region;
    this.country = country;
    this.localtime = localtime;
  }
}

class CurrentWeather {
  humidity: number;
  cloud: number = 89;
  temp_c: number;
  temp_f: number;
  condition: {
    text: string;
    icon: string;
  };
  is_day: number;

  constructor(
    humidity: number,
    temp_c: number,
    temp_f: number,
    condition: { text: string; icon: string },
    is_day: number
  ) {
    this.humidity = humidity;
    this.temp_c = temp_c;
    this.temp_f = temp_f;
    this.condition = condition;
    this.is_day = is_day;
  }
}

export interface HourWeather {
  time: string; time_epoch: number; temp_c: number; temp_f: number; condition: { text: string; icon: string; };
}

export interface Astro {
  is_moon_up: boolean
  is_sun_up: boolean,
  moon_illumination: number,
  moon_phase: string,
  moonrise: string,
  moonset: string,
  sunrise: string,
  sunset: string,
}

export class Forecastday {
  date: string;
  day: {
    maxtemp_c: number;
    maxtemp_f: number;
    mintemp_c: number;
    mintemp_f: number;
    avgtemp_c: number;
    avgtemp_f: number;
    avghumidity: number;
    minhumidity: number;
    maxhumidity: number;
    condition: {
      text: string;
      icon: string;
    };
  };
  hour: HourWeather[];
  astro: Astro;

  constructor(
    date: string,
    day: {
      maxtemp_c: number;
      maxtemp_f: number;
      mintemp_c: number;
      mintemp_f: number;
      avgtemp_c: number;
      avgtemp_f: number;
      avghumidity: number;
      minhumidity: number;
      maxhumidity: number;
      condition: { text: string; icon: string };
    },
    hour: { time: string; time_epoch: number; temp_c: number; temp_f: number; condition: { text: string; icon: string; }; }[],
    astro: Astro
  ) {
    this.date = date;
    this.day = day;
    this.hour = hour
    this.astro = astro
  }
}

export class WeatherForecast {
  location: Location;
  current: CurrentWeather;
  forecast: {
    forecastday: Forecastday[];
  };

  constructor(
    location: Location,
    current: CurrentWeather,
    forecastday: Forecastday[]
  ) {
    this.location = location;
    this.current = current;
    this.forecast = { forecastday };
  }
}
