use crate::models::email_category::{ EmailCategory, NewEmailCategory };
use crate::schema::email_categories::sender_company;
use crate::services::email_category_service;
use crate::services::emails_service;

use crate::models::session::{ NewSession, Session };
use diesel::OptionalEmptyChangesetExtension;
use tauri::{ Emitter, Manager };
use uuid::Uuid;

#[tauri::command]
pub fn list_email_categories() -> Result<Vec<EmailCategory>, String> {
    email_category_service
        ::list_email_categories()
        .map_err(|e| format!("Failed to load email categories: {:?}", e))
}

#[tauri::command]
pub fn get_email_category(category_id: String) -> Option<EmailCategory> {
    match email_category_service::get_email_category(&category_id) {
        Ok(category) => category,
        Err(e) => {
            eprintln!("Error retrieving email category: {:?}", e);
            None
        }
    }
}

#[tauri::command]
pub fn new_email_category(
    category_id: String,
    child_full_domains: String,
    name: String,
    sender_company_: String,
    sender_domain: String,
    avatar_link: String,
    tags: String, // New field for tags
    description: Option<String>, // New field for a brief description
    priority: Option<i32>, // New field for priority
    color: Option<String>, // New field for a hex color code
    unread_count: Option<i32>, // New field to track the number of unread emails
    parent_category_id: String, // New field for parent category ID
    visibility: Option<String>, // New field for visibility
    is_synced: Option<bool> // New field for sync status
) -> EmailCategory {
    let new_email_category = NewEmailCategory {
        id: Uuid::new_v4().to_string(),
        category_id,
        child_full_domains,
        name,
        sender_company: sender_company_,
        sender_domain,
        avatar_link,
        created_at: chrono::Utc::now().naive_utc(),
        tags,
        latest_email_at: Some(chrono::Utc::now().naive_utc()), // Will be updated when an email is added
        description,
        priority,
        color,
        unread_count: Some(unread_count.unwrap_or(0)), // Default to 0 if not provided
        last_accessed_at: None, // Will be updated when the category is accessed
        is_archived: Some(false), // Default to false
        custom_data: None, // Default to None; can be updated later
        parent_category_id,
        class_category_id: "".to_string(),
        visibility,
        is_synced: Some(is_synced.unwrap_or(false)), // Default to false if not provided
    };

    email_category_service::store_email_category(&new_email_category);

    new_email_category.into()
}

#[tauri::command]
pub fn delete_email_category(app_handle: tauri::AppHandle, category_id: String) {
    //messages_service::delete_session_messages(session_id.clone());
    email_category_service::delete_email_category(category_id.clone());

    app_handle.emit("email_category_deleted", {}).unwrap();
}

#[tauri::command]
pub fn get_today_categories() -> Result<Vec<EmailCategory>, String> {
    emails_service::get_today_categories().map_err(|e| e.to_string())
}
