// extern crate rust_bert;

use regex::Regex;
// use rust_bert::pipelines::ner::NERModel;
// use rust_bert::pipelines::sentiment::{SentimentModel, SentimentPolarity};
// use rust_bert::pipelines::sequence_classification::SequenceClassificationModel;
// use rust_bert::pipelines::summarization::SummarizationModel;
use whatlang::{detect, Lang};

use serde_json::Value;
use std::collections::HashMap;
use std::collections::HashSet;
// use tch::nn::Module;
// use tch::Device;

// use crate::schema::emails::thread_id;
// use super::determine_urgency;
use std::fs::File;
use std::io::{BufRead, BufReader};

use tokio::sync::OnceCell;

use tokio::sync::Mutex;

use lazy_static::lazy_static;

pub async fn extract_actionable_items(
    snippet: &str, //ner_model: Arc<Mutex<NERModel>>
) -> Vec<String> {
    // List of actionable keywords and phrases
    const ACTIONABLE_KEYWORDS: &[&str] = &[
        "please review",
        "need approval",
        "follow up",
        "respond by",
        "action required",
        "submit",
        "complete",
        "sign",
        "verify",
        "provide feedback",
        "RSVP",
        "register",
        "confirm",
        "update",
        "schedule",
        "meeting",
        "deadline",
        "approve",
        "acknowledge",
        "consider",
        "take action",
        "reply by",
        "due date",
        "finalize",
        "sign the document",
        "arrange",
        "organize",
        "prepare",
        "share",
        "get back",
        "let me know",
        "provide a quote",
        "fill out the form",
        "request the information",
        "prepare the agenda",
        "set up a reminder",
        "create a report",
        "sign off on",
        "book",
        "assign",
        "review the contract",
        "send the invoice",
        "prepare the report",
        "set up an interview",
        "coordinate",
        "schedule a call",
        "RSVP",
    ];

    // Precompile regex patterns for detecting actionable items (optimized)
    lazy_static::lazy_static! {
        static ref ACTIONABLE_PATTERNS: Vec<Regex> = vec![
            Regex::new(r"please\s+(review|respond|sign|confirm|provide feedback|update|approve)").unwrap(),
            Regex::new(r"need\s+(approval|feedback|response|confirmation)").unwrap(),
            Regex::new(r"follow\s+up").unwrap(),
            Regex::new(r"respond\s+by\s+\d{1,2}/\d{1,2}/\d{4}").unwrap(),
            Regex::new(r"action\s+required").unwrap(),
            Regex::new(r"submit\s+(your|the)\s+.*").unwrap(),
            Regex::new(r"complete\s+(the|your)\s+.*").unwrap(),
            Regex::new(r"schedule\s+(a|an|your)\s+.*").unwrap(),
            Regex::new(r"meeting\s+(on|at|with)\s+.*").unwrap(),
            Regex::new(r"deadline\s+(is|for|to|on)\s+.*").unwrap(),
            Regex::new(r"due\s+date\s+(is|for)\s+.*").unwrap(),
            Regex::new(r"finalize\s+(the|your)\s+.*").unwrap(),
            Regex::new(r"approve\s+(the|your)\s+.*").unwrap(),
        ];
    }

    let mut actionable_items: HashSet<String> = HashSet::new();

    // Step 1: Use keyword matching efficiently
    actionable_items.extend(
        ACTIONABLE_KEYWORDS
            .iter()
            .filter(|&&keyword| snippet.contains(keyword))
            .map(|&keyword| keyword.to_string()),
    );

    // Step 2: Use precompiled regex patterns for efficiency
    for pattern in ACTIONABLE_PATTERNS.iter() {
        for cap in pattern.captures_iter(snippet) {
            if let Some(m) = cap.get(0) {
                actionable_items.insert(m.as_str().to_string());
            }
        }
    }

    // Step 3: Use Rust BERT to enhance detection with NER
    // let ner_model = ner_model.lock().await;
    let sentences = vec![snippet.to_string()];
    // let ner_output = ner_model.predict(&sentences);

    // Step 4: Loop through NER output and look for actionable entities (e.g., "MISC" or "EVENT")
    // for sentence_output in ner_output {
    //     for entity in sentence_output {
    //         if entity.label == "MISC" && !actionable_items.contains(&entity.word) {
    //             actionable_items.insert(entity.word.clone());
    //         }
    //     }
    // }

    // Step 5: Return actionable items, cleaned up and deduplicated
    actionable_items
        .into_iter()
        .map(|mut item| {
            item.retain(|c| !c.is_whitespace());
            item.to_uppercase()
        })
        .collect()
}
