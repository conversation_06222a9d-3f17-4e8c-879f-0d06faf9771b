<template>
  <div class="size-full flex flex-col items-center justify-center">
    <!-- Password Form -->
    <div class="relative z-10 w-full max-w-md rounded-lg">
      <div class="text-center mb-6">
        <h2 class="text-2xl font-bold text-gray-700">Set Password</h2>
        <p class="text-gray-500 text-sm mt-2">Enter and confirm your password below.</p>
      </div>

      <!-- Form -->
      <form @submit.prevent="submitForm" class="space-y-4">
        <!-- New Password Input -->
        <div>
          <label for="new-password" class="block text-sm font-medium text-gray-600">New Password</label>
          <input
            v-model="newPassword"
            id="new-password"
            type="password"
            placeholder="Enter new password"
            class="mt-1 w-full rounded-md border border-gray-300 px-4 py-2 text-sm focus:ring-indigo-500 focus:border-indigo-500"
            @input="sanitizePassword('new')"
          />
          <p v-if="newPasswordError" class="text-red-500 text-xs mt-1">{{ newPasswordError }}</p>
        </div>

        <!-- Confirm Password Input -->
        <div>
          <label for="confirm-password" class="block text-sm font-medium text-gray-600">Confirm Password</label>
          <input
            v-model="confirmPassword"
            id="confirm-password"
            type="password"
            placeholder="Confirm your password"
            class="mt-1 w-full rounded-md border border-gray-300 px-4 py-2 text-sm focus:ring-indigo-500 focus:border-indigo-500"
            @input="sanitizePassword('confirm')"
          />
          <p v-if="confirmPasswordError" class="text-red-500 text-xs mt-1">{{ confirmPasswordError }}</p>
        </div>

        <!-- Submit Button -->
        <div>
          <button
            type="submit"
            :disabled="loading || !isFormValid"
            class="w-full rounded-md bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:bg-gray-300 disabled:cursor-not-allowed"
          >
            {{ loading ? "Setting..." : "Set Password" }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script lang="ts">
import { ref } from "vue";
import { useRouter } from "vue-router";
import { invoke } from "@tauri-apps/api/core";
import { computed } from "vue";

// Simple function for sanitizing input (basic escaping)
const sanitizeInput = (input: string): string => {
  return input.replace(/[<>"'&]/g, (match) => {
    const replacements: Record<string, string> = {
      "<": "&lt;",
      ">": "&gt;",
      '"': "&quot;",
      "'": "&#39;",
      "&": "&amp;",
    };
    return replacements[match];
  });
};

export default {
  setup() {
    // State
    const newPassword = ref("");
    const confirmPassword = ref("");
    const newPasswordError = ref("");
    const confirmPasswordError = ref("");
    const loading = ref(false);
    const isFormValid = ref(false);

    const router = useRouter();
    // Access query parameters using computed properties
    const name = computed(() => router.currentRoute.value.query.name || "");
    const email = computed(() => router.currentRoute.value.query.email || "");
    // Access query parameters

    // Input sanitization
    const sanitizePassword = (field: string) => {
      if (field === "new") {
        newPassword.value = sanitizeInput(newPassword.value.trim());
        validateNewPassword();
      } else if (field === "confirm") {
        confirmPassword.value = sanitizeInput(confirmPassword.value.trim());
        validateConfirmPassword();
      }
    };

    // Validations
    const validateNewPassword = () => {
      newPasswordError.value =
        !newPassword.value || newPassword.value.length < 6 ? "Password must be at least 6 characters long." : "";
      validateForm();
    };

    const validateConfirmPassword = () => {
      confirmPasswordError.value = confirmPassword.value !== newPassword.value ? "Passwords do not match." : "";
      validateForm();
    };

    const validateForm = () => {
      isFormValid.value =
        !newPasswordError.value &&
        !confirmPasswordError.value &&
        newPassword.value.length > 0 &&
        confirmPassword.value.length > 0;
    };

    // Submit Handler
    const submitForm = async () => {
      validateNewPassword();
      validateConfirmPassword();

      if (!isFormValid.value) return;

      try {
        loading.value = true;

        // Send sanitized data to backend
        const sanitizedPassword = sanitizeInput(newPassword.value);

        //  console.log("Sanitized Password Sent:", sanitizedPassword);
        await invoke("js2rs", { message: "set_password", password: sanitizedPassword });
        router.replace("onboarding");
        // Simulate backend call
        setTimeout(() => {
          loading.value = false;
        }, 1000);
      } catch (error) {
        console.error("Error setting password:", error);
      } finally {
        loading.value = false;
      }
    };

    return {
      newPassword,
      confirmPassword,
      newPasswordError,
      confirmPasswordError,
      loading,
      isFormValid,
      sanitizePassword,
      submitForm,
    };
  },
};
</script>

<style scoped>
.app-background {
  background-color: #ede6dc;
}
</style>
