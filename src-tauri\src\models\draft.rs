use diesel::prelude::{Identifiable, Insertable, Queryable};
use serde::{Deserialize, Serialize};

use crate::schema::drafts;

#[derive(Insertable, Queryable, Identifiable, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = drafts)]
pub struct Draft {
    pub id: String,
    pub from: String,
    pub to: String,
    pub subject: String,
    pub body: String,
    pub cc: Option<String>,
    pub bcc: Option<String>,
    pub thread_id: Option<String>,
    pub parent_email_id: Option<String>,
    pub is_send: Option<bool>,
    pub status: String,
    pub task_id: Option<i32>,
    pub created_at: Option<String>,
    pub updated_at: Option<String>,
}
