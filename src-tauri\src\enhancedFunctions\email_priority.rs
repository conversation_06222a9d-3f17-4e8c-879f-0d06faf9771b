// extern crate rust_bert;

use futures::future::ok;
use regex::Regex;
// use rust_bert::pipelines::ner::NERModel;
// use rust_bert::pipelines::sentiment::{SentimentModel, SentimentPolarity};
// use rust_bert::pipelines::sequence_classification::SequenceClassificationModel;
// use rust_bert::pipelines::summarization::SummarizationModel;
use whatlang::{detect, Lang};

use serde_json::Value;
use std::clone;
use std::collections::HashMap;
use std::collections::HashSet;
// use tch::nn::Module;
// use tch::Device;

// use crate::schema::emails::thread_id;
// use super::determine_urgency;
use std::fs::File;
use std::io::{BufRead, BufReader};

use crate::enhancedFunctions::extract_actionable_items::extract_actionable_items;
use serde_json::json;
use tokio::sync::Mutex;
use tokio::sync::OnceCell;

// // Function to load blacklisted domains from a file
// fn load_blacklisted_domains() -> HashSet<String> {
//     let file = File::open("blacklist.txt").expect("Could not open file");
//     let reader = BufReader::new(file);

//     reader
//         .lines()
//         .map(|line| line.expect("Could not read line").trim().to_string())
//         .collect()
// }

// Placeholder function for performing NLP entity recognition (e.g., dates, times)
fn extract_entities(snippet: &str) -> Vec<String> {
    let date_regex = Regex::new(r"\b\d{4}-\d{2}-\d{2}\b|\b\d{1,2}/\d{1,2}/\d{2,4}\b").unwrap();
    date_regex
        .find_iter(snippet)
        .map(|mat| mat.as_str().to_string())
        .collect()
}

// Placeholder function for sentiment analysis using Rust-BERT
async fn analyze_sentiment_urgency(
    snippet: &str,
    // sentiment_model: Arc<Mutex<SentimentModel>>,
) -> f64 {
    // Lock the sentiment model using .await
    // let model = sentiment_model.lock().await;

    // Predict the sentiment of the snippet
    // let sentiments = model.predict(&[snippet]);

    // Return a numeric value based on the sentiment polarity
    // match sentiments[0].polarity {
    //     SentimentPolarity::Negative => -1.0,
    //     SentimentPolarity::Positive => 1.0,
    //     _ => 0.0,
    // }
    0.0
}

// Placeholder function for calculating TF-IDF scores (simplified version)
fn calculate_tfidf(corpus: &[&str]) -> HashMap<String, f64> {
    let mut word_count: HashMap<String, usize> = HashMap::new();
    let mut document_count: HashMap<String, usize> = HashMap::new();

    for doc in corpus {
        let mut seen = HashMap::new();
        for word in doc.split_whitespace() {
            *word_count.entry(word.to_string()).or_insert(0) += 1;
            if !seen.contains_key(word) {
                *document_count.entry(word.to_string()).or_insert(0) += 1;
                seen.insert(word, true);
            }
        }
    }

    let total_docs = corpus.len();
    let mut tfidf_scores: HashMap<String, f64> = HashMap::new();
    for (word, &count) in &word_count {
        let tf = count as f64 / total_docs as f64;
        let idf = (total_docs as f64 / document_count[word] as f64).ln();
        tfidf_scores.insert(word.clone(), tf * idf);
    }

    tfidf_scores
}

pub async fn determine_urgency(
    subject: &str,
    snippet: &str,
    // sentiment_model: Arc<Mutex<SentimentModel>>,
) -> i32 {
    let mut urgency_score = 0;

    let urgency_keywords = vec![
        "urgent",
        "immediate",
        "asap",
        "action required",
        "deadline",
        "important",
        "priority",
        "alert",
    ];

    // Calculate initial urgency based on keyword presence
    urgency_score += urgency_keywords
        .iter()
        .filter(|&&k| subject.to_lowercase().contains(k))
        .count() as i32
        * 2;
    urgency_score += urgency_keywords
        .iter()
        .filter(|&&k| snippet.to_lowercase().contains(k))
        .count() as i32;

    // Use entity extraction to detect dates or times
    let entities = extract_entities(snippet);
    for _ in entities {
        urgency_score += 1;
    }

    // Use sentiment analysis
    let polarity: f64 = analyze_sentiment_urgency(
        &snippet, // sentiment_model.clone()
    )
    .await;
    // Adjust urgency based on sentiment
    if polarity < -0.3 {
        urgency_score += 2;
    } else if polarity < 0.0 {
        urgency_score += 1;
    }

    // TF-IDF calculation for additional context-based urgency detection
    let corpus = vec![subject, snippet];
    let tfidf_scores = calculate_tfidf(&corpus);

    for feature in tfidf_scores.keys() {
        if urgency_keywords.contains(&feature.as_str()) {
            urgency_score += 1;
        }
    }

    // Final adjustment to ensure score is within a reasonable range
    urgency_score = urgency_score.min(10).max(0);

    urgency_score
}

pub async fn analyze_sentiment_advanced(
    snippet: &str,
    // sentiment_model: Arc<Mutex<SentimentModel>>,
) -> (String, f64, Option<f64>) {
    // Lock the sentiment model using .await
    // let model = sentiment_model.lock().await;

    // Predict the sentiment and get the detailed result with confidence scores
    // let sentiments = model.predict(&[snippet]);

    // let sentiment_score = sentiments[0].score; // Extract confidence score
    // let sentiment_polarity = &sentiments[0].polarity;

    // Classify sentiment with categories and return confidence scores
    // let (sentiment, confidence, neutral_threshold) = match sentiment_polarity {
    //     SentimentPolarity::Positive if sentiment_score > 0.7 => {
    //         ("Positive".to_string(), sentiment_score, None) // High confidence for positive sentiment
    //     }
    //     SentimentPolarity::Negative if sentiment_score > 0.7 => {
    //         ("Negative".to_string(), sentiment_score, None) // High confidence for negative sentiment
    //     }
    //     SentimentPolarity::Positive | SentimentPolarity::Negative => {
    //         // Slightly positive or negative with a moderate confidence score
    //         ("Neutral".to_string(), sentiment_score, Some(0.5)) // Neutral threshold for less confident predictions
    //     }
    // };

    // Return the sentiment category, the confidence score, and the neutral threshold (if applicable)
    ("sentiment".to_string(), 0.0, None)
}

fn assess_sender_reputation(
    sender_domain: &str,
    // blacklisted_domains: &HashSet<String>,  // Correctly formatted parameter
) -> String {
    // if blacklisted_domains.contains(sender_domain) {
    //     "Blacklisted".to_string()
    // } else {
    //     "Trusted".to_string()
    // }
    "Trusted".to_string()
}

pub async fn summarize_thread(
    thread_id: Option<&str>,
    thread_content: &str,
    max_summary_length: Option<usize>,
    // model: &Arc<Mutex<SummarizationModel>>, // Use the preloaded model
) -> Option<String> {
    // If thread_id is None or thread content is empty, return None
    if thread_id.is_none() || thread_content.is_empty() {
        return None;
    }
    // // Load a summarization model (e.g., BERT)
    // let model = match SummarizationModel::new(Default::default()) {
    //     Ok(m) => m,
    //     Err(e) => {
    //         eprintln!("Failed to load the summarization model: {}", e);
    //         return None;
    //     }
    // };

    // // Summarize the thread content
    // let summaries = model.summarize(&[thread_content]);

    // If summary length control is desired
    // let summary = summaries.get(0).cloned();
    // if let Some(max_len) = max_summary_length {
    //     summary.map(|s| s.chars().take(max_len).collect())
    // } else {
    //     summary
    // }
    // Lock the model to ensure thread-safe access
    // let model = model.lock().await;
    // let summaries = model.summarize(&[thread_content]);

    // let summary = summaries.get(0).cloned();
    // if let Some(max_len) = max_summary_length {
    //     summary.map(|s| s.chars().take(max_len).collect())
    // } else {
    //     summary
    // }

    return Some("t".to_string());
}

pub fn detect_language(snippet: &str) -> String {
    // Detect the language using whatlang
    match detect(snippet) {
        Some(info) => {
            let lang = info.lang();
            match lang {
                Lang::Eng => "en".to_string(),
                Lang::Spa => "es".to_string(),
                Lang::Fra => "fr".to_string(),
                Lang::Deu => "de".to_string(),
                Lang::Rus => "ru".to_string(),
                // Add more languages as needed
                _ => lang.code().to_string(),
            }
        }
        None => "unknown".to_string(),
    }
}

pub fn translate_snippet(snippet: &str, target_language: &str) -> Option<String> {
    // Placeholder function: Replace with actual translation API integration
    if target_language != "en" {
        Some(format!("Translated snippet to {}", target_language))
    } else {
        None
    }
}

fn assess_phishing_risk(email_data: &HashMap<String, String>) -> String {
    // Placeholder function: Implement phishing detection logic
    let suspicious_keywords = vec!["password", "account", "verify"];
    if suspicious_keywords.iter().any(|&keyword| {
        email_data
            .get("snippet")
            .unwrap_or(&"".to_string())
            .contains(keyword)
    }) {
        "High".to_string()
    } else {
        "Low".to_string()
    }
}

fn determine_bucket_and_priority(
    labels: Vec<String>,
    snippet: &str,
    subject: &str,
    sender_domain: &str,
    is_unread: bool, // New variable to indicate if the email is unread
) -> (String, String) {
    // High priority keywords
    let high_priority_keywords = vec![
        "urgent",
        "important",
        "asap",
        "action required",
        "immediate",
        "deadline",
        "response needed",
    ];

    // Low priority keywords
    let low_priority_keywords = vec![
        "newsletter",
        "promotion",
        "sale",
        "advertisement",
        "discount",
        "unsubscribe",
    ];

    // Define sender domains
    let high_priority_domains: HashSet<&str> = vec!["ceo.company.com", "partner.company.com"]
        .into_iter()
        .collect();
    let medium_priority_domains: HashSet<&str> = vec!["trustedcompany.com", "businesspartner.com"]
        .into_iter()
        .collect();
    let low_priority_domains: HashSet<&str> = vec!["ads.company.com", "news.company.com"]
        .into_iter()
        .collect();

    // Default bucket and priority
    let mut bucket = "Primary".to_string();
    let mut priority = "Neutral".to_string();

    // Categorize by Gmail labels
    if labels.contains(&"CATEGORY_PROMOTIONS".to_string()) {
        bucket = "Promotions".to_string();
        priority = "Low Priority".to_string();
    } else if labels.contains(&"CATEGORY_SOCIAL".to_string()) {
        bucket = "Social".to_string();
        priority = "Low Priority".to_string();
    } else if labels.contains(&"CATEGORY_UPDATES".to_string()) {
        bucket = "Updates".to_string();
        priority = "Medium Priority".to_string();
    } else if labels.contains(&"CATEGORY_FORUMS".to_string()) {
        bucket = "Forums".to_string();
        priority = "Low Priority".to_string();
    } else if labels.contains(&"CATEGORY_PRIMARY".to_string()) {
        bucket = "Primary".to_string();
        priority = "High Priority".to_string();
    }

    // Adjust priority based on keywords in subject or snippet
    if high_priority_keywords
        .iter()
        .any(|&keyword| subject.contains(keyword) || snippet.contains(keyword))
    {
        priority = "High Priority".to_string();
    } else if low_priority_keywords
        .iter()
        .any(|&keyword| subject.contains(keyword) || snippet.contains(keyword))
    {
        priority = "Low Priority".to_string();
    }

    // Check if the sender is from known high or medium priority domains
    if high_priority_domains.contains(sender_domain) {
        priority = "High Priority".to_string();
    } else if medium_priority_domains.contains(sender_domain) && priority == "Neutral" {
        priority = "Medium Priority".to_string();
    } else if low_priority_domains.contains(sender_domain) {
        priority = "Low Priority".to_string();
    }

    // Mark unread emails as higher priority if they aren't already marked
    if is_unread && priority == "Low Priority" {
        priority = "Neutral".to_string(); // Elevate low-priority unread emails to neutral
    }

    // Optional: If email is marked as SPAM or contains suspicious content, demote it
    if labels.contains(&"SPAM".to_string()) || snippet.contains("phishing") {
        bucket = "Spam".to_string();
        priority = "Low Priority".to_string();
    }

    (bucket, priority)
}

pub fn analyze_attachments(
    attachments: Vec<HashMap<String, String>>,
) -> (usize, Vec<String>, usize) {
    let attachment_count = attachments.len();
    let attachment_types: Vec<String> = attachments
        .iter()
        .filter_map(|att| att.get("type").cloned())
        .collect();
    let total_attachment_size = attachments
        .iter()
        .map(|att| {
            att.get("size")
                .unwrap_or(&"0".to_string())
                .parse::<usize>()
                .unwrap_or(0)
        })
        .sum();

    (attachment_count, attachment_types, total_attachment_size)
}

// pub async fn categorize_email_from_metadata(
//     email_data: HashMap<String, String>,
//     summarization_model: Arc<Mutex<SummarizationModel>>,
//     sentiment_model: Arc<Mutex<SentimentModel>>,
//     ner_model: Arc<Mutex<NERModel>>,

//     // blacklisted_domains: &HashSet<String>,  // Add blacklisted_domains parameter

// ) -> HashMap<String, Option<String>> {
//     let labels: Vec<String> = email_data
//         .get("labelIds")
//         .unwrap_or(&"".to_string())
//         .split(',')
//         .map(String::from)
//         .collect();
//     let snippet = email_data
//         .get("snippet")
//         .unwrap_or(&"".to_string())
//         .to_lowercase();
//     let subject = email_data
//         .get("subject")
//         .unwrap_or(&"".to_string())
//         .to_lowercase();
//     let sender_domain = email_data
//         .get("sender_domain")
//         .unwrap_or(&"".to_string())
//         .to_lowercase();

//     let (bucket, priority) =
//         determine_bucket_and_priority(labels, &snippet, &subject, &sender_domain);
//     // Assuming you have the necessary content and max length for summarization
//     let my_str: &str = email_data.get("threadId").map_or("", String::as_str);

//     let thread_id: Option<&str> = Some(my_str);

//     let thread_content: &str = email_data.get("snippet").map_or("", |s| s.as_str());

//     let max_summary_length: Option<usize> = Some(100); // Example value

//     let thread_summary = summarize_thread(
//         thread_id,            // Converts Option<String> to Option<&str>
//         thread_content,       // Converts String to &str
//         max_summary_length,   // Passes the optional max summary length
//         &summarization_model, // Pass the model reference
//     )
//     .await;

//     let attachments: Vec<HashMap<String, String>> = vec![]; // Replace with actual attachment data parsing
//     let (attachment_count, attachment_types, total_attachment_size) =
//         analyze_attachments(attachments);

//     let urgency_score = determine_urgency(&subject, &snippet, sentiment_model.clone()).await;
//     let sentiment = analyze_sentiment(&snippet, sentiment_model.clone()).await;
//     let actionable_items = extract_actionable_items(&snippet, ner_model);

//     let language = detect_language(&snippet);
//     let translated_snippet: Option<String> = if language != "en" {
//         translate_snippet(&snippet, "en")
//     } else {
//         None
//     };

//     let phishing_risk = assess_phishing_risk(&email_data);
//     let sender_reputation = assess_sender_reputation(&sender_domain, //&blacklisted_domains
//     );

//     let mut email_info = HashMap::new();
//     email_info.insert("bucket".to_string(), Some(bucket));
//     email_info.insert("priority".to_string(), Some(priority.clone()));
//     email_info.insert("thread_id".to_string(), Some(priority));
//     email_info.insert("thread_summary".to_string(), thread_summary);
//     email_info.insert(
//         "attachment_count".to_string(),
//         Some(attachment_count.to_string()),
//     );
//     email_info.insert(
//         "attachment_types".to_string(),
//         Some(attachment_types.join(", ")),
//     );
//     email_info.insert(
//         "total_attachment_size".to_string(),
//         Some(total_attachment_size.to_string()),
//     );
//     email_info.insert("urgency_score".to_string(), Some(urgency_score.to_string()));
//     email_info.insert("sentiment".to_string(), Some(sentiment));
//     email_info.insert(
//         "actionable_items".to_string(),
//         Some(actionable_items.await.join(", ")),
//     );
//     email_info.insert("language".to_string(), Some(language));
//     email_info.insert("translated_snippet".to_string(), translated_snippet);
//     email_info.insert("phishing_risk".to_string(), Some(phishing_risk));
//     email_info.insert("sender_reputation".to_string(), Some(sender_reputation));

//     email_info
// }

pub async fn categorize_email_from_metadata(
    email_data: HashMap<String, String>,
    // summarization_model: Arc<Mutex<SummarizationModel>>,
    // sentiment_model: Arc<Mutex<SentimentModel>>,
    // ner_model: Arc<Mutex<NERModel>>,
) -> HashMap<String, Option<String>> {
    let labels: Vec<String> = match serde_json::from_str::<Vec<String>>(
        email_data.get("labelIds").unwrap_or(&"".to_string()),
    ) {
        Ok(parsed_labels) => parsed_labels
            .into_iter()
            .map(|label| label.trim_matches(|c| c == '\\' || c == '"').to_string()) // Clean extra escape characters
            .collect(),
        Err(_) => vec![], // Return an empty vector if parsing fails
    };

    // Print the labels for debugging

    // Fetch frequently accessed fields early
    let snippet = email_data
        .get("snippet")
        .unwrap_or(&"".to_string())
        .to_lowercase();
    let subject = email_data
        .get("subject")
        .unwrap_or(&"".to_string())
        .to_lowercase();
    let sender_domain = email_data
        .get("sender_domain")
        .unwrap_or(&"".to_string())
        .to_lowercase();
    let email_size = email_data.get("sizeEstimate").unwrap_or(&"0".to_string());
    let is_unread = email_data
        .get("labelIds")
        .unwrap_or(&"".to_string()) // Default to an empty string if not found
        .split(',')
        .any(|label| label == "UNREAD"); // Check if any label is "UNREAD"
                                         // Determine bucket and priority
    let (bucket, priority) = determine_bucket_and_priority(
        labels.clone(),
        &snippet,
        &subject,
        &sender_domain,
        is_unread,
    );
    // Debug print for labels
    println!("Labels: {:?}", labels);

    let is_important = labels.contains(&"IMPORTANT".to_string());

    // Debug print for importance check
    println!("Is important: {}", is_important);

    // Process the thread and summarize it
    let thread_id = email_data.get("threadId").map_or("", String::as_str);
    let thread_content = email_data.get("snippet").map_or("", |s| s.as_str());
    let max_summary_length = Some(100); // Example summary length

    // If email is promotional or low priority, assign default values and skip heavy processing
    // if bucket == "Promotions" || bucket == "Social" || bucket == "Ads" || priority == "Low Priority"
    // {

    // Process the thread and summarize it
    let thread_id = email_data.get("threadId").map_or("", String::as_str);
    let thread_content = email_data.get("snippet").map_or("", |s| s.as_str());
    let max_summary_length = Some(100); // Example summary length

    return HashMap::from([
        ("bucket".to_string(), Some(bucket)), // Return the bucket
        ("priority".to_string(), Some("Low Priority".to_string())), // Default low priority
        ("urgency_score".to_string(), Some("0".to_string())), // Set urgency to 0
        ("sentiment".to_string(), Some("Neutral".to_string())), // Default neutral sentiment
        ("thread_summary".to_string(), Some("".to_string())), // No summarization
        ("actionable_items".to_string(), Some("None".to_string())), // No actionable items
        ("language".to_string(), Some("en".to_string())), // Default to English
        ("phishing_risk".to_string(), Some("Low".to_string())), // Default low phishing risk
        ("sender_reputation".to_string(), Some("Safe".to_string())), // Default safe sender
        ("process_flag".to_string(), Some(is_important.to_string())),
        (
            "snippet".to_string(),
            Some(
                email_data
                    .get("snippet")
                    .map_or("".to_string(), |s| s.to_string()),
            ),
        ), // Default safe sender
    ]);
    // }

    // Parallel execution of summarization, sentiment, urgency, and NER analysis
    let summarization_future = summarize_thread(
        Some(thread_id),
        thread_content,
        max_summary_length,
        // &summarization_model,
    );
    let urgency_future = determine_urgency(
        &subject, &snippet, // sentiment_model.clone()
    );
    let sentiment_future = analyze_sentiment_advanced(
        &snippet, //sentiment_model.clone()
    );
    let actionable_items_future = extract_actionable_items(
        &snippet, //ner_model.clone()
    );

    // Await all futures concurrently
    let (thread_summary, urgency_score, (sentiment, _, _), actionable_items) = tokio::join!(
        summarization_future,
        urgency_future,
        sentiment_future,
        actionable_items_future
    );

    // Process attachments
    let attachments: Vec<HashMap<String, String>> = vec![]; // Replace with actual attachments data
    let (attachment_count, attachment_types, total_attachment_size) =
        analyze_attachments(attachments);

    // Detect language and translate snippet if necessary
    let language = detect_language(&snippet);
    let translated_snippet = if language != "en" {
        translate_snippet(&snippet, "en")
    } else {
        None
    };

    // Assess phishing risk and sender reputation
    let phishing_risk = assess_phishing_risk(&email_data);
    let sender_reputation = assess_sender_reputation(&sender_domain);

    // Collect and return all the processed email metadata
    let mut email_info = HashMap::new();
    email_info.insert("snippet".to_string(), Some(snippet));

    email_info.insert("bucket".to_string(), Some(bucket));
    email_info.insert("priority".to_string(), Some(priority));
    email_info.insert("thread_id".to_string(), Some(thread_id.to_string()));
    email_info.insert("thread_summary".to_string(), thread_summary);
    email_info.insert(
        "attachment_count".to_string(),
        Some(attachment_count.to_string()),
    );
    email_info.insert(
        "attachment_types".to_string(),
        Some(attachment_types.join(", ")),
    );
    email_info.insert(
        "total_attachment_size".to_string(),
        Some(total_attachment_size.to_string()),
    );
    email_info.insert("urgency_score".to_string(), Some(urgency_score.to_string()));
    email_info.insert("sentiment".to_string(), Some(sentiment));
    email_info.insert(
        "actionable_items".to_string(),
        Some(actionable_items.join(", ")),
    );
    email_info.insert("language".to_string(), Some(language));
    email_info.insert("translated_snippet".to_string(), translated_snippet);
    email_info.insert("phishing_risk".to_string(), Some(phishing_risk));
    email_info.insert("sender_reputation".to_string(), Some(sender_reputation));

    // Return the collected metadata
    email_info
}
