![oway Logo](/docs/images/oway.png?raw=true)
# oway

Introducing oway - the desktop app that lets you create multiple assistants with specific goals. With chatGPT technology behind it, oway's assistants are capable of helping you with anything you need, based on the goals you've defined for them.

Built with <PERSON><PERSON>, oway is a lightweight app that takes up only 11MB of space on your computer. It's the perfect tool for anyone who wants to define a unique profile for their assistant, complete with a list of instructions, and have personalized conversations with it.

oway's features include a local SQLite database that stores the chat history for each assistant and allows you to easily organize your conversations by assistant. You can also use your own OpenAI key and general settings are stored in local.

Whether you're looking for a more productive workday or simply want to chat with a friendly virtual assistant, oway has got you covered. Try it out today and see how it can simplify your life.

## Installation

- Download the installation file and support us by paying what you want (even 0$) from [here](https://taecontrol.lemonsqueezy.com/checkout/buy/0b6e6aea-457b-42ea-a14b-c4371dd1a3fa).
- You can also clone the repo, install dependencies, and run `npm run tauri build`. The installation file is going to be created on `./src-tauri/target/release/bundle` directory.

- To run the the app do this `npm run tauri dev`

## Get our book - MoonGuard: The Software Creator's Journey

Our team has written a book, "MoonGuard: The Software
Creator's Journey." In this book, we document and explain the entire process
of creating, developing, publishing a [Filament plugin](https://filamentphp.com/). Get your own digital copy on [https://moonguard.dev](https://moonguard.dev/book)

