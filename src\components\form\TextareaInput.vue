<script setup lang="ts">
// Define the interface for Props
interface Props {
  label: string;
  name: string;
  id: string;
  rows?: number;
  required?: boolean;
  modelValue: string;
}

// Use defineProps with the Props interface
const props = withDefaults(defineProps<Props>(), {
  rows: 4,
  required: false
});

// Define emits
const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>();
</script>

<template>
<div>
  <label :for="id" class="block text-sm font-medium leading-6 text-gray-900">
    {{ label }}
  </label>
  <div class="mt-2">
    <textarea
      :required="required"
      :rows="rows"
      :name="name"
      :id="id"
      class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
      :value="modelValue"
      @input="emit('update:modelValue', ($event.target as HTMLTextAreaElement).value)"
    />
  </div>
</div>
</template>