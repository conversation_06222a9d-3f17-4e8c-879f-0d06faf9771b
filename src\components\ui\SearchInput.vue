<template>
  <div class="flex relative w-96">
    <input
      type="text"
      class="h-7 w-full border-0 focus:border-2 focus:border-secondary-300 outline-transparent focus-visible:ring-0 outline-0 hover:bg-secondary-200/60 bg-secondary-200/40 rounded-lg placeholderx:text-xs text-xs text-secondary-800 pt-1.5"
      placeholder="Search"
      :value="inputValue"
      @keyup.enter="(e)=>inputValue = (e.target as HTMLInputElement).value"
    />
    <MagnifyingGlassIcon class="size-5 absolute right-1 top-1 text-base-200" />
  </div>
</template>

<script setup lang="ts">
import { MagnifyingGlassIcon } from "@heroicons/vue/24/outline";
import { ref, watch } from "vue";

const inputValue = ref("");
let timer: NodeJS.Timeout;

const emit = defineEmits<{
  (e: "change", value: string): void;
}>();

watch(inputValue, (value) => {
  emit("change", value);
});
</script>

<style scoped></style>
