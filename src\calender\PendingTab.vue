<template>
  <div class="size-full overflow-hidden p-2">
    <!-- <div>Pending</div>
    <h2 class="text-lg font-semibold mb-2">No Pending Events Coming Soon</h2>
    <p class="text-gray-500 mb-4">You don't have any pending events at the moment.</p>
    <button class="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600">View Pending Events</button> -->

    <div class="w-full h-[calc(100%-180px)]x h-[75%]">
      <ScheduleXCalendar
        :calendar-app="calendarApp"
        class="size-full bg-whitex border-none rounded-t-md drop-shadow-md"
      >
        <template #monthGridEvent="{ calendarEvent }">
          <div
            class="flex flex-1 items-center gap-0.5 px-1 truncate rounded overflow-hidden"
            :class="{
              'text-green-700 bg-green-200': calendarEvent.booking.status == 'ACCEPTED',
              'text-red-700 bg-red-200': calendarEvent.booking.status == 'CANCELLED',
              'text-red-800 bg-red-200': calendarEvent.booking.status == 'REJECTED',
              'text-orange-700 bg-orange-200': calendarEvent.booking.status == 'PENDING',
            }"
          >
            <div>
              <BookmarkIcon v-if="calendarEvent.booking.status == 'ACCEPTED'" class="size-3" />
              <BookmarkSlashIcon v-if="calendarEvent.booking.status == 'CANCELLED'" class="size-3" />
              <NoSymbolIcon v-if="calendarEvent.booking.status == 'REJECTED'" class="size-3" />
              <QuestionMarkCircleIcon v-if="calendarEvent.booking.status == 'PENDING'" class="size-3" />
            </div>
            <div class="flex-1x text-ellipsis max-w-full">{{ calendarEvent.title }}</div>
          </div>
        </template>

        <template #timeGridEvent="{ calendarEvent }">
          <div
            class="flex flex-1 items-center gap-0.5 px-1 truncate rounded overflow-hidden"
            :class="{
              'text-green-700 bg-green-200': calendarEvent.booking.status == 'ACCEPTED',
              'text-red-700 bg-red-200': calendarEvent.booking.status == 'CANCELLED',
              'text-red-800 bg-red-200': calendarEvent.booking.status == 'REJECTED',
              'text-orange-700 bg-orange-200': calendarEvent.booking.status == 'PENDING',
            }"
          >
            <div>
              <BookmarkIcon v-if="calendarEvent.booking.status == 'ACCEPTED'" class="size-3" />
              <BookmarkSlashIcon v-if="calendarEvent.booking.status == 'CANCELLED'" class="size-3" />
              <NoSymbolIcon v-if="calendarEvent.booking.status == 'REJECTED'" class="size-3" />
              <QuestionMarkCircleIcon v-if="calendarEvent.booking.status == 'PENDING'" class="size-3" />
            </div>
            <div class="flex-1x text-ellipsis max-w-full">{{ calendarEvent.title }}</div>
          </div>
        </template>

        <template #dateGridEvent="{ calendarEvent }">
          <div
            class="flex flex-1 items-center gap-0.5 px-1 truncate rounded overflow-hidden"
            :class="{
              'text-green-700 bg-green-200': calendarEvent.booking.status == 'ACCEPTED',
              'text-red-700 bg-red-200': calendarEvent.booking.status == 'CANCELLED',
              'text-red-800 bg-red-200': calendarEvent.booking.status == 'REJECTED',
              'text-orange-700 bg-orange-200': calendarEvent.booking.status == 'PENDING',
            }"
          >
            <div>
              <BookmarkIcon v-if="calendarEvent.booking.status == 'ACCEPTED'" class="size-3" />
              <BookmarkSlashIcon v-if="calendarEvent.booking.status == 'CANCELLED'" class="size-3" />
              <NoSymbolIcon v-if="calendarEvent.booking.status == 'REJECTED'" class="size-3" />
              <QuestionMarkCircleIcon v-if="calendarEvent.booking.status == 'PENDING'" class="size-3" />
            </div>
            <div class="flex-1x text-ellipsis max-w-full">{{ calendarEvent.title }}</div>
          </div>
        </template>
      </ScheduleXCalendar>
    </div>
    <div class="w-full flex flex-col h-[25%] rounded-b-md bg-primary++">
      <div class="h-9 w-full flex justify-center items-center">
        <button
          @click="toggleWeatherTime"
          class="flex justify-center items-center gap- bg-white text-xs border border-gray-200 h-7 w-40 rounded-full overflow-hidden"
        >
          <div
            class="flex gap-1 justify-center items-center w-1/2 h-full rounded-full transition-all duration-300"
            :class="{ 'bg-primary-600 text-white ': weatherTimeToggle === 'weather' }"
          >
            <SunIcon class="size-4" />
            <p>Weather</p>
          </div>
          <div
            class="flex gap-1 justify-center items-center w-1/2 h-full rounded-full transition-all duration-300"
            :class="{ 'bg-primary-600 text-white ': weatherTimeToggle === 'time' }"
          >
            <ClockIcon class="size-4" />
            <p>Timezone</p>
          </div>
        </button>
      </div>
      <div class="flex-1">
        <WeatherDisplay v-if="weatherTimeToggle === 'weather'" />
        <TimezoneDisplay v-if="weatherTimeToggle === 'time'" />
      </div>
    </div>
  </div>
  <CalendarEventModal
    v-if="selectedEvent"
    :event="selectedEvent"
    @refresh="bookingsStore.fetchBookings"
    @close="selectedEvent = undefined"
  />
</template>

<script setup lang="ts">
import {
  SunIcon,
  ClockIcon,
  BookmarkIcon,
  BookmarkSlashIcon,
  QuestionMarkCircleIcon,
  NoSymbolIcon,
} from "@heroicons/vue/24/outline";
import { ScheduleXCalendar } from "@schedule-x/vue";
import {
  createCalendar,
  createViewDay,
  createViewMonthAgenda,
  createViewMonthGrid,
  createViewWeek,
  CalendarEvent,
  viewMonthGrid,
} from "@schedule-x/calendar";
import "./style/calendar.css";
import { onMounted, ref, watch } from "vue";
import WeatherDisplay from "./ui/WeatherDisplay.vue";
import TimezoneDisplay from "./ui/TimezoneDisplay.vue";
import { Booking } from "../models/booking-model";
import { createEventsServicePlugin } from "@schedule-x/events-service";
import CalendarEventModal from "./modals/CalendarEventModal.vue";
import { useBookingStore } from "../stores/bookingsStore";
import { storeToRefs } from "pinia";

export interface SelEvent extends CalendarEvent {
  booking: Booking;
}

const eventsServicePlugin = createEventsServicePlugin();

const weatherTimeToggle = ref<"weather" | "time">("weather");
const bookingsStore = useBookingStore();
const { bookings } = storeToRefs(bookingsStore);
const selectedEvent = ref<SelEvent>();

function transformBookingsToEvents(bookings: Booking[]): CalendarEvent[] {
  return bookings.map((booking) => {
    // Format date to YYYY-MM-DD HH:mm
    const formatDate = (dateString: string): string => {
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");

      return `${year}-${month}-${day} ${hours}:${minutes}`;
    };

    return {
      id: booking.id ?? 0,
      title: booking.title,
      start: formatDate(booking.startTime),
      end: formatDate(booking.endTime),
      booking,
    };
  });
}

const calendarApp = createCalendar(
  {
    selectedDate: undefined,
    defaultView: viewMonthGrid.name,
    views: [createViewDay(), createViewWeek(), createViewMonthAgenda(), createViewMonthGrid()],
    events: [],
    callbacks: {
      onEventClick(event) {
        //  console.log("Clicked Event ==>", event);
        selectedEvent.value = event as SelEvent;
      },
    },
  },
  [eventsServicePlugin]
);

function toggleWeatherTime() {
  weatherTimeToggle.value = weatherTimeToggle.value === "weather" ? "time" : "weather";
}

function updateCalendarEvents() {
  const newEvents = transformBookingsToEvents(bookings.value);
  //  console.log("NEW EVENTS => ", newEvents);
  eventsServicePlugin.set(newEvents);
}

watch(bookings.value, () => {
  //  console.log("@@@", bookings.value);
  updateCalendarEvents();
});

onMounted(async () => {
  updateCalendarEvents();
});
</script>

<style scoped></style>
