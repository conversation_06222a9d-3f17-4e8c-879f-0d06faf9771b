<template>
  <div
    :class="[
      'w-full px-2 py-3 rounded border-l-4 border-orange-300',
      'bg-orange-50 hover:border-orange-500 transition-colors duration-200',
    ]"
  >
    <div class="group flex items-start justify-between gap-2 w-full h-full">
      <div class="w-full cursor-pointer" @click="handleViewEmail">
        <div class="flex justify-between items-start mb-2">
          <div class="flex-1">
            <h3 class="text-md text-base-700 font-semibold group-hover:text-dark-400">
              {{ emailSubject }}
            </h3>
            <p class="text-[10px] text-base-600 mt-1x">From: {{ emailFrom }}</p>
          </div>
          <div class="flex gap-1">
            <button
              @click.stop="handleUnsnooze"
              class="size-8 rounded-full hover:bg-orange-200 text-orange-600 flex items-center justify-center transition-colors"
              title="Unsnooze Email"
            >
              <i class="pi pi-undo text-xs"></i>
            </button>
          </div>
        </div>

        <p class="text-sm text-base-600 line-clamp-2 mb-2 bg-orange-100/50 p-2 rounded-lg">
          {{ emailSnippet }}
        </p>

        <div class="flex justify-between items-center text-xs text-base-500">
          <span class="flex items-center gap-1 w-32">
            <i class="pi pi-bookmark mt-1"></i>
            <p class="capitalize">
              {{
                snoozedEmail.email_details?.storage_location
                  ? snoozedEmail.email_details.storage_location.split("_").join(" ")
                  : "Unknown"
              }}
            </p>
          </span>
          <div class="text-right text-xs text-orange-600 ml-2">
            <div class="flex items-center gap-1">
              <i class="pi pi-clock"></i>
              <span>{{ snoozeTimeText }}</span>
            </div>
          </div>
          <span class="w-20">{{ emailDate }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from "vue";
import { SnoozedEmailWithDetails } from "../../models/cm-model";
import { Email } from "../../types";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";

dayjs.extend(relativeTime);

const props = defineProps<{
  snoozedEmail: SnoozedEmailWithDetails;
}>();

const emit = defineEmits<{
  viewEmail: [email: Email];
  unsnooze: [snoozedEmailId: string];
}>();

const emailSubject = computed(() => {
  return props.snoozedEmail.email_details?.subject?.replace(/"/g, "") || "( NO SUBJECT )";
});

const emailFrom = computed(() => {
  return props.snoozedEmail.email_details?.from || "Unknown Sender";
});

const emailSnippet = computed(() => {
  return props.snoozedEmail.email_details?.snippet || "No preview available";
});

const emailDate = computed(() => {
  if (!props.snoozedEmail.email_details?.date) return "Unknown date";
  return dayjs(props.snoozedEmail.email_details.date).format("MMM D, YYYY");
});

const snoozeTimeText = computed(() => {
  const snoozeTime = dayjs(props.snoozedEmail.snooze_log.snoozed_until);
  const now = dayjs();

  if (snoozeTime.isBefore(now)) {
    return "Ready to resurface";
  }

  return `Until ${snoozeTime.format("MMM D, h:mm A")}`;
});

const handleViewEmail = () => {
  if (props.snoozedEmail.email_details) {
    emit("viewEmail", props.snoozedEmail.email_details);
  }
};

const handleUnsnooze = () => {
  emit("unsnooze", props.snoozedEmail.snooze_log.id);
};

onMounted(() => {
  console.log("SnoozedEmailCard mounted", props.snoozedEmail);
});
</script>

<style scoped></style>
