<template>
  <div class="size-full bg-primary-100 rounded-lg shadow-xl border border-secondary-200">
    <!-- Header -->
    <div class="flex justify-between items-center p-4 border-b border-secondary-200">
      <div class="flex items-center text-secondary-700 gap-2">
        <div class="p-2 bg-highlight-100 rounded-lg">
          <Icon icon="fluent:bot-48-filled" class="size-5 text-highlight-600" />
        </div>
        <div>
          <h3 class="font-semibold text-lg text-base-500">Meeting Bot</h3>
          <p class="text-sm text-secondary-600">Join your meeting with AI assistance</p>
        </div>
      </div>
      <button
        @click="emit('close')"
        class="text-secondary-500 hover:text-accent-600 transition-colors duration-200 rounded-lg p-2 hover:bg-secondary-100"
      >
        <XMarkIcon class="size-5" />
      </button>
    </div>

    <!-- Content -->
    <div class="p-6 space-y-6">
      <!-- Platform Selection -->
      <div class="space-y-3">
        <label class="block text-sm font-medium text-secondary-700">Platform</label>
        <ToggleGroups
          :items="platforms"
          :selected="selectedPlatform"
          @update:selected="selectedPlatform = $event"
          class="w-full"
        />
      </div>

      <!-- Meeting Link -->
      <div class="space-y-3">
        <label class="block text-sm font-medium text-secondary-700">Meeting Link</label>
        <div class="relative">
          <input
            type="url"
            name="link"
            class="w-full px-4 py-3 border border-secondary-300 bg-primary-50 rounded-lg focus:ring-2 focus:ring-highlight-500 focus:border-highlight-500 transition-colors text-base-600"
            :placeholder="
              selectedPlatform == 'MEET'
                ? 'https://meet.google.com/abc-defg-hij'
                : 'https://teams.microsoft.com/l/meetup-join/...'
            "
            v-model="link"
            required
          />
          <div class="absolute inset-y-0 right-0 flex items-center pr-3">
            <Icon :icon="selectedPlatform == 'MEET' ? 'logos:google-meet' : 'logos:microsoft-teams'" class="size-5" />
          </div>
        </div>
      </div>

      <!-- Description -->
      <div class="space-y-3">
        <label class="block text-sm font-medium text-secondary-700">Description (Optional)</label>
        <textarea
          name="description"
          rows="3"
          class="w-full px-4 py-3 border border-secondary-300 bg-primary-50 rounded-lg focus:ring-2 focus:ring-highlight-500 focus:border-highlight-500 transition-colors resize-none text-base-600"
          placeholder="Brief description of the meeting..."
          v-model="description"
        />
      </div>

      <!-- Action Button -->
      <div class="flex justify-end pt-4">
        <button
          @click="joinMeeting()"
          :disabled="!link || isJoining"
          class="flex items-center px-6 py-3 font-semibold bg-highlight-600 hover:bg-highlight-700 disabled:bg-secondary-300 disabled:cursor-not-allowed gap-2 text-primary-100 rounded-lg transition-colors duration-200 shadow-sm hover:shadow-md"
        >
          <Icon v-if="isJoining" icon="eos-icons:loading" class="size-5 animate-spin" />
          <PlayIcon v-else class="size-5" />
          <span>{{ isJoining ? "Joining..." : "Join Meeting" }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { XMarkIcon } from "@heroicons/vue/24/outline";
import { Icon } from "@iconify/vue/dist/iconify.js";
import ToggleGroups, { ToggleGroupsItem } from "../../../components/ui/ToggleGroups.vue";
import { ref } from "vue";
import { PlayIcon } from "@heroicons/vue/24/solid";
import { CreateMeetingRecording, MeetingPlatform, MeetingStatus } from "../../../models/meetings-modal";
import { useCurrentUserStore } from "../../../stores/currentUser";
import { MeetingBotService } from "../../../services/meeting-bot-service";
import { toast } from "vue-sonner";

const emit = defineEmits(["close"]);

const session = useCurrentUserStore();

const platforms: ToggleGroupsItem[] = [
  {
    label: "Google Meet",
    value: "MEET",
  },
  {
    label: "Microsoft Teams",
    value: "TEAMS",
  },
];

// Make Google Meet the default platform
const selectedPlatform = ref<string>("MEET");
const link = ref<string>("");
const description = ref<string>("");
const isJoining = ref<boolean>(false);

async function joinMeeting() {
  if (!link.value.trim()) {
    toast.error("Please enter a meeting link");
    return;
  }

  if (!session.calInfo?.id) {
    toast.error("Calendar ID is required");
    return;
  }

  isJoining.value = true;

  try {
    const newMeetingBot: CreateMeetingRecording = {
      platform: selectedPlatform.value as MeetingPlatform,
      url: link.value.trim(),
      description: description.value.trim() || null,
      userId: session.calInfo.id,
      recordingStatus: MeetingStatus.IN_PROGRESS,
      summarizationStatus: MeetingStatus.NOT_STARTED,
    };

    await MeetingBotService.startMeetingBot(newMeetingBot);

    // Show success toast
    toast.success("Meeting bot is joining the meeting!", {
      description: "The AI assistant will join your meeting shortly",
    });

    // Close the modal on success
    emit("close");
  } catch (error: any) {
    console.error("Failed to join meeting:", error);
    toast.error("Failed to join meeting", {
      description: error.message || "An unexpected error occurred",
    });
  } finally {
    isJoining.value = false;
  }
}
</script>

<style scoped></style>
