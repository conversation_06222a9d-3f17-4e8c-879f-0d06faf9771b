export enum MeetingPlatform {
    MEET = 'MEET',
    TEAMS = 'TEAMS',
    ZOOM = 'ZOOM',
  }
  
  export enum MeetingStatus {
    NOT_STARTED = 'NOT_STARTED',
    FAILED = 'FAILED',
    IN_PROGRESS = 'IN_PROGRESS',
    FINISHED = 'FINISHED',
  }
  
  export interface MeetingsRecordings {
    id: number;
    userId: number;
    platform: MeetingPlatform;
    url: string;
    recording?: string | null;
    recordingStatus: MeetingStatus;
    summarization?: string | null;
    summarizationStatus: MeetingStatus;
    bookingId?: number | null;
    description?: string | null;
  
    createdAt: Date;
    updatedAt: Date;
  
  }
  
  export interface MeetingsRecordingsDetails extends MeetingsRecordings {
    signedRecording?: string,
    summarizationDetails?: any
  
  }
  
  export type CreateMeetingRecording = Omit<MeetingsRecordings, 'id' | 'createdAt' | 'updatedAt'>;