<template>
  <div class="w-full h-full flex gap-2 justify-between px-2 items-center">
    <div v-for="(time, index) in timezones" class="py-1 px-2 flex flex-col gap-1 rounded-md drop-shadow">
      <div class="w-full h-14 flex justify-center items-center">
        <Clock :key="time.time" :time="time.time" :theme="(index + 1) % 2 == 0 ? 'light' : 'dark'" />
      </div>
      <div class="flex justify-start items-center gap-1 text-xs text-nowrap">
        <div :class="`fi fi-${time.iso}`"></div>
        <p>{{ time.country }}</p>
      </div>
      <div class="text-sm flex items-center font-semibold text-black/70 font-rozha">
        <Icon icon="ic:round-home" class="size-4 mb-0.5" />
        <div
          v-for="part of time.parts"
          :class="{
            'text-black/30': part.type == 'second',
            'w-4 text-right': !['literal', 'dayPeriod', 'hour'].includes(part.type),
          }"
        >
          {{ part.value }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import Clock from "../../components/svgs/clock/Clock.vue";
import { Icon } from "@iconify/vue/dist/iconify.js";

const timezones =
  ref<{ country: string; iso: string; time: string; date: Date; parts: { type: string; value: string }[] }[]>();

const countries = [
  { name: "USA", iso: "US", timeZone: "America/New_York" },
  { name: "UK", iso: "GB", timeZone: "Europe/London" },
  { name: "Japan", iso: "JP", timeZone: "Asia/Tokyo" },
  { name: "Australia", iso: "AU", timeZone: "Australia/Sydney" },
  { name: "Hong Kong", iso: "HK", timeZone: "Asia/Hong_Kong" },
  { name: "Algeria", iso: "DZ", timeZone: "Africa/Algiers" },
  { name: "France", iso: "FR", timeZone: "Europe/Paris" },
  { name: "Russia", iso: "RU", timeZone: "Europe/Moscow" },
  { name: "India", iso: "IN", timeZone: "Asia/Kolkata" },
];

function getTimeForCountries(countries: { name: string; iso: string; timeZone: string }[]) {
  const now = new Date();

  return countries.map((country) => {
    const formatter = new Intl.DateTimeFormat("en-US", {
      timeZone: country.timeZone,
      hour: "numeric",
      minute: "numeric",
      second: "numeric",
      hour12: true,
    });
    return {
      country: country.name,
      iso: country.iso.toLowerCase(),
      time: formatter.format(now),
      date: now,
      parts: formatter.formatToParts(now),
    };
  });
}

onMounted(() => {
  timezones.value = getTimeForCountries(countries);
  setInterval(() => {
    timezones.value = getTimeForCountries(countries);
  }, 1000);
});
</script>

<style scoped></style>
