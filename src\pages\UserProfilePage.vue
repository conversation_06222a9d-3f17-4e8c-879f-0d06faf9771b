<style scoped>
.button {
  border: none;
  color: white;
  padding: 15px 32px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 4px 2px;
  cursor: pointer;
  background-color: #274a53;
}
.custom-teal {
  background-color: #274a53;
}
</style>

<template>
  <TMSSection />

  <div class="relative z-0 flex flex-col items-center gap-4 size-full max-h-full overflow-hidden bg-primary-500">
    <!-- <button
      type="button"
      @click="
        async () => {
          await invoke('test_process_emails', {});
        }
      "
      class="absolute right-24 inline-flex justify-center text-primary-800 items-center gap-2 p-0.5 hover:underline hover:font-semibold transition-all duration-300"
    >
      Test email processing
    </button> -->
    <!-- <div class="relative group flex justify-center items-center w-2/3 bg-secondary-100 p-2 rounded-lg drop-shadow-md">
      <button
        @click="editOpen = true"
        class="absolute size-6 top-2 right-2 flex rounded-md justify-center items-center hover:bg-primary-700 hover:text-white invisiblex group-hover:visible transition-all duration-300"
      >
        <PencilSquareIcon class="size-4" />
      </button>
      <div class="w-fullx px-4 flex justify-start">
        <img :src="me.picture" class="rounded-full h-32 w-32" />
      </div>
      <div class="h-full py-4">
        <div class="text-3xl text-dark-400">{{ me.name }}</div>
        <div class="text-sm font-semibold">{{ me.phone }}</div>
        <div class="text-xs max-w-80">
          A passionate developer who loves to create awesome software and tools to make the world a better place.
        </div>
      </div>
    </div> -->
    <!-- <div class="flex flex-col w-2/3 p-2 rounded-lg drop-shadow-md gap-4"> -->
    <!-- <div class="h-24 w-full flex gap-4"> -->
    <!-- <StatisticDisplay label="Total Emails" :value="me.totalEmailCount" /> -->
    <!-- <StatisticDisplay label="Unread Emails" :value="me.unreadEmailCount" /> -->
    <!-- <StatisticDisplay label="Email Categories" :value="me.emailCategories" /> -->
    <!-- <StatisticDisplay label="Spam" :value="80" /> -->
    <!-- </div> -->

    <!-- </div> -->
    <!-- <div class="flex flex-col w-2/3 rounded-lg drop-shadow-md gap-4">
      </div> -->
    <div
      class="absolute size-full flex-col justify-center items-center bg-primary-500"
      :class="{ flex: editOpen, hidden: !editOpen }"
    >
      <button
        @click="editOpen = false"
        class="absolute size-8 top-6 left-8 flex rounded-md justify-center bg-primary-500 items-center hover:text-primary-700 invisiblex transition-all duration-150"
      >
        Back<ArrowLeftIcon />
      </button>
      <div class="relative px-6 py-10 bg-primary-400 drop-shadow-sm rounded-md">
        <h1 class="text-3xl text-center font-semibold text-primary-700 mb-4">Edit Profile</h1>
        <div class="flex flex-wrap">
          <div class="w-full lg:w-6/12 px-4 mb-4">
            <label class="block uppercase text-secondary-500 text-xs font-bold mb-2" for="first-name">
              First Name
            </label>
            <input
              type="text"
              id="first-name"
              class="border-0 px-3 py-3 placeholder-blueGray-300 text-blueGray-600 bg-secondary-200/50 rounded text-sm shadow focus:outline-none focus:ring w-full ease-linear transition-all duration-150"
              value="John"
            />
          </div>

          <div class="w-full lg:w-6/12 px-4 mb-4">
            <label class="block uppercase text-secondary-500 text-xs font-bold mb-2" for="last-name"> Last Name </label>
            <input
              type="text"
              id="last-name"
              class="border-0 px-3 py-3 placeholder-blueGray-300 bg-secondary-200/50 bg-secondary-200/50rounded text-sm shadow focus:outline-none focus:ring w-full ease-linear transition-all duration-150"
              value="Doe"
            />
          </div>

          <!-- Phone Number -->
          <div class="w-full lg:w-12/12 px-4 mb-4">
            <label class="block uppercase text-secondary-500 text-xs font-bold mb-2" for="phone"> Phone Number </label>
            <input
              type="text"
              id="phone"
              class="border-0 px-3 py-3 placeholder-blueGray-300 text-blueGray-600 bg-secondary-200/50 rounded text-sm shadow focus:outline-none focus:ring w-full ease-linear transition-all duration-150"
              value="+****************"
            />
          </div>

          <!-- About Me Section -->
          <div class="w-full lg:w-12/12 px-4 mb-4">
            <label class="block uppercase text-secondary-500 text-xs font-bold mb-2" for="about-me"> About Me </label>
            <textarea
              id="about-me"
              class="border-0 px-3 py-3 placeholder-blueGray-300 text-blueGray-600 bg-secondary-200/50 rounded text-sm shadow focus:outline-none focus:ring w-full ease-linear transition-all duration-150"
              rows="4"
            >
  A passionate developer who loves to create awesome software and tools to make the world a better place.</textarea
            >
          </div>
        </div>

        <!-- Save Button -->
        <div class="flex justify-center">
          <button
            class="bg-primary-600 text-white font-bold uppercase text-sm px-6 py-3 rounded shadow hover:shadow-lg outline-none focus:outline-none ease-linear transition-all duration-150"
            type="button"
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import TMSSection from "../components/profile/TMSSection.vue";

import { getCurrentWebviewWindow } from "@tauri-apps/api/webviewWindow";
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { invoke } from "@tauri-apps/api/core";
import { ArrowLeftStartOnRectangleIcon, ArrowLeftIcon } from "@heroicons/vue/24/outline";
import { useCurrentUserStore } from "../stores/currentUser";
import { WebviewWindow } from "@tauri-apps/api/webviewWindow";
const appWindow = getCurrentWebviewWindow();

const currentUserStore = useCurrentUserStore();

const loading = ref(false);
const me = ref({
  email: "",
  name: "",
  picture: "",
  unreadEmailCount: 0,
  totalEmailCount: 0,
  emailCategories: 0,
});
const title = ref("Tari + oauth example");
const router = useRouter();
const editOpen = ref(false);

// Function to handle login
const on_login = async () => {
  loading.value = true;
  try {
    const data = await invoke("js2rs", {
      message: "get_stored_user",
    });
    const me_data = JSON.parse(data as string);
    me.value.name = me_data.name || "";
    me.value.email = me_data.email || "";
    me.value.picture = me_data.picture || "";
    me.value.unreadEmailCount = me_data.unread_email_count || 0;
    me.value.totalEmailCount = me_data.total_email_count || 0;
    me.value.emailCategories = me_data.email_categories || 0;
  } catch (err) {
    console.error(err);
  } finally {
    loading.value = false;
  }
};

// Function to handle logout
const on_logout = async () => {
  me.value = {
    email: "",
    name: "",
    picture: "",
    unreadEmailCount: 0,
    totalEmailCount: 0,
    emailCategories: 0,
  };

  invoke("js2rs", {
    message: "logout",
  }).then((data: any) => {
    try {
      let me_data = JSON.parse(data);
      me.value.name = me_data.name || "";
      me.value.email = me_data.email || "";
      me.value.picture = me_data.picture || "";
    } catch (err) {
      console.error(err);
    }
    loading.value = false;
    try {
      currentUserStore.removeLocalStorageKeys();
      router.replace("/signin");
    } catch (error) {
      window.location.href = "/";
    }
  });
};

// Function to close the app window
const on_end = async () => {
  await appWindow.close();
};

// Automatically call on_login when component is mounted
onMounted(async () => {
  //  console.log(`MainLayout mounted()`);
  await on_login();
});
</script>
