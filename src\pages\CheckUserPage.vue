<template>
  <div class="bg-primary w-full h-[calc(100vh-32px)] max-h-[calc(100vh-32px)] overflow-hidden">
    <div class="relative flex flex-col h-full bg-primary">
      <div class="relative flex h-full">
        <SidebarMain v-if="!$route.path.includes('auth')" />
        <div class="grow overflow-y-hidden max-h-full bg-primary">
          <router-view />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SidebarMain from "../components/chat/EmailMainSideBar.vue";
import { useRouter } from "vue-router";
import { onMounted } from "vue";
import { useCurrentUserStore } from "../stores/currentUser";
import { invoke } from "@tauri-apps/api/core";
import { User, EmailContext, PhoneCallContext } from "../types";

const router = useRouter();
const currentUserStore = useCurrentUserStore();

// onMounted(async () => {
//   invoke("js2rs", { message: "get_stored_user" }).then((data: any) => {
//     try {
//       let me_data = JSON.parse(data);

//       // Check if the email is empty
//       if (!me_data.email) {
//         //  console.log("No user email found. Assigning default values and redirecting to sign-in page.");

//         // Assign empty values to user
//         const user = {
//           id: me_data.id || "",
//           email: "",
//           verified_email: false,
//           name: "",
//           given_name: "",
//           family_name: "",
//           picture: "",
//           refreshToken: "",
//           expires_in: "",
//           access_token: "",
//           username: "this is my new user data for myself if there is no username",
//           locale: "",
//           created_at: "",
//           updated_at: "",
//           last_login: "",
//           login_count: 0,
//           role: "",
//           is_active: false,
//         };

//         // Assign empty/default values to phone context
//         const phoneContextData = {
//           fullName: "",
//           jobTitle: "",
//           organization: "",
//           targetAudience: "",
//           communicationGoal: "",
//           callToAction: "",
//           tonePreference: "",
//           languageStyle: "",
//           preferredResponsePatterns: [],
//           personalizationTriggers: [],
//           keyPoints: "",
//           contextualData: [],
//           businessOverview: "",
//           pricingInfo: "",
//           frequentlyAskedQuestions: [],
//           servicesOffered: [],
//           workHours: ["", ""],
//           availabilityNote: "",
//           contactEmail: "",
//           contactPhone: "",
//           locationAddress: "",
//           urgencyLevel: "",
//           greetingMessage: "",
//           followUpMessage: "",
//           dynamicScripts: [],
//           escalationContact: "",
//           supportedLanguages: [],
//           sentimentTriggers: [],
//           additionalResources: "",
//           adaptiveLearningEnabled: false,
//           responseTimePreferences: "",
//           defaultEscalationPath: "",
//           backupAgentContact: "",
//         };

//         // Assign empty/default values to email context
//         const emailContextData = {
//           full_name: "",
//           job_title: "",
//           organization: "",
//           target_audience: "",
//           communication_goal: "",
//           call_to_action: "",
//           tone_preference: "",
//           language_style: "",
//           key_points: "",
//           known_preferences: [],
//           personal_sign_off: "",
//           email_signature: "",
//           work_hours: ["", ""],
//           availability_note: "",
//           email_length_preference: "",
//           urgency_level: "",
//         };

//         // Store user and context information in local storage
//         try {
//           currentUserStore.storeUserInfo(user);
//           //  console.log("User info stored successfully.");
//         } catch (error) {
//           console.error("Error storing user info:", error);
//         }

//         try {
//           currentUserStore.storePhoneContextInfo(phoneContextData);
//           //  console.log("Phone context stored successfully.");
//         } catch (error) {
//           console.error("Error storing phone context:", error);
//         }

//         try {
//           currentUserStore.storeEmailContextInfo(emailContextData);
//           //  console.log("Email context stored successfully.");
//         } catch (error) {
//           console.error("Error storing email context:", error);
//         }

//         // Redirect to the sign-in page
//         router.push("/signin");
//         return;
//       }
//       // Assign parsed data to the `User` type
//       const user: User = {
//         id: me_data.id,
//         email: me_data.email,
//         verified_email: me_data.verified_email,
//         name: me_data.name,
//         given_name: me_data.given_name,
//         family_name: me_data.family_name,
//         picture: me_data.picture,
//         refreshToken: me_data.refreshToken,
//         expires_in: me_data.expire_in,
//         access_token: me_data.access_token,
//         username: "this my new user data for my self if there is no username",
//         locale: "",
//         created_at: "",
//         updated_at: "",
//         last_login: "",
//         login_count: 0,
//         role: "",
//         is_active: false,
//       };
//       //  console.log(me_data);
//       const phoneContextData: PhoneCallContext = {
//         fullName: me_data.phone_context?.full_name ?? "Default Full Name",
//         jobTitle: me_data.phone_context?.job_title ?? "Default Job Title",
//         organization: me_data.phone_context?.organization ?? "Default Organization",
//         targetAudience: me_data.phone_context?.target_audience ?? "Default Target Audience",
//         communicationGoal: me_data.phone_context?.communication_goal ?? "Default Communication Goal",
//         callToAction: me_data.phone_context?.call_to_action ?? "Default Call to Action",
//         tonePreference: me_data.phone_context?.tone_preference ?? "Default Tone",
//         languageStyle: me_data.phone_context?.language_style ?? "Default Language Style",
//         preferredResponsePatterns: me_data.phone_context?.preferred_response_patterns ?? [],
//         personalizationTriggers: me_data.phone_context?.personalization_triggers ?? [],
//         keyPoints: me_data.phone_context?.key_points ?? "Default Key Points",
//         contextualData: me_data.phone_context?.contextual_data ?? [],
//         businessOverview: me_data.phone_context?.business_overview ?? "Default Business Overview",
//         pricingInfo: me_data.phone_context?.pricing_info ?? "Default Pricing Info",
//         frequentlyAskedQuestions: me_data.phone_context?.frequently_asked_questions ?? [],
//         servicesOffered: me_data.phone_context?.services_offered ?? [],
//         workHours: me_data.phone_context?.work_hours ?? ["09:00", "17:00"],
//         availabilityNote: me_data.phone_context?.availability_note ?? "Default Availability Note",
//         contactEmail: me_data.phone_context?.contact_email ?? "<EMAIL>",
//         contactPhone: me_data.phone_context?.contact_phone ?? "+123456789",
//         locationAddress: me_data.phone_context?.location_address ?? "Default Location Address",
//         urgencyLevel: me_data.phone_context?.urgency_level ?? "Default Urgency Level",
//         greetingMessage: me_data.phone_context?.greeting_message ?? "Default Greeting Message",
//         followUpMessage: me_data.phone_context?.follow_up_message ?? "Default Follow Up Message",
//         dynamicScripts: me_data.phone_context?.dynamic_scripts ?? [],
//         escalationContact: me_data.phone_context?.escalation_contact ?? "Default Escalation Contact",
//         supportedLanguages: me_data.phone_context?.supported_languages ?? ["English"],
//         sentimentTriggers: me_data.phone_context?.sentiment_triggers ?? [],
//         additionalResources: me_data.phone_context?.additional_resources ?? "Default Resources",
//         adaptiveLearningEnabled: me_data.phone_context?.adaptive_learning_enabled ?? true,
//         responseTimePreferences: me_data.phone_context?.response_time_preferences ?? "Default Response Time",
//         defaultEscalationPath: me_data.phone_context?.default_escalation_path ?? "Default Escalation Path",
//         backupAgentContact: me_data.phone_context?.backup_agent_contact ?? "Default Backup Agent Contact",
//       };

//       const emailContextData: EmailContext = {
//         full_name: me_data.email_context?.full_name || "Default Full Name",
//         job_title: me_data.email_context?.job_title || "Default Job Title",
//         organization: me_data.email_context?.organization || "Default Organization",
//         target_audience: me_data.email_context?.target_audience || "Default Target Audience",
//         communication_goal: me_data.email_context?.communication_goal || "Default Communication Goal",
//         call_to_action: me_data.email_context?.call_to_action || "Default Call to Action",
//         tone_preference: me_data.email_context?.tone_preference || "Default Tone",
//         language_style: me_data.email_context?.language_style || "Default Language Style",
//         key_points: me_data.email_context?.key_points || "Default Key Points",
//         known_preferences: me_data.email_context?.known_preferences || [],
//         personal_sign_off: me_data.email_context?.personal_sign_off || "Default Sign Off",
//         email_signature: me_data.email_context?.email_signature || "Default Email Signature",
//         work_hours: me_data.email_context?.work_hours || ["09:00", "17:00"],
//         availability_note: me_data.email_context?.availability_note || "Default Availability Note",
//         email_length_preference: me_data.email_context?.email_length_preference || "Default Length Preference",
//         urgency_level: me_data.email_context?.urgency_level || "Default Urgency Level",
//         email_length: 0,
//       };

//       // //  console.log("Assigned Email Context:", emailContextData);
//       //  console.log("Assigned me_data.phone_context:", me_data.phone_context);

//       //  console.log("Full Name:", me_data.phone_context?.fullName);
//       //  console.log("Job Title:", me_data.phone_context?.jobTitle);
//       //  console.log("Organization:", me_data.phone_context?.organization);

//       //  console.log("Assigned Phone Context:", phoneContextData);

//       try {
//         currentUserStore.storeUserInfo(user);
//         //  console.log("User info stored successfully.");
//       } catch (error) {
//         console.error("Error storing user info:", error);
//       }

//       try {
//         currentUserStore.storePhoneContextInfo(phoneContextData);
//         //  console.log("Phone context stored successfully.");
//       } catch (error) {
//         console.error("Error storing phone context:", error);
//       }
//       try {
//         currentUserStore.storeEmailContextInfo(emailContextData);
//         //  console.log("Email context stored successfully.");
//       } catch (error) {
//         console.error("Error storing email context:", error);
//       }

//       //  else {
//       //   //  console.log("user email found. Redirecting mainPage page.");
//       //     router.push("/mainpage"); // Redirect to the sign-in page
//       // }
//     } catch (err) {
//       console.error("Error parsing data or updating user:", err);
//     }
//   });
//   // router.push("/signin"); // Redirect to the sign-in page

//   // //  console.log("CurrentUserStore after initialization:", currentUserStore.currentUser);
// });
</script>
<style scoped>
h-screen {
  height: 100vh;
}
.flex-grow {
  flex-grow: 1;
}
</style>
