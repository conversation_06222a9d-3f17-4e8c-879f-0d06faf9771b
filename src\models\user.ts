export type Day = "SUNDAY" | "MONDAY" | "TUESDAY" | "WEDNESDAY" | "THURSDAY" | "FRIDAY" | "SATURDAY"
type TimeFormat = "TWELVE" | "TWENTY_FOUR"
export interface CalCreateUser {
  email: string,
  username: string,
  weekStart?: Day,
  timeZone?: string,
  timeFormat?: TimeFormat,
  locale?: string,
  avatar?: string,
  password?: string,
}

// export interface CalUser {
//   id: number;
//   email: string;
//   username: string;
//   bio: string;
//   createdDate: string;
//   role: string;
//   defaultScheduleId: number;
//   weekStart: Day;
//   timeZone: string;
//   timeFormat: string;
//   locale: string;
//   avatar: string;
//   password: string;
// }

export interface CalUser {
  id: number;
  username?: string | null;
  name?: string | null;
  email: string;
  emailVerified?: string | null; // ISO 8601 string
  bio?: string | null;
  avatarUrl?: string | null;
  timeZone?: string | null;
  weekStart?: string | null;
  startTime?: number | null;
  endTime?: number | null;
  bufferTime?: number | null;
  hideBranding?: boolean | null;
  theme?: string | null;
  appTheme?: string | null;
  createdDate?: string | null;
  trialEndsAt?: string | null;
  defaultScheduleId?: number | null;
  completedOnboarding?: boolean | null;
  locale?: string | null;
  timeFormat?: number | null;
  twoFactorSecret?: string | null;
  twoFactorEnabled?: boolean | null;
  backupCodes?: string | null;
  identityProvider?: string | null;
  identityProviderId?: string | null;
  invitedTo?: string | null;
  brandColor?: string | null;
  darkBrandColor?: string | null;
  allowDynamicBooking?: boolean | null;
  allowSeoIndexing?: boolean | null;
  receiveMonthlyDigestEmail?: boolean | null;
  metadata?: any; // or Record<string, unknown> if you want stronger typing
  verified?: boolean | null;
  role?: string | null;
  disableImpersonation?: boolean | null;
  organizationId?: number | null;
  locked?: boolean | null;
  movedToProfileId?: number | null;
  isPlatformManaged?: boolean | null;
  smsLockState?: string | null;
  smsLockReviewedByAdmin?: boolean | null;
  referralLinkId?: number | null;
}
