{"build": {"beforeDevCommand": " npm run dev", "beforeBuildCommand": " npm run build", "frontendDist": "../dist", "devUrl": "http://localhost:1420"}, "bundle": {"active": true, "category": "Productivity", "copyright": "Copyright © 2023 Seijelli LLC", "targets": "all", "externalBin": [], "icon": ["icons/32x32.png", "icons/128x128.png", "icons/256x256.png", "icons/icon.icns", "icons/icon.ico"], "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": ""}, "longDescription": "", "macOS": {"exceptionDomain": "", "frameworks": [], "providerShortName": null, "signingIdentity": null, "entitlements": "../entitlements.plist", "minimumSystemVersion": "12.0"}, "resources": [], "shortDescription": "", "linux": {"deb": {"depends": []}}}, "productName": "Oway", "mainBinaryName": "Oway", "version": "1.0.1", "identifier": "com.todarke.oway", "plugins": {}, "app": {"macOSPrivateApi": true, "withGlobalTauri": false, "windows": [], "security": {"csp": "null; connect-src ipc: http://ipc.localhost"}}}