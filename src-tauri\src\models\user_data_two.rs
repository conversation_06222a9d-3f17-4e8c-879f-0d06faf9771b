use crate::google_auth::get_token::get_token;
use chrono::{DateTime, Utc};
use home::home_dir;
use oauth2::{AccessToken, RefreshToken};
use serde_json::json;
use std::collections::HashMap;
use std::fs;
use std::fs::read_to_string;
use std::time::Duration;
use tracing::{error, info, warn};

pub const MAIN_DATA_FILENAME: &str = r#".tauri_oauth"#;

#[derive(serde::Serialize, serde::Deserialize, Debug, Clone, Default)]
pub struct User {
    pub id: String,
    pub name: String,
    pub given_name: String,
    pub family_name: String,
    pub picture: String,
    pub verified_email: bool,
    pub credentials: HashMap<String, UserCredentials>, // Store multiple emails with HashMap
}

#[derive(serde::Serialize, serde::Deserialize, Debug, Clone)]
pub struct UserCredentials {
    pub google_id: String,
    pub email: String,
    pub name: String,
    pub access_token: Option<String>,
    pub refresh_token: Option<String>,
    pub token_expiry: Option<String>,
}

impl UserCredentials {
    pub fn new(
        google_id: String,
        email: String,
        name: String,
        access_token: Option<String>,
        refresh_token: Option<String>,
        token_expiry: Option<String>,
    ) -> Self {
        UserCredentials {
            google_id,
            email,
            name,
            access_token,
            refresh_token,
            token_expiry,
        }
    }
}

#[derive(serde::Serialize, serde::Deserialize, Debug, Clone, Default)]
pub struct UserData {
    pub user: User,
}

impl UserData {
    pub fn new(user_data: &User) -> Self {
        UserData {
            user: user_data.clone(),
        }
    }

    pub fn init_user_data() -> Self {
        let home_dir = home_dir().unwrap_or("".into());
        let file_and_path = format!("{}/{}", home_dir.to_str().unwrap_or(""), MAIN_DATA_FILENAME);

        let user_data_string = read_to_string(&file_and_path).unwrap_or("".to_string());

        let user_data = match serde_json::from_str(&user_data_string) {
            Ok(result) => result,
            Err(err) => {
                warn!(?err, "Failed to read user data file. Initializing default.");
                UserData::default()
            }
        };
        info!("Loaded user data: {:#?}", user_data);
        user_data
    }

    pub fn save_me(&self) {
        let home_dir = home_dir().unwrap_or("".into());
        let file_and_path = format!("{}/{}", home_dir.to_str().unwrap_or(""), MAIN_DATA_FILENAME);

        let main_data_json = json!(self).to_string();
        if let Err(err) = fs::write(&file_and_path, main_data_json) {
            error!(?err, "Failed to save user data.");
        }
    }

    pub async fn log_in(&mut self, window: &tauri::Window) -> bool {
        let (access_token, refresh_token, expires_in) =
            match get_token(window, self.user.credentials.keys().next().unwrap(), None).await {
                Ok(tokens) => tokens,
                Err(e) => {
                    error!("Failed to retrieve access token: {}", e);
                    self.clear_credentials();
                    return false;
                }
            };

        let user_info = self.fetch_user_info(&access_token).await;
        if let Some(user_info) = user_info {
            self.user.id = user_info.id.clone();
            self.user.name = user_info.name.clone();
            self.user.given_name = user_info.given_name.clone();
            self.user.family_name = user_info.family_name.clone();
            self.user.picture = user_info.picture.clone();
            self.user.verified_email = user_info.verified_email;

            let credentials = UserCredentials::new(
                user_info.id,
                user_info.email.clone(),
                user_info.name,
                access_token.clone(),
                refresh_token.clone(),
                self.calculate_token_expiry(expires_in),
            );
            self.user.credentials.insert(user_info.email.clone(), credentials);
            self.save_me();
            self.store_credentials_in_backend(&user_info.email, &access_token, &refresh_token, &expires_in).await;
            true
        } else {
            self.clear_credentials();
            false
        }
    }

    pub async fn log_in_new_email(&mut self, window: &tauri::Window, email: &str) -> bool {
        let (access_token, refresh_token, expires_in) =
            match get_token(window, email, None).await {
                Ok(tokens) => tokens,
                Err(e) => {
                    error!("Failed to retrieve access token: {}", e);
                    return false;
                }
            };

        let user_info = self.fetch_user_info(&access_token).await;
        if let Some(user_info) = user_info {
            let credentials = UserCredentials::new(
                user_info.id.clone(),
                user_info.email.clone(),
                user_info.name.clone(),
                access_token.clone(),
                refresh_token.clone(),
                self.calculate_token_expiry(expires_in),
            );
            self.user.credentials.insert(user_info.email.clone(), credentials);
            self.save_me();
            self.store_credentials_in_backend(&user_info.email, &access_token, &refresh_token, &expires_in).await;
            true
        } else {
            false
        }
    }

    async fn fetch_user_info(&self, access_token: &Option<String>) -> Option<User> {
        let url = format!(
            "https://www.googleapis.com/oauth2/v1/userinfo?alt=json&access_token={}",
            access_token.clone().unwrap_or_default()
        );

        let resp = match reqwest::get(&url).await {
            Ok(res) => match res.text().await {
                Ok(text) => text,
                Err(e) => {
                    error!("Failed to read user info response: {}", e);
                    return None;
                }
            },
            Err(e) => {
                error!("Failed to retrieve user info: {}", e);
                return None;
            }
        };

        serde_json::from_str(&resp).ok()
    }

    fn calculate_token_expiry(&self, expires_in: Option<Duration>) -> Option<String> {
        expires_in.map(|duration| {
            (Utc::now() + chrono::Duration::from_std(duration).unwrap()).to_rfc3339()
        })
    }

    async fn store_credentials_in_backend(
        &self,
        email: &str,
        access_token: &Option<String>,
        refresh_token: &Option<String>,
        expires_in: &Option<Duration>,
    ) {
        let client = reqwest::Client::new();
        let check_url = format!("http://localhost:9000/users/?email={}", email);
        let response = client.get(&check_url).send().await;

        if let Ok(resp) = response {
            if resp.status().is_success() {
                let existing_users: serde_json::Value = resp.json().await.unwrap_or_default();

                if existing_users.as_array().unwrap_or(&vec![]).is_empty() {
                    // User does not exist, create new
                    let user_create_url = "http://localhost:9000/users/";
                    client.post(user_create_url)
                        .json(&json!({ "email": email, "name": self.user.name }))
                        .send()
                        .await
                        .expect("Failed to create user");

                    // Store email account information
                    let email_account_url = "http://localhost:9000/email-accounts/";
                    client.post(email_account_url)
                        .json(&json!({
                            "email": email,
                            "email_provider": "gmail",
                            "access_token": access_token.clone(),
                            "refresh_token": refresh_token.clone(),
                            "token_expiry": self.calculate_token_expiry(*expires_in),
                        }))
                        .send()
                        .await
                        .expect("Failed to store email account");
                }
            }
        }
    }

    fn clear_credentials(&mut self) {
        self.user.credentials.clear();
        self.save_me();
    }
}