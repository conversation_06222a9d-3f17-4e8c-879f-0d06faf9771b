use crate::models::email::Email;
use once_cell::sync::OnceCell;
use tokio::sync::mpsc::Sender; // adjust based on where your Email struct is

pub static EMAIL_TX: OnceCell<Sender<Email>> = OnceCell::new();

pub fn set_email_sender(sender: Sender<Email>) {
    EMAIL_TX.set(sender).expect("EMAIL_TX can only be set once");
}

pub fn get_email_sender() -> Option<&'static Sender<Email>> {
    EMAIL_TX.get()
}
