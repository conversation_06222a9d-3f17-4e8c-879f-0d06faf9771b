<template>
<div class="size-full flex">
    <div class="flex flex-col gap-2 flex-1 p-2 overflow-y-auto custom-scrollbar">
      <MeetingCard v-for="meeting in meetings" :meeting="meeting" />
    </div>
    <MeetingDrawer :meeting="meetingsStore.selectedMeeting ?? undefined" />
  </div>
</template>

<script setup lang="ts">
import { useMeetingsStore } from "../stores/meetingsStore";
import { storeToRefs } from "pinia";
import MeetingCard from "./ui/MeetingCard.vue";
import MeetingDrawer from "./ui/MeetingDrawer.vue";
const meetingsStore = useMeetingsStore();
const { meetings } = storeToRefs(meetingsStore);
</script>

<style scoped></style>