{"name": "Oway", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri", "build-only": "vite build"}, "dependencies": {"@ckeditor/ckeditor5-vue": "^7.3.0", "@growthbunker/vueflags": "^0.1.14", "@headlessui/vue": "^1.7.22", "@heroicons/vue": "^2.1.5", "@preact/signals": "^1.3.0", "@schedule-x/calendar": "^2.4.1", "@schedule-x/events-service": "^2.12.1", "@schedule-x/theme-default": "^2.4.1", "@schedule-x/vue": "^2.3.1", "@svgdotjs/svg.js": "^3.2.4", "@tailwindcss/forms": "^0.5.3", "@tauri-apps/api": "2.0.0", "@tauri-apps/plugin-clipboard-manager": "~2", "@tauri-apps/plugin-dialog": "~2", "@tauri-apps/plugin-shell": "~2", "@vueup/vue-quill": "^1.2.0", "axios": "^1.7.2", "dayjs": "^1.11.7", "fix": "^0.0.6", "flag-icons": "^7.2.3", "ldrs": "^1.0.2", "marked": "^4.3.0", "pinia": "^2.0.35", "preact": "^10.24.3", "primeicons": "^7.0.0", "primevue": "^3.52.0", "qalendar": "^3.8.1", "uuid": "^9.0.0", "vue": "^3.2.45", "vue-quill-editor": "^3.0.6", "vue-router": "^4.1.6", "vue-sonner": "^1.3.0", "vue-tel-input": "^9.2.0", "vue3-google-login": "^2.0.26", "vuetify": "^3.7.3", "zod": "^3.24.1"}, "devDependencies": {"@iconify/json": "^2.2.309", "@iconify/tailwind": "^1.2.0", "@iconify/vue": "^4.3.0", "@tailwindcss/typography": "^0.5.9", "@tauri-apps/cli": "^2.5.0", "@types/country-telephone-data": "^0.6.3", "@types/marked": "^4.0.8", "@types/node": "^18.7.10", "@types/uuid": "^9.0.1", "@types/vue-tel-input": "^2.1.7", "@vitejs/plugin-vue": "^4.0.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.21", "tailwindcss": "^3.4.3", "typescript": "^4.6.4", "vite": "^4.0.0", "vue-tsc": "^1.0.11"}}