<template>
  <div class="size-full flex flex-col gap-4">
    <div class="p-5 rounded-lg border-2 border-primary-200 flex flex-col gap-2">
      <div class="flex flex-col">
        <label class="text-primary-800 font-semibold" for="">Title</label>
        <input
          class="bg-primary-200 outline-none border-none rounded-md text-primary-900"
          type="text"
          placeholder="Quick chat"
          :value="editData.title"
          @change="(e:Event)=>updateTitle((e.target as HTMLInputElement).value)"
        />
      </div>
      <div class="flex flex-col">
        <label class="text-primary-800 font-semibold" for="">Description</label>
        <textarea
          v-model="editData.description"
          class="bg-primary-200 outline-none border-none rounded-md text-primary-900"
          placeholder="A quick video meeting about..."
        ></textarea>
      </div>
      <div class="flex flex-col">
        <label class="text-primary-800 font-semibold" for="">URL</label>
        <div class="flex">
          <div class="bg-primary-300 rounded-l-md px-2 flex justify-center items-center text-primary-700">username</div>
          <input
            v-model="editData.slug"
            class="bg-primary-200 flex-1 outline-none border-none rounded-r-md text-primary-900"
            type="text"
            placeholder="Quick chat"
          />
        </div>
      </div>
    </div>
    <div class="p-5 rounded-lg border-2 border-primary-200 flex flex-col gap-2">
      <div class="flex flex-col">
        <label class="text-primary-800 font-semibold" for="">Duration</label>
        <div class="flex">
          <input
            class="bg-primary-200 flex-1 outline-none border-none rounded-l-md text-primary-900"
            type="text"
            placeholder="15"
            v-model="editData.length"
          />
          <div class="bg-primary-300 rounded-r-md px-2 flex justify-center items-center text-primary-700">minutes</div>
        </div>
      </div>
      <!-- <div class="flex items-center gap-2">
        <input type="checkbox" class="bg-primary-400 rounded-md" name="" id="toggle-duration" />
        <label for="toggle-duration">Allow users to select duration</label>
      </div> -->
    </div>
    <ToggleAccordion
      id="hide-event-profile"
      :can-open="false"
      :open="editData.hidden"
      title="Hidden Event"
      sub-title="Hide event from the profile."
      @on-toggle="(value:boolean)=>editData.hidden = value"
    >
    </ToggleAccordion>
    <ToggleAccordion
      id="require-confirmation"
      :can-open="false"
      :open="editData.requiresConfirmation"
      title="Requires confirmation"
      sub-title="The booking needs to be manually confirmed before it is pushed to the integrations and a confirmation mail is sent."
      @on-toggle="(value:boolean)=>editData.requiresConfirmation = value"
    >
    </ToggleAccordion>
    <div class="p-5 rounded-lg border-2 border-primary-200 flex flex-col gap-2">
      <div class="flex flex-col gap-2">
        <label class="text-primary-800 font-semibold" for="">Locations</label>
        <div
          v-for="(location, index) of editData.locations"
          class="flex flex-col gap-2 drop-shadow p-1 border-dotted rounded-lg border-primary-400 border"
        >
          <div class="flex justify-between items-center">
            <label class="text-xs font-semibold">Location {{ index + 1 }}</label>
            <button
              @click="() => deleteLocation(index)"
              class="text-primary-600 hover:text-red-500 transition-all duration-200"
            >
              <XMarkIcon class="size-4" />
            </button>
          </div>
          <div class="flex gap-1 justify-center items-center">
            <div
              class="bg-primary-200 hover:bg-primary-300 transition-all duration-200 cursor-pointer w-fit p-1 grow rounded-md text-xs flex justify-start items-center gap-1 h-full"
              v-for="lc of eventLocationsTypes"
              @click="()=>changeLocationType(index,lc.value as EventLocationType)"
              :class="{ 'bg-primary-400': lc.value === location.type }"
            >
              <component :is="lc.icon" class="size-4" />
              <div>{{ lc.label }}</div>
            </div>
          </div>
          <div v-if="['inPerson', 'link', 'userPhone'].includes(location.type)">
            <div class="flex">
              <div class="bg-primary-300 rounded-l-md px-2 flex justify-center items-center text-primary-700">
                <MapPinIcon class="size-6" v-if="location.type === 'userPhone'" />
                <LinkIcon class="size-6" v-if="location.type === 'link'" />
                <PhoneIcon class="size-6" v-if="location.type === 'inPerson'" />
              </div>
              <input
                v-if="location.type === 'inPerson'"
                v-model="location.address"
                class="bg-primary-200 flex-1 outline-none border-none rounded-r-md text-primary-900"
                type="text"
                placeholder="Quick chat"
              />
              <input
                v-model="location.hostPhoneNumber"
                v-else-if="location.type === 'userPhone'"
                class="bg-primary-200 flex-1 outline-none border-none rounded-r-md text-primary-900"
                type="text"
                placeholder="Quick chat"
              />
              <input
                v-model="location.link"
                v-else-if="location.type === 'link'"
                class="bg-primary-200 flex-1 outline-none border-none rounded-r-md text-primary-900"
                type="text"
                placeholder="Quick chat"
              />
            </div>
          </div>
        </div>
        <div
          @click="addNewLocation"
          class="w-full border-dashed border-2 border-primary-500 rounded-lg text-primary-600 py-1 flex justify-center items-center hover:bg-primary-300 transition-all duration-300 cursor-pointer"
        >
          <PlusIcon class="size-6" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { LinkIcon, MapPinIcon, PhoneIcon, PlusIcon, XMarkIcon } from "@heroicons/vue/24/outline";
import { ref, watch } from "vue";
import {
  defaultEventSetup,
  EventLocation,
  EventLocationType,
  EventTypeSetup,
} from "../../../../models/event-type-model";
import ToggleAccordion from "../../../../components/ui/ToggleAccordion.vue";

const defaultLocation: EventLocation = {
  type: "inPerson",
};

const eventLocationsTypes = [
  { value: "inPerson", label: "Organizer address", icon: MapPinIcon },
  { value: "attendeeInPerson", label: "Attendee address", icon: MapPinIcon },
  { value: "somewhereElse", label: "Custom address", icon: MapPinIcon },
  { value: "link", label: "Link", icon: LinkIcon },
  { value: "userPhone", label: "Organizer Phone", icon: PhoneIcon },
  { value: "phone", label: "Attendee Phone", icon: PhoneIcon },
];

const props = defineProps({
  setup: {
    type: Object,
    default: null,
  },
});

const eventLocations = ref<EventLocation[]>([]);
// const data = ref<EventTypeSetup>((props.setup as EventTypeSetup) ?? { ...defaultData });
const editData = ref<EventTypeSetup>((props.setup as EventTypeSetup) ?? { ...defaultEventSetup });

function addNewLocation() {
  editData.value.locations.push({ ...defaultLocation });
}

function changeLocationType(index: number, type: EventLocationType) {
  editData.value.locations[index] = { type };
}

function deleteLocation(index: number) {
  editData.value.locations = editData.value.locations.filter((_, i) => i != index);
}

function updateTitle(value: string) {
  editData.value.title = value;
  const words = value.split(" ");
  editData.value.slug = words.length > 1 ? words.join("-") : value;
}

const emit = defineEmits<{
  (e: "change", value: EventTypeSetup): void;
}>();

watch(
  editData,
  (v) => {
    //  console.log("SETUP ==>", v);
    emit("change", v);
  },
  { deep: true }
);
</script>

<style scoped></style>
