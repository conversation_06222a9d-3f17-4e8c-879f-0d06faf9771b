/** @type {import('tailwindcss').Config} */

const colors = {
  primary: {
    DEFAULT: "#F9F4ED",
    900: "#3d301f",
    800: "#79613e",
    700: "#af9064",
    600: "#cebba1",
    500: "#ede6dc",
    400: "#f1ece4",
    300: "#f5f1eb",
    200: "#f8f5f2",
    100: "#fcfaf8",
    // 50: "#f9f4ed",
    // 100: "#f5ede0",
    // 200: "#e9d6bf",
    // 300: "#dbbb96",
    // 400: "#cb9a6c",
    // 500: "#c0804f",
    // 600: "#b36d43",
    // 700: "#955739",
    // 800: "#784734",
    // 900: "#623b2d",
    // 950: "#341e16",
  },
  secondary: {
    // DEFAULT: "#2C4B46",
    DEFAULT: "#bc9981",
    900: "#291e16",
    800: "#533b2c",
    700: "#7c5941",
    600: "#a67757",
    500: "#bc9981",
    400: "#caad99",
    300: "#d7c1b3",
    200: "#e4d6cc",
    100: "#f2eae6",
    // 50: "#f4f9f8",
    // 100: "#dbece7",
    // 200: "#b6d9cf",
    // 300: "#8abeb1",
    // 400: "#61a092",
    // 500: "#478578",
    // 600: "#376a60",
    // 700: "#2f564f",
    // 800: "#2c4b46",
    // 900: "#253c38",
    // 950: "#11221f",
  },
  accent: {
    DEFAULT: "#dc9c8c",
    900: "#371911",
    800: "#6f3122",
    700: "#a64a33",
    600: "#cb6e56",
    500: "#dc9c8c",
    400: "#e3b1a4",
    300: "#eac5bb",
    200: "#f1d8d2",
    100: "#f8ece8",
  },
  highlight: {
    DEFAULT: "#cd8028",
    900: "#291a08",
    800: "#523310",
    700: "#7b4d18",
    600: "#a46620",
    500: "#cd8028",
    400: "#dc9a4e",
    300: "#e5b37a",
    200: "#edcca6",
    100: "#f6e6d3",
  },
  dark: {
    DEFAULT: "#1c444e",
    900: "#060e10",
    800: "#0b1b1f",
    700: "#11292f",
    600: "#17373f",
    500: "#1c444e",
    400: "#32788a",
    300: "#4da8bf",
    200: "#89c5d4",
    100: "#c4e2ea",
  },
  muted: {
    DEFAULT: "#2c4b46",
    900: "#090f0e",
    800: "#111e1c",
    700: "#1a2c29",
    600: "#233b37",
    500: "#2c4b46",
    400: "#487b73",
    300: "#6ba89e",
    200: "#9cc5be",
    100: "#cee2df",
  },
  light: {
    DEFAULT: "#efefef",
    900: "#303030",
    800: "#606060",
    700: "#909090",
    600: "#c0c0c0",
    500: "#efefef",
    400: "#f3f3f3",
    300: "#f6f6f6",
    200: "#f9f9f9",
    100: "#fcfcfc",
  },
  base: {
    DEFAULT: "#222222",
    900: "#070707",
    800: "#0d0d0d",
    700: "#141414",
    600: "#1b1b1b",
    500: "#222222",
    400: "#4e4e4e",
    300: "#7a7a7a",
    200: "#a6a6a6",
    100: "#d3d3d3",
  },
};
export default {
  content: ["./index.html", "./src/**/*.{vue, ts}"],
  theme: {
    extend: {
      colors: colors,
      fontFamily: {
        rozha: ["Rozha One", "serif"],
        poppins: ["Poppins", "serif"],
      },
    },
  },
  plugins: [require("@tailwindcss/forms"), require("@tailwindcss/typography")],
};
