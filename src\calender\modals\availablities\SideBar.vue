<template>
  <div class="w-1/4 h-full py-6 px-4 bg-primary border-r border-primary-400">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <button
        @click="() => emit('close')"
        class="p-2 text-secondary-500 hover:text-secondary-700 hover:bg-primary-200 rounded-lg transition-all duration-200"
      >
        <ChevronLeftIcon class="w-5 h-5" />
      </button>
      <h2 class="text-lg font-semibold text-base-500">Schedules</h2>
      <button
        @click="
          () => {
            emit('newSchedule');
            selSchedule = defaultSelSchedule;
            editableSch = { ...defaultSelSchedule };
          }
        "
        class="p-2 text-highlight-600 hover:text-highlight-700 hover:bg-highlight-100 rounded-lg transition-all duration-200"
        title="Create new schedule"
      >
        <PencilIcon class="w-4 h-4" />
      </button>
    </div>
    <!-- Schedule List -->
    <div class="space-y-2">
      <div
        class="group relative p-3 rounded-lg border transition-all duration-200 cursor-pointer"
        v-if="props.schedules"
        v-for="schedule in props.schedules"
        :class="
          editableSch && editableSch.id == schedule.id
            ? 'bg-secondary-100 border-secondary-300 shadow-sm'
            : 'bg-primary-200 border-primary-400 hover:bg-primary-300 hover:border-primary-500'
        "
        @click="
          () => {
            emit('selectSchedule', schedule);
            selectSchedule(schedule);
          }
        "
      >
        <div class="flex flex-col gap-2">
          <div class="flex items-start justify-between">
            <h3 class="font-medium text-base-500 truncate pr-2">{{ schedule.name }}</h3>
            <div class="flex items-center gap-1">
              <span
                class="text-xs bg-highlight-500 text-primary px-2 py-1 rounded-full font-medium"
                v-if="session.calInfo?.defaultScheduleId === schedule.id"
              >
                Default
              </span>
              <button
                @click.stop="() => emit('deleteSchedule', schedule)"
                class="opacity-0 group-hover:opacity-100 p-1 text-accent-600 hover:text-accent-700 hover:bg-accent-100 rounded transition-all duration-200"
                title="Delete schedule"
              >
                <TrashIcon class="w-4 h-4" />
              </button>
            </div>
          </div>
          <div class="flex items-center gap-1 text-xs text-secondary-600">
            <GlobeAltIcon class="w-3 h-3" />
            <span class="truncate">{{ schedule.timeZone.replace(/_/g, " ") }}</span>
          </div>
        </div>
      </div>

      <!-- Empty state -->
      <div v-if="!props.schedules || props.schedules.length === 0" class="text-center py-8">
        <div class="text-secondary-400 mb-2">
          <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
        </div>
        <p class="text-sm text-secondary-600 mb-3">No schedules yet</p>
        <button
          @click="
            () => {
              emit('newSchedule');
              selSchedule = defaultSelSchedule;
              editableSch = { ...defaultSelSchedule };
            }
          "
          class="text-sm text-highlight-600 hover:text-highlight-700 font-medium"
        >
          Create your first schedule
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useCurrentUserStore } from "../../../stores/currentUser";
import { CalSchedule, defaultSelSchedule } from "../../../models/schedule-model";
import { ChevronLeftIcon, GlobeAltIcon, PencilIcon, TrashIcon } from "@heroicons/vue/24/outline";

const session = useCurrentUserStore();

const props = defineProps({
  schedules: {
    type: Object,
  },
});

const emit = defineEmits(["close", "selectSchedule", "deleteSchedule", "newSchedule"]);

const editableSch = ref<CalSchedule>();
const selSchedule = ref<CalSchedule>();

function selectSchedule(s: CalSchedule) {
  selSchedule.value = s;
  editableSch.value = { ...s };
}
</script>

<style scoped></style>
