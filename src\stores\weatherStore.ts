import { defineStore } from "pinia"
import { WeatherForecast } from "../models/weather-models"
import { WeatherService } from "../services/weather-api/weather-service";

export const useWeatherStore = defineStore("weatherStore", {
  state() {
    return {
      data: {} as WeatherForecast
    }
  },
  actions: {
    async init() {
      const pos = localStorage.getItem("gps") ? JSON.parse(localStorage.getItem("gps") ?? "") : null;
      const weather = await WeatherService.getWeather(pos ? `${pos.latitude},${pos.longitude}` : "algeria", 5);
      //  console.log("Weather data =>", weather?.data as WeatherForecast);
      this.data = weather?.data as WeatherForecast;
    },
  }
})
