<template>
  <div class="relative w-full">
    <!-- Select Button -->
    <button
      @click="isOpen = !isOpen"
      class="w-full px-4 py-2 text-left bg-primary-300/40 border rounded-md flex items-center justify-between hover:bg-primary-300/60"
      :aria-expanded="isOpen"
    >
      <span class="truncate">
        {{ selectedTimezone ? getSelectedLabel : "Select timezone..." }}
      </span>
      <ChevronDownIcon class="size-5" />
    </button>

    <div v-if="isOpen" class="absolute mt-1 w-full bg-primary-100 border rounded-md shadow-lg z-10">
      <!-- Search Input -->
      <input
        type="text"
        v-model="searchTerm"
        placeholder="Search timezones..."
        class="w-full p-2 border-none focus:outline-none bg-primary-300/30 px-10"
      />

      <!-- Timezone List -->
      <div class="max-h-64 overflow-y-auto custom-scrollbar">
        <template v-if="filteredTimezones.length">
          <div
            v-for="timezone in filteredTimezones"
            :key="timezone.value"
            @click="selectTimezone(timezone)"
            class="flex items-center justify-start px-4 py-2 hover:bg-primary-200 cursor-pointer"
          >
            <div class="size-6">
              <CheckIcon v-if="selectedTimezone === timezone.value" class="text-blue-600 size-5" />
            </div>
            <div class="flex flex-col">
              <span class="text-sm">{{ timezone.label }}</span>
              <span class="text-gray-500 text-xs"> {{ timezone.time }} ({{ timezone.offset }}) </span>
            </div>
          </div>
        </template>
        <div v-else class="p-2 text-gray-500">No timezone found</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { CheckIcon, ChevronDownIcon } from "@heroicons/vue/24/outline";
import { ref, computed, onMounted, onBeforeUnmount, watch } from "vue";

const props = defineProps({
  modelValue: {
    type: String,
    default: "",
  },
  value: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["select"]);

const isOpen = ref(false);
const searchTerm = ref("");
const selectedTimezone = ref(props.value);
const timezones = ref([]);

// watch(props.modelValue, (value) => {
//   selectedTimezone.value = props.modelValue;
// });

const getTimezones = () => {
  const timezoneList = Intl.supportedValuesOf("timeZone");
  return timezoneList.map((zone) => {
    const time = new Date().toLocaleTimeString("en-US", { timeZone: zone });
    const offset = new Date()
      .toLocaleString("en-US", {
        timeZone: zone,
        timeZoneName: "shortOffset",
      })
      .split(" ")
      .pop();
    return {
      value: zone,
      label: zone.replace(/_/g, " "),
      time,
      offset,
    };
  });
};

onMounted(() => {
  timezones.value = getTimezones();
  //  console.log("TimeZones", timezones.value);
  //  console.log("Selected TimeZone", props.value);
});

const filteredTimezones = computed(() => {
  return timezones.value.filter(
    (tz) =>
      tz.label.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
      tz.value.toLowerCase().includes(searchTerm.value.toLowerCase())
  );
});

const getSelectedLabel = computed(() => {
  const timezone = timezones.value.find((tz) => tz.value === selectedTimezone.value);
  return timezone ? timezone.label : "";
});

const selectTimezone = (timezone) => {
  selectedTimezone.value = timezone.value;
  // emit("update:modelValue", timezone.value);
  emit("select", timezone.value);
  isOpen.value = false;
  searchTerm.value = "";
};

const closeDropdown = () => {
  isOpen.value = false;
  searchTerm.value = "";
};

const vClickOutside = {
  mounted(el, binding) {
    el._clickOutside = (event) => {
      if (!(el === event.target || el.contains(event.target))) {
        binding.value(event);
      }
    };
    document.addEventListener("click", el._clickOutside);
  },
  unmounted(el) {
    document.removeEventListener("click", el._clickOutside);
  },
};
</script>
