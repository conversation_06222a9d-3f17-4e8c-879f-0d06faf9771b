

export interface User {
    email: string;
    name: string;
    timeZone: string;
    locale: string;
  }
  
  export interface Payment {
    id: number;
    success: boolean;
    paymentOption: string;
  }
  
  export interface Location {
    optionValue: string;
    value: string;
  }
  
  export interface Responses {
    email: string;
    name: string;
    location: Location;
  }
  
  export type BookingStatus = "ACCEPTED" | "CANCELLED" | "PENDING" | "REJECTED";
  
  export interface Booking {
    id?: number;
    userId: number;
    description?: string;
    eventTypeId: number;
    uid: string;
    title: string;
    startTime: string;
    endTime: string;
    attendees: User[];
    user: User;
    payment?: Payment[];
    metadata?: Record<string, unknown>;
    responses?: Responses;
    status?: BookingStatus;
  }