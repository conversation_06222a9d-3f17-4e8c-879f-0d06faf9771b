use chrono::{DateTime, Duration, NaiveTime, TimeZone, Utc};
use serde::{Deserialize, Serialize};
use std::collections::BTreeMap;
use std::str::FromStr;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CalendarEvent {
    pub start: DateTime<Utc>,
    pub end: DateTime<Utc>,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UserContext {
    pub full_name: String,
    pub name: String,
    pub timezone: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct EmailThread {
    pub subject: String,
    pub from_email: String,
    pub content: String,
}

pub fn find_available_meeting_slots(
    events: Vec<CalendarEvent>,
    working_hours: (NaiveTime, NaiveTime),
    date: DateTime<Utc>,
    min_slot_minutes: i64,
) -> Vec<(DateTime<Utc>, DateTime<Utc>)> {
    let mut busy: Vec<(DateTime<Utc>, DateTime<Utc>)> = events
        .into_iter()
        .filter(|e| e.start.date_naive() == date.date_naive())
        .map(|e| (e.start, e.end))
        .collect();

    busy.sort_by_key(|(start, _)| *start);

    let mut slots = Vec::new();
    let mut current_time = date
        .date_naive()
        .and_time(working_hours.0)
        .unwrap();
    let end_of_day = date
        .date_naive()
        .and_time(working_hours.1)
        .unwrap();

    let mut current_utc = Utc.from_utc_datetime(&current_time);
    let end_utc = Utc.from_utc_datetime(&end_of_day);

    for (busy_start, busy_end) in busy {
        if current_utc < busy_start {
            let free_slot_end = busy_start;
            let free_duration = free_slot_end - current_utc;
            if free_duration.num_minutes() >= min_slot_minutes {
                slots.push((current_utc, free_slot_end));
            }
        }
        if current_utc < busy_end {
            current_utc = busy_end;
        }
    }

    if current_utc < end_utc {
        let free_duration = end_utc - current_utc;
        if free_duration.num_minutes() >= min_slot_minutes {
            slots.push((current_utc, end_utc));
        }
    }

    slots
}

pub fn generate_meeting_reply(
    context: &UserContext,
    email: &EmailThread,
    available_slots: Vec<(DateTime<Utc>, DateTime<Utc>)>,
) -> String {
    if available_slots.is_empty() {
        return format!("{} is not free at the requested times.", context.name);
    }

    let slot_lines: Vec<String> = available_slots
        .into_iter()
        .map(|(start, end)| {
            let local_start = start.with_timezone(&chrono_tz::Tz::from_str(&context.timezone).unwrap());
            let local_end = end.with_timezone(&chrono_tz::Tz::from_str(&context.timezone).unwrap());
            format!("{}-{}", local_start.format("%I:%M%p"), local_end.format("%I:%M%p"))
        })
        .collect();

    format!("{} is free at the following times:\n{}", context.name, slot_lines.join("\n"))
}

pub fn generate_prompt(context: &UserContext, email: &EmailThread, current_date: &str) -> String {
    format!(
        "You are {full_name}'s executive assistant. You are a top-notch executive assistant who cares about {name} performing as well as possible.

The below email thread has been flagged as requesting time to meet. Your SOLE purpose is to survey {name}'s calendar and schedule meetings for {name}.

If the email is suggesting some specific times, then check if {name} is available then.

If the emails asks for time, use the tool to find valid times to meet (always suggest them in {tz}).

If they express preferences in their email thread, try to abide by those. Do not suggest times they have already said won't work.

Try to send available spots in as big of chunks as possible. For example, if {name} has 1pm-3pm open, send:

1pm-3pm

NOT

1-1:30pm
1:30-2pm
2-2:30pm
2:30-3pm

Do not send time slots less than 15 minutes in length.

Your response should be extremely high density. You should not respond directly to the email, but rather just say factually whether {name} is free, and what time slots. Do not give any extra commentary.

The current date is {current_date}

Here is the email thread:

From: {author}
Subject: {subject}

{email_thread}",
        full_name = context.full_name,
        name = context.name,
        tz = context.timezone,
        current_date = current_date,
        author = email.from_email,
        subject = email.subject,
        email_thread = email.content,
    )
}
