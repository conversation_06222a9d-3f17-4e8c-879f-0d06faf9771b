-- Your SQL goes here
CREATE TABLE drafts (
    id VARCHAR(50) PRIMARY KEY NOT NULL,  -- Unique draft ID
    "from" TEXT NOT NULL,  -- Sender's email address
    "to" TEXT NOT NULL,  -- Recipient's email addresses (CSV)
    subject TEXT NOT NULL,  -- Email subject
    body TEXT NOT NULL,  -- Email subject
    cc TEXT,  -- CC recipients (CSV)
    bcc TEXT,  -- BCC recipients (CSV)
    thread_id VARCHAR(50),  -- ID of the conversation thread this email belongs to
    parent_email_id VARCHAR(50),  -- Reference to the parent email in the thread
    is_send BOOLEAN DEFAULT FALSE,  -- Indicates if the email content was AI-generated
    status TEXT NOT NULL CHECK( status IN ('accepted', 'refused', 'waiting_user_action') ),
    task_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- Record creation timestamp
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Record update timestamp
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE SET NULL
);
