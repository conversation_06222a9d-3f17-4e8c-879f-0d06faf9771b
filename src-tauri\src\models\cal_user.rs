use std::{ env, fs::read_to_string };

use chrono::{ DateTime, Utc };
use dirs::home_dir;
use reqwest::Client;
use serde::{ Deserialize, Serialize };
use serde_json::json;
use tracing::info;

use crate::models::user_data::{ UserData, MAIN_DATA_FILENAME };

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct CalUser {
    pub id: i32,
    pub username: Option<String>,
    pub name: Option<String>,
    pub email: String,
    pub email_verified: Option<DateTime<Utc>>,
    pub bio: Option<String>,
    pub avatar_url: Option<String>,
    pub time_zone: Option<String>,
    pub week_start: Option<String>,
    pub start_time: Option<i32>,
    pub end_time: Option<i32>,
    pub buffer_time: Option<i32>,
    pub hide_branding: Option<bool>,
    pub theme: Option<String>,
    pub app_theme: Option<String>,
    pub created_date: Option<DateTime<Utc>>,
    pub trial_ends_at: Option<DateTime<Utc>>,
    pub default_schedule_id: Option<i32>,
    pub completed_onboarding: Option<bool>,
    pub locale: Option<String>,
    pub time_format: Option<i32>,
    pub two_factor_secret: Option<String>,
    pub two_factor_enabled: Option<bool>,
    pub backup_codes: Option<String>,
    pub identity_provider: Option<String>,
    pub identity_provider_id: Option<String>,
    pub invited_to: Option<String>,
    pub brand_color: Option<String>,
    pub dark_brand_color: Option<String>,
    pub allow_dynamic_booking: Option<bool>,
    pub allow_seo_indexing: Option<bool>,
    pub receive_monthly_digest_email: Option<bool>,
    pub metadata: Option<serde_json::Value>,
    pub verified: Option<bool>,
    pub role: Option<String>,
    pub disable_impersonation: Option<bool>,
    pub organization_id: Option<i32>,
    pub locked: Option<bool>,
    pub moved_to_profile_id: Option<i32>,
    pub is_platform_managed: Option<bool>,
    pub sms_lock_state: Option<String>,
    pub sms_lock_reviewed_by_admin: Option<bool>,
    pub referral_link_id: Option<i32>,
}

#[derive(Debug, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct CalUserRes {
    pub user: CalUser,
}

impl CalUser {
    pub async fn create_user(user_name: String, email: String) -> Option<CalUser> {
        info!("Creating user: {} - {}", user_name, email);
        let client = Client::new();
        let api_url = env::var("VITE_CAL_API_HOST").expect("API_URL must be set");
        println!("api_url: {}", api_url);

        match
            client
                .post(format!("{}/api/users", api_url)) // Replace with actual URL
                .json(&json!({ "username": user_name, "email": email }))
                .send().await
        {
            Ok(resp) => {
                match resp.json::<CalUserRes>().await {
                    Ok(data) => {
                        println!("data: {:?}", data);
                        Some(data.user)
                    }
                    Err(err) => {
                        eprintln!("Error parsing response: {:?}", err);
                        None
                    }
                }
            }
            Err(err) => {
                eprintln!("Error sending request: {:?}", err);
                None
            }
        }
    }

    pub async fn get_user_by_email(email: &str) -> Option<CalUser> {
        info!("Getting user by email: {}", email);
        let client = Client::new();
        let api_url = env::var("VITE_CAL_API_HOST").expect("API_URL must be set");
        println!("api_url: {}", api_url);
        // Replace with actual backend URL

        let response = client
            .get(format!("{}/api/users", api_url))
            .query(&[("email", email)])
            .send().await;

        match response {
            Ok(resp) => {
                match resp.json::<CalUserRes>().await {
                    Ok(data) => {
                        println!("data: {:?}", data);
                        Some(data.user)
                    }
                    Err(err) => {
                        eprintln!("Failed to parse JSON: {:?}", err);
                        None
                    }
                }
            }
            Err(err) => {
                eprintln!("Request failed: {:?}", err);
                None
            }
        }
    }

    pub fn get_from_local_storage() -> Option<CalUser> {
        let home_dir = home_dir().unwrap_or("".into());

        let file_and_path = format!(
            "{}/{}",
            home_dir.to_str().unwrap_or("").to_string(),
            MAIN_DATA_FILENAME
        );
        println!("file_and_path: {}", file_and_path);
        let user_data_string = read_to_string(file_and_path.clone()).unwrap_or("".to_string());
        // info!("user_data: {:#?}", user_data_string.clone());
        // info!("user_data: {:#?}", file_and_path.clone());

        let cal_info = match serde_json::from_str::<UserData>(&user_data_string) {
            Ok(result) => result.cal_info,
            Err(err) => None,
        };
        // info!("user_data: {:#?}", user_data);
        return cal_info;
    }
}
