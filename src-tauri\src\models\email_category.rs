use crate::schema::email_categories::{self, child_full_domains};
use chrono::NaiveDateTime;
use diesel::{Insertable, Queryable};
use serde::{Deserialize, Serialize};
use std::clone::Clone; // This line is optional because <PERSON><PERSON> is part of the Rust prelude

#[derive(Serialize, Queryable)]
pub struct EmailCategory {
    pub id: String,
    pub category_id: String,
    pub child_full_domains: Option<String>,
    pub name: Option<String>,
    pub sender_company: Option<String>,
    pub sender_domain: Option<String>,
    pub avatar_link: Option<String>, // Include this field
    pub created_at: NaiveDateTime,
    pub tags: Option<String>, // New field for tags like "promotion", "social"
    pub latest_email_at: Option<NaiveDateTime>, // New field to store the timestamp of the latest email in the category
    pub description: Option<String>,            // New field for a brief description of the category
    pub priority: Option<i32>,                  // New field to assign a priority to the category
    pub color: Option<String>, // New field to store a hex color code for the category
    pub unread_count: Option<i32>, // New field to track the number of unread emails in the category
    pub last_accessed_at: Option<NaiveDateTime>, // New field to track when the category was last accessed
    pub is_archived: Option<bool>,               // New field to mark the category as archived
    pub custom_data: Option<String>,             // New field for storing arbitrary JSON data
    pub parent_category_id: Option<String>,      // New field to support hierarchical categories
    pub class_category_id: Option<String>, // New field to support class hierarchical categories
    pub visibility: Option<String>,        // New field to control the visibility of the category
    pub is_synced: Option<bool>, // New field to track synchronization status with external services
}

#[derive(Insertable, Clone)]
#[diesel(table_name = email_categories)]
pub struct NewEmailCategory {
    pub id: String,
    pub category_id: String,
    pub child_full_domains: String,
    pub name: String,
    pub sender_company: String,
    pub sender_domain: String,
    pub avatar_link: String, // Include this field
    pub created_at: NaiveDateTime,
    pub tags: String, // New field for tags like "promotion", "social"
    pub latest_email_at: Option<NaiveDateTime>, // New field to store the timestamp of the latest email in the category
    pub description: Option<String>,            // New field for a brief description of the category
    pub priority: Option<i32>,                  // New field to assign a priority to the category
    pub color: Option<String>, // New field to store a hex color code for the category
    pub unread_count: Option<i32>, // New field to track the number of unread emails in the category
    pub last_accessed_at: Option<NaiveDateTime>, // New field to track when the category was last accessed
    pub is_archived: Option<bool>,               // New field to mark the category as archived
    pub custom_data: Option<String>,             // New field for storing arbitrary JSON data
    pub parent_category_id: String,              // New field to support hierarchical categories
    pub class_category_id: String, // New field to support  hierarchical class categories
    pub visibility: Option<String>, // New field to control the visibility of the category
    pub is_synced: Option<bool>, // New field to track synchronization status with external services
}

impl From<NewEmailCategory> for EmailCategory {
    fn from(new_email_category: NewEmailCategory) -> Self {
        EmailCategory {
            id: new_email_category.id,
            category_id: new_email_category.category_id,
            child_full_domains: Some(new_email_category.child_full_domains),
            name: Some(new_email_category.name),
            sender_company: Some(new_email_category.sender_company),
            sender_domain: Some(new_email_category.sender_domain),
            avatar_link: Some(new_email_category.avatar_link),
            created_at: new_email_category.created_at,
            tags: Some(new_email_category.tags),
            latest_email_at: new_email_category.latest_email_at,
            description: new_email_category.description,
            priority: new_email_category.priority,
            color: new_email_category.color,
            unread_count: new_email_category.unread_count,
            last_accessed_at: new_email_category.last_accessed_at,
            is_archived: new_email_category.is_archived,
            custom_data: new_email_category.custom_data,
            parent_category_id: Some(new_email_category.parent_category_id),
            class_category_id: Some(new_email_category.class_category_id),
            visibility: new_email_category.visibility,
            is_synced: new_email_category.is_synced,
        }
    }
}

/*
New Fields Added:

    1.	description: A brief description of the category for additional context.
    2.	priority: Assigns a priority level to the category, useful for sorting or displaying.
    3.	color: Stores a hex color code, which can be used to visually distinguish categories.
    4.	unread_count: Tracks the number of unread emails in the category.
    5.	last_accessed_at: Records when the category was last accessed.
    6.	is_archived: Indicates whether the category is archived.
    7.	custom_data: Allows for storing arbitrary JSON data, enabling flexibility for custom metadata or settings.
    8.	parent_category_id: Supports hierarchical categories by linking to a parent category.
    9.	visibility: Controls the visibility of the category, such as “public,” “private,” etc.
    10.	is_synced: Tracks synchronization status with external services, useful if the category is linked to an external email provider or system.

*/
