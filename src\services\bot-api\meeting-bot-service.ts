import { CreateMeetingRecording, MeetingsRecordings, MeetingsRecordingsDetails } from "../../models/meetings-modal";
import { useCurrentUserStore } from "../../stores/currentUser";
import CalApi from "../cal-api/cal-api-service";


async function startMeetingBot(meeting: CreateMeetingRecording) {
  try {
    const response = await CalApi.post<MeetingsRecordings>(`api/meetings/run`, meeting);
    return response.data as MeetingsRecordings;
  } catch (error) {
    console.error("ERR => ", error);
    return null
  }
}

async function getMeetings() {
  try {
    const session = useCurrentUserStore()
    //  console.log("Session => ", session.calInfo?.id);
    if (session.calInfo?.id) {
      const response = await CalApi.get<MeetingsRecordings[]>(`api/meetings?userId=${session.calInfo?.id}`);
      return response.data as MeetingsRecordings[];
    }
    throw new Error("No user found");
  } catch (error) {
    console.error("ERR => ", error);
    return null
  }
}

async function getMeetingDetails(id: number) {
  try {
    const response = await CalApi.get<MeetingsRecordingsDetails>(`api/meetings/${id}/details`);
    return response.data as MeetingsRecordingsDetails;
  } catch (error) {
    console.error("ERR => ", error);
    return null
  }
}


export const MeetingBotService = {
  startMeetingBot,
  getMeetings,
  getMeetingDetails
};
