<template>
    <div class="w-full h-auto">
      <div
        class="border-2 border-primary-600 rounded-md px-2 py-1 flex justify-between items-center text-primary-900 cursor-pointer"
        @click="open = !open"
      >
        <div>{{ props.label }}</div>
        <div>
          <ChevronDownIcon :class="{ 'size-4 transition-all duration-300': true, 'rotate-180': open }" />
        </div>
      </div>
      <div
        class="w-full overflow-hidden bg-primary-500 transition-all duration-300"
        :class="{ 'h-0 max-h-0': !open, 'h-auto': open }"
      >
        <slot></slot>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ChevronDownIcon, ChevronUpIcon } from "@heroicons/vue/24/outline";
  import { ref } from "vue";
  
  const open = ref<boolean>(false);
  
  const props = defineProps({
    label: String,
  });
  </script>
  
  <style scoped></style>