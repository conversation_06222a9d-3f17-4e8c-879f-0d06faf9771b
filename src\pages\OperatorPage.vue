<template>
  <!-- Main container for the application layout with enhanced styling and organization -->
  <div class="app-background relative flex flex-col h-full p-6 space-y-4 overflow-auto">
    <!-- Header Section -->
    <header class="flex justify-between items-center p-4 bg-white shadow-md rounded-lg">
      <h1 class="text-2xl font-bold text-gray-800">Operator Dashboard</h1>

      <div
        v-if="phoneContextOpen"
        class="relative flex flex-col bg-primary-400 size-full p-4 drop-shadow-md rounded-md overflow-y-auto custom-scrollbar"
      >
        <button
          @click="phoneContextOpen = false"
          class="flex justify-start items-center gap-2 text-sm text-primary-900 hover:text-secondary-700"
        >
          <ArrowLeftIcon class="size-4" />
          <p>Email Context</p>
        </button>
        <h1 class="text-xl font-semibold text-dark-400 mt-2">Email Context</h1>
        <div>
          <phoneContext :data="phoneContextTest" />
        </div>
      </div>
      <button class="bg-blue-500 text-white py-2 px-6 rounded-lg font-semibold shadow-md">
        Operator Availability: Online
      </button>
    </header>

    <!-- Incoming Call and Client Details Sections -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <section class="incoming-call-panel p-4 bg-white border border-gray-200 rounded-lg shadow-lg">
        <h2 class="text-xl font-semibold mb-4 text-gray-700">Incoming Call</h2>

        <div class="callers-list">
          <h1 class="text-lg font-bold mb-2">Call Logs</h1>
          <ul>
            <!-- Show only the first few callers (e.g., first 5) -->
            <li
              v-for="caller in callers"
              :key="caller.id"
              class="caller-item flex justify-between items-center py-2 border-b border-gray-200"
            >
              <div class="caller-details">
                <span class="caller-name font-medium text-gray-800">{{ caller.name }}</span>
                <span class="caller-number text-sm text-gray-600">{{ caller.phone_number }}</span>
              </div>
              <div class="caller-time text-sm text-gray-500">
                {{ new Date(caller.call_time).toLocaleTimeString() }}
              </div>
            </li>
          </ul>
        </div>
      </section>

      <!-- Incoming Call Notification Panel -->
      <!-- <section class="incoming-call-panel p-4 bg-white border border-gray-200 rounded-lg shadow-lg">
        <h2 class="text-xl font-semibold mb-4 text-gray-700">Incoming Call</h2>
        <div class="caller-info mb-4">
          <p class="text-gray-600"><strong>Caller Name:</strong> John Doe</p>
          <p class="text-gray-600"><strong>Phone Number:</strong> ****** 567 890</p>
          <p class="text-gray-600"><strong>Call Purpose:</strong> Scheduling an Appointment</p>
        </div>
        <div class="flex items-center gap-4"> -->
      <!-- Call control buttons with intuitive icons and hover effects -->
      <!-- <button class="action-btn bg-green-500 hover:bg-green-600">Answer</button>
          <button class="action-btn bg-yellow-500 hover:bg-yellow-600">Hold</button>
          <button class="action-btn bg-red-500 hover:bg-red-600">End Call</button>
        </div>
        <div class="call-timer mt-4 text-sm text-gray-500">Call Duration: 00:00</div>
      </section> -->

      <!-- Client Details Panel -->
      <section class="client-details-panel p-4 bg-white border border-gray-200 rounded-lg shadow-lg">
        <h2 class="text-xl font-semibold mb-4 text-gray-700">Client Details</h2>
        <p class="text-gray-600"><strong>Client Name:</strong> {{ phoneContextData.fullName }}</p>
        <p class="text-gray-600"><strong>Contact:</strong> {{ phoneContextData.contactPhone }}</p>
        <p class="text-gray-600"><strong>Preferred Schedule:</strong> {{ phoneContextData.workHours }}</p>
        <div class="appointment-history mt-4">
          <h3 class="font-semibold text-gray-700">Appointment History</h3>
          <ul class="list-disc list-inside text-sm text-gray-600">
            <li>Last Appointment: 2023-09-10</li>
            <li>Missed Appointment: 2023-08-05</li>
          </ul>
        </div>
      </section>
    </div>

    <!-- Appointment Scheduling Section -->
    <!-- <section class="appointment-scheduling-panel p-4 bg-white border border-gray-200 rounded-lg shadow-lg"> -->
    <!-- <h2 class="text-xl font-semibold mb-4 text-gray-700">Schedule Appointment</h2> -->
    <!-- Form fields with structured spacing and clear labels -->
    <!-- <div class="grid grid-cols-1 sm:grid-cols-2 gap-4"> -->
    <!-- <div> -->
    <!-- <label class="text-sm font-semibold text-gray-600">Select Date</label> -->
    <!-- <input type="date" class="input-field" /> -->
    <!-- </div> -->
    <!-- <div> -->
    <!-- <label class="text-sm font-semibold text-gray-600">Select Time</label> -->
    <!-- <input type="time" class="input-field" /> -->
    <!-- </div> -->
    <!-- <div class="col-span-2"> -->
    <!-- <label class="text-sm font-semibold text-gray-600">Purpose of Appointment</label> -->
    <!-- <input type="text" placeholder="Enter purpose" class="input-field" /> -->
    <!-- </div> -->
    <!-- </div> -->
    <!-- Confirmation button with distinct styling -->
    <!-- <button class="bg-blue-500 text-white py-2 px-6 rounded-lg font-semibold mt-4 shadow-md"> -->
    <!-- Confirm Appointment -->
    <!-- </button> -->
    <!-- </section> -->

    <!-- Call Notes & Summary Section -->
    <!-- <section class="call-notes-panel p-4 bg-white border border-gray-200 rounded-lg shadow-lg">
      <h2 class="text-xl font-semibold mb-4 text-gray-700">Call Notes & Summary</h2>
      <textarea placeholder="Enter notes here..." class="input-field h-24 resize-none"></textarea>
      <button class="bg-indigo-500 text-white py-2 px-6 rounded-lg font-semibold mt-4 shadow-md">Save Notes</button>
    </section> -->

    <!-- Action History and Business Context Section -->
    <section class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Action History & Reminders -->
      <div class="action-history-panel p-4 bg-white border border-gray-200 rounded-lg shadow-lg">
        <h2 class="text-xl font-semibold mb-4 text-gray-700">Action History & Reminders</h2>
        <ul class="list-disc list-inside text-sm text-gray-600">
          <li>Upcoming Appointment: Jane - 2023-09-20 at 3:00 PM</li>
          <li>Follow-up needed: Call with Mike - Missed on 2023-09-18</li>
        </ul>
      </div>

      <!-- Business Context Panel for LLM Integration -->
      <div class="business-context-panel p-4 bg-white border border-gray-200 rounded-lg shadow-lg">
        <h2 class="text-xl font-semibold mb-4 text-gray-700">Business Context</h2>
        <label class="text-sm font-semibold text-gray-600">Business Hours</label>
        <input type="text" placeholder="e.g., Mon-Fri 9am - 6pm" class="input-field mb-2" />

        <label class="text-sm font-semibold text-gray-600">Main Operations</label>
        <input type="text" placeholder="e.g., Customer support, Product inquiries" class="input-field mb-2" />

        <!-- <label class="text-sm font-semibold text-gray-600">General Business Info</label>
        <textarea placeholder="Enter general information..." class="input-field h-24 resize-none"></textarea> -->
        <div class="flex justify-center mt-4">
          <button @click="openModal" class="bg-purple-500 text-white py-2 px-6 rounded-lg font-semibold shadow-md">
            View Business Info
          </button>
        </div>
      </div>
      <!-- Button to show modal -->

      <!-- Modal Component -->
      <div v-if="isModalVisible" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div class="bg-white p-6 rounded-lg shadow-lg w-[90vw] h-[70vh]">
          <div class="grid grid-cols-3 py-1 flex flex-col gap-2 max-h-full overflow-y-auto">
            <div v-for="([key,value],index) in Object.entries(phoneContext!)" class="flex justify-between items-center">
              <div
                v-if="!['tone_preference', 'key_points'].includes(key)"
                class="flex justify-between items-end w-full"
              >
                <div class="flex flex-col grow">
                  <div class="text-xs text-primary-900">{{ getNameFromAttr(key) }}</div>
                  <div class="text-sm font-semibold" v-if="!keysOnEdit.includes(key)">{{ getValue(value) }}</div>
                  <div v-else>
                    <input
                      type="text"
                      class="h-6 bg-secondary-200 border-none rounded-md p-1 drop-shadow-sm w-full"
                      :value="value"
                      @input="(e:any)=>updateEmailContext(key,value,e.target.value)"
                    />
                  </div>
                </div>
                <div class="flex items-end justify-center h-full">
                  <div v-if="!keysOnEdit.includes(key)">
                    <button
                      @click="() => keysOnEdit.push(key)"
                      class="hover:bg-secondary-200 size-6 flex justify-center items-center rounded-md transition-colors duration-200"
                    >
                      <PencilIcon class="size-4" />
                    </button>
                  </div>
                  <div class="flex gap-0.5" v-else>
                    <button
                      @click="() => removeEditKey(key)"
                      class="hover:bg-green-100 text-green-700 size-6 flex justify-center items-center rounded-md transition-colors duration-200"
                    >
                      <CheckIcon class="size-4" />
                    </button>
                    <button
                      @click="
                  () => {
                    updatePhoneContext(key, value, getValue(props.data![key]));
                    removeEditKey(key);
                  }
                "
                      class="hover:bg-red-100 text-red-700 size-6 flex justify-center items-center rounded-md transition-colors duration-200"
                    >
                      <XMarkIcon class="size-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="flex justify-end">
            <button class="bg-gray-300 text-gray-800 px-4 py-2 rounded hover:bg-gray-400" @click="closeModal">
              Close
            </button>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { useCurrentUserStore } from "../stores/currentUser";
import { PhoneCallContext } from "../models/phone-context";
import { CheckIcon, PencilIcon, XMarkIcon } from "@heroicons/vue/24/outline";

import PhoneCallContextEditor from "../components/ui/PhoneContextInput.vue";
import axios from "axios";

const API_URL = "http://localhost:9000/api/callers/list_callers/?user_id=1";

const fetchCallersApi = async () => {
  try {
    const response = await axios.get(API_URL);
    return response.data;
  } catch (error) {
    console.error("Error fetching callers:", error);
    throw error;
  }
};
const currentUserStore = useCurrentUserStore();
// const phoneContextTest = ref();
// Reactive state for the modal visibility
const isModalVisible = ref(false);

// Methods to control the modal
function openModal() {
  isModalVisible.value = true;
}

function closeModal() {
  isModalVisible.value = false;
}
const phoneContextOpen = ref(false);
const phoneContextData = currentUserStore.currentPhoneContext;

const callers = ref([]); // Reactive variable to store caller data
const error = ref(null); // Reactive variable to store errors
let intervalId = null; // To track the interval ID
const phoneContext = ref<PhoneCallContext>(phoneContextData as PhoneCallContext);

const keysOnEdit = ref<string[]>([]);

// Reactive State for Editor Visibility
const isEditorVisible = ref(false);
function getNameFromAttr(attr: string) {
  const splitedName = attr.toLowerCase().split("_");
  splitedName[0] = splitedName[0][0].toUpperCase() + splitedName[0].substring(1);
  return splitedName.join(" ");
}

function getValue(value: any) {
  if (Array.isArray(value)) return value.join(", ");
  else return value;
}

function removeEditKey(key: string) {
  keysOnEdit.value = keysOnEdit.value.filter((k) => k != key);
}

function updatePhoneContext(key: keyof PhoneCallContext, oldValue: any, newValue: any) {
  //  console.log("new Value", newValue);
  phoneContext.value = {
    ...phoneContext.value,
    [key]: Array.isArray(oldValue) ? newValue.split(",") : newValue,
  };
}
// Methods
function toggleEditor() {
  isEditorVisible.value = true;
}

function closeEditor() {
  isEditorVisible.value = false;
}

// function updatePhoneContext(updatedContext: PhoneCallContext) {
//   phoneContext.value = { ...updatedContext };
//   //  console.log("Updated Phone Call Context:", phoneContext.value);
//   closeEditor();
// }

// Function to fetch callers from the backend
const fetchCallers = async () => {
  // const startTime = new Date();
  // const endTime = new Date(startTime.getTime() + 10 * 60 * 1000); // 10 minutes from now

  // const startISO = startTime.toISOString();
  // const endISO = endTime.toISOString();

  try {
    //  console.log("Fetching callers...");
    const response = await axios.get("http://localhost:9000/api/callers/list_callers/", {
      params: {
        user_id: 1, // Pass query parameters here
      },
      headers: {
        "Content-Type": "application/json",
      },
    });

    //  console.log("Response data:", response.data);
    callers.value = response.data; // Assign fetched data to callers
    error.value = null; // Clear previous errors
  } catch (err) {}

  // try {
  //   //  console.log(" this inside");
  //   const response = await fetch(
  //     "http://localhost:9000/api/callers/list_callers/?user_id=1"
  //   );
  //   //  console.log("response", response);
  //   if (!response.ok) {
  //     throw new Error(`Error: ${response.status} ${response.statusText}`);
  //   }

  //   const data = await response.json();
  //   callers.value = data; // Update callers data
  //   //  console.log("Callers Data:", callers.value);
  //   error.value = null; // Clear previous errors
  // } catch (err) {
  //   console.error("Error fetching callers:", err);
  //   // error.value = err.message; // Store error message
  // }
};

// Function to start the periodic fetching
const startFetchingCallers = () => {
  fetchCallers(); // Fetch initially

  // Set up a 10-minute interval
  intervalId = setInterval(() => {
    fetchCallers();
  }, 10 * 60 * 1000);
};

// Function to stop periodic fetching
const stopFetchingCallers = () => {
  if (intervalId) {
    clearInterval(intervalId);
    intervalId = null;
  }
};

// Lifecycle hooks to start/stop fetching
onMounted(() => {
  startFetchingCallers();

  //  console.log("Phone Context Data:", phoneContextData);
});

onUnmounted(() => {
  // const phoneContextdata = currentUserStore.currentPHoneContext;

  // const phoneContextTest = ref(phoneContextdata);
  stopFetchingCallers(); // Clear the interval when the component is unmounted
});

// // Fetch phone context from Rust backend and update phoneContextTest
// const updatePhoneContext = async () => {
//   try {
//     const data = await invoke("js2rs", {
//       message: "get_phone_context",
//     });
//     const phoneContextdata = JSON.parse(data as string);

// // Assign values from the returned JSON object to phoneContextTest
// phoneContextTest.full_name = phoneContextdata.full_name || "";
// phoneContextTest.job_title = phoneContextdata.job_title || "";
// phoneContextTest.organization = phoneContextdata.organization || "";
// phoneContextTest.target_audience = phoneContextdata.target_audience || "";
// phoneContextTest.communication_goal = phoneContextdata.communication_goal || "";
// phoneContextTest.call_to_action = phoneContextdata.call_to_action || "";
// phoneContextTest.tone_preference = phoneContextdata.tone_preference || "";
// phoneContextTest.language_style = phoneContextdata.language_style || "";
// phoneContextTest.key_points = phoneContextdata.key_points || [];
// phoneContextTest.known_preferences = phoneContextdata.known_preferences || [];
// phoneContextTest.business_overview = phoneContextdata.business_overview || "";
// phoneContextTest.pricing_info = phoneContextdata.pricing_info || "";
// phoneContextTest.frequently_asked_questions = phoneContextdata.frequently_asked_questions || {};
// phoneContextTest.services_offered = phoneContextdata.services_offered || [];
// phoneContextTest.work_hours_start = phoneContextdata.work_hours_start || "";
// phoneContextTest.work_hours_end = phoneContextdata.work_hours_end || "";
// phoneContextTest.availability_note = phoneContextdata.availability_note || "";
// phoneContextTest.contact_email = phoneContextdata.contact_email || "";
// phoneContextTest.contact_phone = phoneContextdata.contact_phone || "";
// phoneContextTest.location_address = phoneContextdata.location_address || "";
// phoneContextTest.urgency_level = phoneContextdata.urgency_level || "";
// phoneContextTest.greeting_message = phoneContextdata.greeting_message || "";
// phoneContextTest.follow_up_message = phoneContextdata.follow_up_message || "";
// phoneContextTest.supported_languages = phoneContextdata.supported_languages || [];
// phoneContextTest.escalation_contact = phoneContextdata.escalation_contact || "";
// phoneContextTest.sentiment_triggers = phoneContextdata.sentiment_triggers || {};
// phoneContextTest.additional_resources = phoneContextdata.additional_resources || [];
// phoneContextTest.dynamic_scripts = phoneContextdata.dynamic_scripts || {};
// phoneContextTest.adaptive_learning_enabled =
//   phoneContextdata.adaptive_learning_enabled !== undefined ? phoneContextdata.adaptive_learning_enabled : true;
// phoneContextTest.response_time_preferences = phoneContextdata.response_time_preferences || "";
// phoneContextTest.default_escalation_path = phoneContextdata.default_escalation_path || "";
// phoneContextTest.backup_agent_contact = phoneContextdata.backup_agent_contact || "";

//     //  console.log("Phone Context Updated:", phoneContextTest);
//   } catch (error) {
//     console.error("Error fetching phone context:", error);
//   }
// };

// Call updatePhoneContext when needed (e.g., on component mount)
// Template for an Operator Dashboard with features for handling incoming calls,
// managing client details, scheduling appointments, and inputting business context.

// Each section is enhanced for modern, state-of-the-art design and better usability.
// The Business Context panel allows business owners to input key details, such as
// business hours, main operations, and general info to feed into an AI model.
</script>

<style scoped>
.app-background {
}
.header {
  position: sticky;
  top: 0;
  z-index: 10;
}

.action-btn {
  color: white;
  font-weight: bold;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.input-field {
  width: 100%;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
}

.input-field:focus {
  outline: none;
  border-color: #ff5733;
  box-shadow: 0 0 5px rgba(255, 87, 51, 0.3);
}

.callers-list {
  font-family: Arial, sans-serif;
}

.caller-item {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  border-bottom: 1px solid #ccc;
}

.caller-details {
  display: flex;
  flex-direction: column;
}

.caller-name {
  font-weight: bold;
}

.caller-time {
  color: gray;
  font-size: 0.9em;
}

.input-field {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.input-field:focus {
  outline: none;
  border-color: #6b7280;
  box-shadow: 0 0 0 1px #6b7280;
}
</style>
