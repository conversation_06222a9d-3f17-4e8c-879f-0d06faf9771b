import { Email } from "../types";

export type EmailLog = {
  id: string; // unique snooze log ID
  email_id: string; // associated email ID
  snoozed_until: string; // ISO timestamp, e.g., "2025-06-15T10:00:00"
  created_at?: string | null; // optional, may be null
  resurfaced?: boolean | null; // optional, may be null
};
export type SnoozedEmail = EmailLog;

export type SnoozedEmailWithDetails = {
  snooze_log: EmailLog;
  email_details?: Email | null;
};

export type UserRoleConfig = {
  userId: string;
  summarizeDaysBack: number;
  autoCleanupEnabled: boolean;
  snoozeResurfaceEnabled: boolean;
  autoAddVipDomain: boolean;
};
