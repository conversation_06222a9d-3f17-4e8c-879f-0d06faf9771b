import { EmailContext } from "./types";

// export const createPhoneCallContext = (
//   data: Partial<PhoneCallContext>
// ): PhoneCallContext => ({
//   // User-specific context
//   fullName: data.fullName || "",
//   jobTitle: data.jobTitle || "",
//   organization: data.organization || undefined,

//   // Targeting and communication details
//   targetAudience: data.targetAudience || "",
//   communicationGoal: data.communicationGoal || "",
//   callToAction: data.callToAction || undefined,
//   tonePreference: data.tonePreference || "",
//   languageStyle: data.languageStyle || "",
//   preferredResponsePatterns: data.preferredResponsePatterns || [],
//   personalizationTriggers: data.personalizationTriggers || [],
//   keyPoints: data.keyPoints || "",

//   // Known caller preferences
//   contextualData: data.contextualData || [],

//   // Business details
//   businessOverview: data.businessOverview || undefined,
//   pricingInfo: data.pricingInfo || undefined,
//   frequentlyAskedQuestions: data.frequentlyAskedQuestions || [],
//   servicesOffered: data.servicesOffered || [],

//   // Availability and contact details
//   workHours: data.workHours || undefined,
//   availabilityNote: data.availabilityNote || undefined,
//   contactEmail: data.contactEmail || undefined,
//   contactPhone: data.contactPhone || undefined,
//   locationAddress: data.locationAddress || undefined,

//   // AI agent configuration
//   urgencyLevel: data.urgencyLevel || "",
//   greetingMessage: data.greetingMessage || "",
//   followUpMessage: data.followUpMessage || undefined,
//   dynamicScripts: data.dynamicScripts || [],
//   escalationContact: data.escalationContact || undefined,
//   supportedLanguages: data.supportedLanguages || [],
//   sentimentTriggers: data.sentimentTriggers || [],
//   additionalResources: data.additionalResources || undefined,

//   // Advanced conversational configurations
//   adaptiveLearningEnabled: data.adaptiveLearningEnabled ?? true,
//   responseTimePreferences: data.responseTimePreferences || undefined,
//   defaultEscalationPath: data.defaultEscalationPath || undefined,
//   backupAgentContact: data.backupAgentContact || undefined,

//   // Methods
//   serialize() {
//     return { ...this };
//   },
//   deserialize(input: Record<string, any>): PhoneCallContext {
//     Object.assign(this, input);
//     return this;
//   },
//   toJSON() {
//     return JSON.stringify(this);
//   },
//   clone(): PhoneCallContext {
//     return { ...this };
//   },
//   update(updates: Partial<PhoneCallContext>): PhoneCallContext {
//     return { ...this, ...updates };
//   },
// });

// Default implementation for the methods (add this in your implementation file)
export const createEmailContext = (data: Partial<EmailContext>): EmailContext => ({
    full_name: data.full_name || "",
    job_title: data.job_title || "",
    organization: data.organization || "",
    target_audience: data.target_audience || "",
    communication_goal: data.communication_goal || "",
    call_to_action: data.call_to_action || "",
    tone_preference: data.tone_preference || "",
    language_style: data.language_style || "",
    key_points: data.key_points || "",
    known_preferences: data.known_preferences || [],
    personal_sign_off: data.personal_sign_off || "",
    email_signature: data.email_signature || "",
    work_hours: data.work_hours || [],
    availability_note: data.availability_note || "",
    email_length_preference: data.email_length_preference || "",
    urgency_level: data.urgency_level || "",
    
    serialize() {
      return { ...this };
    },
    deserialize(data: Record<string, any>): EmailContext {
      return { ...this, ...data };
    },
    toJSON() {
      return JSON.stringify(this);
    },
    clone(): EmailContext {
      return { ...this };
    },
    update(data: Partial<EmailContext>): EmailContext {
      return { ...this, ...data };
    },
  });
  