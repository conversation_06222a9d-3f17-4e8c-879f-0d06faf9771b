<template>
  <div class="size-full flex justify-around flex-col overflow-hidden custom-scrollbar">
    <div class="w-72 h-20 absolute top-10 left-1/2 -translate-x-1/2 flex justify-center items-center">
      <DayTimeZone />
    </div>
    <CurvedTimeLine />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { Schedule } from "../models/schedule-model";
import { WeatherService } from "../services/weather-service";
import { Forecastday, HourWeather } from "../models/weather-models";
import CurvedTimeLine from "../components/svgs/timeline/CurvedTimeLine.vue";
import DayTimeZone from "../components/svgs/DayTimeZone/DayTimeZone.vue";
import { useBookingStore } from "../stores/bookingsStore";
import { storeToRefs } from "pinia";

interface Timelines extends Schedule {
  weather?: HourWeather;
}

const bookingStore = useBookingStore();
const { todayBookings } = storeToRefs(bookingStore);

const events = ref<Timelines[]>([]);
const now = new Date();

const weatherData = ref<Forecastday>();

const timelines = computed(() =>
  events.value.map((event) => {
    return {
      ...event,
      weather: getWeatherByTime(event.time),
    };
  })
);

const data: Schedule[] = [
  {
    title: "Meeting with the patron",
    description: "About hiring a new developer...",
    time: "8:30 AM",
  },
  {
    title: "Daily Standup",
    description: "Discuss the progress of the current sprint.",
    time: "9:00 AM",
  },
  {
    title: "Client Call",
    description: "Call with the client to discuss project requirements.",
    time: "10:15 AM",
  },
  {
    title: "Team Lunch",
    description: "Lunch break with the team.",
    time: "12:00 PM",
  },
  {
    title: "Code Review",
    description: "Review code for the new feature.",
    time: "2:00 PM",
  },
  {
    title: "Strategy Meeting",
    description: "Discuss strategies for the next quarter.",
    time: "3:30 PM",
  },
  {
    title: "One-on-One with Manager",
    description: "Weekly update with the manager.",
    time: "4:30 PM",
  },
  {
    title: "Wrap-up",
    description: "Wrap up the day's tasks and prepare for tomorrow.",
    time: "5:30 PM",
  },
  {
    title: "Exercise",
    description: "Evening run in the park.",
    time: "6:30 PM",
  },
  {
    title: "Read a Book",
    description: "Read a chapter of a book before bed.",
    time: "9:00 PM",
  },
];

watch(todayBookings.value, (value) => {
  //  console.log("Today Bookings", value);
});

function getTimelines(data: Schedule[]) {
  let newData = data.map((item) => {
    const [time, period] = item.time.split(" ");
    const [hours, minutes] = time.split(":").map(Number);
    const itemTime = new Date(now);
    itemTime.setHours(period === "AM" ? hours : hours + 12, minutes, 0);

    const color = itemTime < now ? "#e66" : "#6d6";
    return { ...item, color, itemTime };
  });

  return newData.sort((a, b) => a.itemTime.getTime() - b.itemTime.getTime());
}

function getWeatherByTime(str: string) {
  const today = new Date();
  const [time, period] = str.split(" ");
  const [hours, minutes] = time.split(":");
  let hour = parseInt(hours);
  if (period === "PM" && hour !== 12) hour += 12;
  if (period === "AM" && hour === 12) hour = 0;
  const minute = parseInt(minutes);
  today.setHours(minute > 30 ? hour + 1 : hour, 0, 0, 0);

  return weatherData.value?.hour.find((h) => {
    const date = new Date(h.time);
    return today.getHours() === date.getHours();
  });
}

onMounted(async () => {
  events.value = getTimelines(data);
  events.value = getTimelines(data);

  const pos = JSON.parse(localStorage.getItem("gps") ?? "") ?? null;
  const weather = await WeatherService.getWeather(pos ? `${pos.latitude},${pos.longitude}` : "algeria", 5);
  weatherData.value = weather?.data.forecast.forecastday[0] as Forecastday;
  //  console.log("## ", weatherData.value);

  //  console.log("Today Bookings", todayBookings.value);
});
</script>

<style scoped></style>
