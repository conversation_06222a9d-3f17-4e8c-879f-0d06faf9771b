<template>
  <div
    v-if="!isPaid && !isFreeRoute"
    class="fixed inset-0 bg-white/80 backdrop-blur-sm flex flex-col items-center justify-between z-50"
  >
    <!-- Centered message -->
    <div class="text-center max-w-md p-6 mt-32 bg-white rounded-2xl shadow-xl">
      <h2 class="text-2xl font-bold mb-2 text-red-600">Don’t lose your smart assistant.</h2>
      <p class="text-gray-800 mb-4">Subscribe now to keep automating your work.</p>

      <!-- ✅ Subscribe Button -->
      <button
        @click="goToStripe"
        class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-6 rounded-lg transition"
      >
        Subscribe Now
      </button>
    </div>

    <!-- ✅ Bottom login prompt -->
    <div class="mb-8">
      <button @click="goToLogin" class="text-indigo-600 hover:underline font-medium text-sm">
        Already paid? Log back in
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watchEffect } from "vue";
import { useRoute } from "vue-router";
import { open } from "@tauri-apps/plugin-shell";
import { invoke } from "@tauri-apps/api/core";
import { calUsersService } from "../services/user-cal-service";
import { useCurrentUserStore } from "../stores/currentUser";

const router = useRoute();
const me = ref({
  email: "",
  name: "",
  picture: "",
  unreadEmailCount: 0,
  totalEmailCount: 0,
  emailCategories: 0,
});

const loading = ref(false);

// Redirect to login
const goToLogin = async () => {
  me.value = {
    email: "",
    name: "",
    picture: "",
    unreadEmailCount: 0,
    totalEmailCount: 0,
    emailCategories: 0,
  };
  localStorage.removeItem("oway_paid");

  invoke("js2rs", {
    message: "logout",
  }).then((data: any) => {
    try {
      let me_data = JSON.parse(data);
      me.value.name = me_data.name || "";
      me.value.email = me_data.email || "";
      me.value.picture = me_data.picture || "";
    } catch (err) {
      console.error(err);
    }
    loading.value = false;
    try {
      currentUserStore.removeLocalStorageKeys();
      router.replace("/signin");
    } catch (error) {
      window.location.href = "/";
    }
  });
};

const goToStripe = async () => {
  await open("https://buy.stripe.com/28o7tFal0cM68oMdQR");
};
const route = useRoute();
const isPaid = ref(true);
const isFreeRoute = ref(false);

watchEffect(() => {
  const freePaths = ["/signin", "/password", "/pricings"];
  isFreeRoute.value = freePaths.includes(route.path);
});

onMounted(async () => {
  const email = localStorage.getItem("email");
  //  console.log("📩 Retrieved email from localStorage:", email);

  if (!email) {
    isPaid.value = false;
    //  console.log("🚫 No email found, setting isPaid to false.");
    return;
  }

  try {
    const res = await fetch(`http://data.oway.life:8080/api/check-status/?email=${email}`);
    const data = await res.json();
    //  console.log("✅ Received response from API:", data);

    isPaid.value = data.is_paid;
    //  console.log("💰 isPaid status:", isPaid.value);

    if (!data.is_paid) {
      localStorage.removeItem("oway_paid");
      //  console.log("🧹 Removed oway_paid from localStorage.");
    }
  } catch (err) {
    isPaid.value = false;
    console.error("❌ Error fetching payment status:", err);
  }
});
</script>
