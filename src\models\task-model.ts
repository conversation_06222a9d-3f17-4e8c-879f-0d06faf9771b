export interface Task {
  id: number;
  title: string;
  category: string | null;
  sub_type: string | null;
  status: string | null;
  priority: string | null;
  linked_entity: string | null;
  context_type: string | null;
  context_data: string | null;
  due_date: string | null; // Consider using Date | string if you parse dates
  assigned_to: string | null;
  requires_sync: boolean | null;
  queue_position: number | null;
  snoozed_until: string | null;
  auto_executable: boolean | null;
  execution_status: string | null;
  execution_attempts: number | null;
  last_attempted_at: string | null;
  reschedule_count: number | null;
  created_at: string | null;
}

export interface TasksStat {
  active: number,
  pending: number,
  ai_executed: number,
  escalated: number
}


export interface Draft {
  id: string;
  from: string;
  to: string;
  subject: string;
  body: string;
  cc: string | null;
  bcc: string | null;
  thread_id: string | null;
  parent_email_id: string | null;
  is_send: boolean | null;
  status: string;
  task_id: number | null;
  created_at: Date | string | null;
  updated_at: Date | string | null;
}



// Utility types for create/update operations
// type CreateTask = Omit<Task, 'id' | 'created_at'> & {
//   id?: never; // Prevent ID assignment on creation
//   created_at?: never;
// };

// type UpdateTask = Partial<Omit<Task, 'id' | 'created_at'>>;
