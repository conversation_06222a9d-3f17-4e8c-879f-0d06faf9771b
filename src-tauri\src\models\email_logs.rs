use crate::schema::email_logs;
use serde::{Deserialize, Serialize};
use diesel::{Insertable, Queryable, Identifiable};
use chrono::NaiveDateTime;

#[derive(Insertable, Queryable, Identifiable, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = email_logs)]
pub struct EmailLog {
    pub id: String,
    pub email_id: String,
    pub snoozed_until: NaiveDateTime,
    pub created_at: Option<NaiveDateTime>,
    pub resurfaced: Option<bool>,
}

#[derive(Insertable, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = email_logs)]
pub struct NewEmailLog {
    pub id: String,
    pub email_id: String,
    pub snoozed_until: NaiveDateTime,
    pub created_at: NaiveDateTime,
}

impl From<NewEmailLog> for EmailLog {
    fn from(new_log: NewEmailLog) -> Self {
        EmailLog {
            id: new_log.id,
            email_id: new_log.email_id,
            snoozed_until: new_log.snoozed_until,
            created_at: Some(new_log.created_at),
            resurfaced: Some(false),
        }
    }
}