export interface SearchQuery {
  sender?: string;
  recipient?: string;
  subject?: string;
  body?: string;
}

export function parseSearchQuery(query: string): SearchQuery {
  // If query is empty, return empty object
  if (!query.trim()) {
    return {};
  }

  // If query doesn't contain any @ symbols, apply search to all fields
  if (!query.includes('@')) {
    const trimmedQuery = query.trim();
    return {
      sender: trimmedQuery,
      recipient: trimmedQuery,
      subject: trimmedQuery,
      body: trimmedQuery,
    };
  }

  const result: SearchQuery = {};
  let currentField: keyof SearchQuery | null = null;
  let currentValue: string[] = [];
  let isOrCondition = false;

  // Split the query into tokens
  const tokens = query.split(' ').filter(token => token.length > 0);

  for (let i = 0; i < tokens.length; i++) {
    const token = tokens[i];

    // Handle operators
    if (token.startsWith('@')) {
      const operator = token.toLowerCase();

      // Handle logical operators
      if (operator === '@and') {
        // Save current value if we have a field
        if (currentField && currentValue.length > 0) {
          result[currentField] = currentValue.join(' ');
          currentValue = [];
        }
        continue;
      }

      if (operator === '@or') {
        isOrCondition = true;
        continue;
      }

      // Handle field operators
      if (currentField && currentValue.length > 0) {
        result[currentField] = currentValue.join(' ');
        currentValue = [];
      }

      switch (operator) {
        case '@sender':
          currentField = 'sender';
          break;
        case '@recipient':
          currentField = 'recipient';
          break;
        case '@subject':
          currentField = 'subject';
          break;
        case '@body':
          currentField = 'body';
          break;
        default:
          // Invalid operator, treat as part of value
          if (currentField) {
            currentValue.push(token);
          }
      }
    } else {
      // Add to current value if we have a field
      if (currentField) {
        currentValue.push(token);
      }
    }
  }

  // Save the last value if we have one
  if (currentField && currentValue.length > 0) {
    result[currentField] = currentValue.join(' ');
  }

  return result;
}

// Test cases to verify functionality
const testCases = [
  {
    input: '@body search this text @and @recipient <EMAIL>',
    expected: {
      body: 'search this text',
      recipient: '<EMAIL>'
    }
  },
  {
    input: '@sender alice @or @recipient bob',
    expected: {
      sender: 'alice',
      recipient: 'bob'
    }
  },
  {
    input: 'simple search without operators',
    expected: {
      sender: 'simple search without operators',
      recipient: 'simple search without operators',
      subject: 'simple search without operators',
      body: 'simple search without operators'
    }
  },
  {
    input: '@subject important meeting @and @body discuss project',
    expected: {
      subject: 'important meeting',
      body: 'discuss project'
    }
  },
  {
    input: '',
    expected: {}
  }
];

// Run test cases
testCases.forEach((testCase, index) => {
  const result = parseSearchQuery(testCase.input);
  //  console.log(`Test ${index + 1}:`);
  //  console.log('Input:', testCase.input);
  //  console.log('Result:', result);
  //  console.log('Expected:', testCase.expected);
  //  console.log('Passed:', JSON.stringify(result) === JSON.stringify(testCase.expected));
  //  console.log('---');
});
