export interface Schedule {
    title: string,
    description?: string,
    time: string,
    color?: string
  }
  
  
  export interface CalAvailability {
    id?: number,
    eventTypeId?: number,
    date?: string,
    days?: number[],
    startTime: string,
    endTime: string,
    scheduleId?: number,
  }
  
  export const DAYS = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]