<template>
  <div class="relative">
    <!-- Time Input with Chevron Controls -->
    <div
      class="flex items-center bg-primary border border-primary-400 rounded-lg shadow-sm hover:border-secondary-400 focus-within:border-secondary-600 focus-within:ring-2 focus-within:ring-secondary-200 transition-all duration-200"
    >
      <!-- Decrease Time Button -->
      <button
        @click="decreaseTime"
        class="p-2 text-secondary-500 hover:text-secondary-700 hover:bg-primary-200 rounded-l-lg transition-colors duration-150"
        type="button"
      >
        <ChevronLeftIcon class="w-4 h-4" />
      </button>

      <!-- Time Input Field -->
      <input
        ref="timeInput"
        v-model="displayTime"
        @input="handleTimeInput"
        @blur="validateAndFormatTime"
        @keydown.enter="validateAndFormatTime"
        @keydown.up.prevent="increaseTime"
        @keydown.down.prevent="decreaseTime"
        class="flex-1 px-3 py-2 text-center text-sm font-medium text-base-500 bg-transparent border-none outline-none min-w-0"
        placeholder="12:00 PM"
        type="text"
      />

      <!-- Increase Time Button -->
      <button
        @click="increaseTime"
        class="p-2 text-secondary-500 hover:text-secondary-700 hover:bg-primary-200 rounded-r-lg transition-colors duration-150"
        type="button"
      >
        <ChevronRightIcon class="w-4 h-4" />
      </button>
    </div>

    <!-- Dropdown for Quick Selection -->
    <div
      v-if="showDropdown"
      class="absolute z-50 w-full mt-1 bg-primary border border-primary-400 rounded-lg shadow-lg max-h-60 overflow-y-auto custom-scrollbar"
    >
      <div
        v-for="time in filteredTimes"
        :key="time.value"
        @click="selectTime(time)"
        class="px-3 py-2 text-sm cursor-pointer hover:bg-secondary-100 hover:text-secondary-700 transition-colors duration-150"
        :class="{ 'bg-secondary-200 text-secondary-700': time.value === selectedTime }"
      >
        {{ time.label }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, onBeforeUnmount, ref, watch } from "vue";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/vue/24/outline";
import { generateTimeIntervals, normalizeTime, TimeInterval, timeToISOString } from "../../utils/availability-utils";

const props = defineProps({
  value: {
    type: String,
  },
});

const emit = defineEmits<{
  (e: "select", value: string): void;
}>();

const times = ref<TimeInterval[]>(generateTimeIntervals());
const selectedTime = ref(props.value && props.value != "none" ? timeToISOString(props.value) : undefined);
const displayTime = ref("");
const showDropdown = ref(false);
const timeInput = ref<HTMLInputElement>();

// Convert ISO time to display format
const formatTimeForDisplay = (isoTime: string): string => {
  if (!isoTime) return "";
  try {
    const date = new Date(isoTime);
    return date.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
  } catch {
    return "";
  }
};

// Find the closest time interval
const findClosestTimeInterval = (targetTime: string): TimeInterval | null => {
  if (!targetTime) return null;

  const target = new Date(targetTime);
  let closest = times.value[0];
  let minDiff = Math.abs(new Date(closest.value).getTime() - target.getTime());

  for (const time of times.value) {
    const diff = Math.abs(new Date(time.value).getTime() - target.getTime());
    if (diff < minDiff) {
      minDiff = diff;
      closest = time;
    }
  }

  return closest;
};

// Filtered times for dropdown
const filteredTimes = computed(() => {
  if (!displayTime.value) return times.value;

  const searchTerm = displayTime.value.toLowerCase();
  return times.value.filter((time) => time.label.toLowerCase().includes(searchTerm));
});

// Initialize display time
const initializeDisplayTime = () => {
  if (selectedTime.value) {
    displayTime.value = formatTimeForDisplay(selectedTime.value);
  } else {
    displayTime.value = "12:00 PM";
  }
};

// Handle time input changes
const handleTimeInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  displayTime.value = target.value;

  // Show dropdown when typing
  if (displayTime.value.length > 0) {
    showDropdown.value = true;
  }
};

// Validate and format time input
const validateAndFormatTime = () => {
  showDropdown.value = false;

  if (!displayTime.value.trim()) {
    displayTime.value = "12:00 PM";
    return;
  }

  try {
    const normalized = normalizeTime(displayTime.value);
    displayTime.value = normalized;

    // Convert to ISO and emit
    const isoTime = timeToISOString(normalized);
    selectedTime.value = isoTime;
    emit("select", isoTime);
  } catch {
    // If normalization fails, try to find closest match
    const closest = times.value.find((time) => time.label.toLowerCase().includes(displayTime.value.toLowerCase()));

    if (closest) {
      displayTime.value = closest.label;
      selectedTime.value = closest.value;
      emit("select", closest.value);
    } else {
      displayTime.value = "12:00 PM";
    }
  }
};

// Select time from dropdown
const selectTime = (time: TimeInterval) => {
  displayTime.value = time.label;
  selectedTime.value = time.value;
  showDropdown.value = false;
  emit("select", time.value);
};

// Increase time by 15 minutes
const increaseTime = () => {
  const currentIndex = times.value.findIndex((time) => time.value === selectedTime.value);
  const nextIndex = currentIndex < times.value.length - 1 ? currentIndex + 1 : 0;
  const nextTime = times.value[nextIndex];

  displayTime.value = nextTime.label;
  selectedTime.value = nextTime.value;
  emit("select", nextTime.value);
};

// Decrease time by 15 minutes
const decreaseTime = () => {
  const currentIndex = times.value.findIndex((time) => time.value === selectedTime.value);
  const prevIndex = currentIndex > 0 ? currentIndex - 1 : times.value.length - 1;
  const prevTime = times.value[prevIndex];

  displayTime.value = prevTime.label;
  selectedTime.value = prevTime.value;
  emit("select", prevTime.value);
};

// Watch for prop changes
watch(
  () => props.value,
  (newValue) => {
    if (newValue && newValue !== "none") {
      selectedTime.value = timeToISOString(newValue);
      displayTime.value = formatTimeForDisplay(selectedTime.value);
    }
  },
  { immediate: true }
);

// Close dropdown when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement;
  if (!target.closest(".relative")) {
    showDropdown.value = false;
  }
};

onMounted(() => {
  initializeDisplayTime();
  document.addEventListener("click", handleClickOutside);
});

// Cleanup event listener
onBeforeUnmount(() => {
  document.removeEventListener("click", handleClickOutside);
});
</script>

<style scoped></style>
