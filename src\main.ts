import { createApp } from "vue";
import { createRouter, createWeb<PERSON><PERSON>ory } from "vue-router";
import { createPinia } from "pinia";

import "primeicons/primeicons.css";
import "./style.css";
import "flag-icons/css/flag-icons.min.css";

import App from "./App.vue";
import GoogleSignInPlugin from "vue3-google-login";

// Define lazy-loaded route components
const routes = [
  {
    path: "/",
    component: () => import("./pages/CheckUserPage.vue"),
    children: [
      { path: "", redirect: "emails" },
      { path: "emails", component: () => import("./pages/EmailPage.vue") },
      { path: "calendar", component: () => import("./pages/CalenderPage.vue") },
      { path: "operator", component: () => import("./pages/OperatorPage.vue") },
      { path: "send", component: () => import("./pages/SendPage.vue") },
      { path: "userprofile", component: () => import("./pages/UserProfilePage.vue") },
      {
        path: "auth",
        component: () => import("./pages/auth/AuthPage.vue"),
        children: [
          { path: "", redirect: "signin" },
          { path: "signin", component: () => import("./pages/auth/SignInPage.vue") },
          { path: "password", component: () => import("./pages/auth/PasswordPage.vue") },
          { path: "onboarding", component: () => import("./pages/auth/OnBoarding.vue") },
        ],
      },
    ],
  },
  { path: "/settings", component: () => import("./pages/settings/SettingsPage.vue") },
  { path: "/pricings", component: () => import("./pages/PricingPage.vue") },
  { path: "/subscribe", component: () => import("./pages/SubscriptionGate.vue") },
  { path: "/assistants", component: () => import("./pages/AssistantsPage.vue") },
  { path: "/assistants/create", component: () => import("./pages/CreateAssistantPage.vue") },
  { path: "/assistants/:id", component: () => import("./pages/EditAssistantPage.vue") },
  { path: "/:pathMatch(.*)*", redirect: "/" },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

const pinia = createPinia();

createApp(App)
  .use(router)
  .use(pinia)
  .use(GoogleSignInPlugin, {
    clientId: "834830517259-urgd6md3v79ccksbq1ki1ar1cb8laa1r.apps.googleusercontent.com",
  })
  .mount("#app");
