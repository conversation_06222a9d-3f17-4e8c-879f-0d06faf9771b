use crate::schema::meetings;
use chrono::NaiveDateTime;
use diesel::{Insertable, Queryable};
use serde::{Deserialize, Serialize};

#[derive(Serialize, Queryable, Insertable, Deserialize)]
#[diesel(table_name = meetings)]
pub struct Meeting {
    pub id: String,                         // Unique identifier for the meeting
    pub title: String,                      // Title of the meeting
    pub description: Option<String>,        // Detailed description of the meeting
    pub location: Option<String>,           // Location of the meeting (can be physical or virtual)
    pub start_time: NaiveDateTime,          // The start time of the meeting
    pub end_time: NaiveDateTime,            // The end time of the meeting
    pub organizer: String,                  // The organizer of the meeting
    pub attendees: Option<String>,          // List of attendees (as JSON string or comma-separated)
    pub calendar_id: String, // Identifier for the calendar to which the meeting belongs
    pub is_all_day: Option<bool>, // Flag indicating if the meeting lasts all day
    pub recurrence_pattern: Option<String>, // Recurrence pattern for the meeting (e.g., "weekly")
    pub is_recurring: Option<bool>, // Flag indicating whether the meeting is part of a recurring series
    pub created_at: Option<NaiveDateTime>, // Timestamp when the meeting was created
    pub updated_at: Option<NaiveDateTime>, // Timestamp when the meeting was last updated
    pub reminder_settings: Option<String>, // Reminder settings for the meeting (as JSON string)
    pub time_zone: String,          // Time zone of the meeting
    pub visibility: Option<String>, // Visibility of the meeting (e.g., "public", "private")
    pub color_code: Option<String>, // Custom color code for the meeting
    pub status: Option<String>, // Current status of the meeting (e.g., "confirmed", "tentative")
    pub event_url: Option<String>, // URL to view or join the meeting (e.g., video conference link)
    pub importance: Option<i32>, // Importance level of the meeting
    pub category: Option<String>, // Category of the meeting (e.g., "work", "personal")
    pub attachments: Option<String>, // Attachments related to the meeting (as JSON string)
    pub is_cancelled: Option<bool>, // Flag indicating if the meeting has been cancelled
    pub recurrence_id: Option<String>, // ID to link recurring meetings together
    pub sentiment: Option<String>, // Sentiment analysis of the meeting description or related content
    pub actionable_items: Option<String>, // List of actionable items identified in the meeting (as JSON string)
    pub language: Option<String>,         // Language detected in the meeting content
    pub translated_title: Option<String>, // Translated title of the meeting (if applicable)
    pub event_priority: Option<i32>,      // Priority score for the meeting
    pub custom_fields: Option<String>,    // Any additional custom fields (as JSON string)
    pub related_email_id: Option<String>, // Identifier linking the meeting to a related email, if any
}
