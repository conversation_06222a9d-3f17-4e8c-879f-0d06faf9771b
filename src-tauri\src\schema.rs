// @generated automatically by Diesel CLI.

diesel::table! {
    assistants (id) {
        id -> Text,
        name -> Text,
        description -> Text,
        created_at -> Timestamp,
    }
}

diesel::table! {
    drafts (id) {
        id -> Text,
        from -> Text,
        to -> Text,
        subject -> Text,
        body -> Text,
        cc -> Nullable<Text>,
        bcc -> Nullable<Text>,
        thread_id -> Nullable<Text>,
        parent_email_id -> Nullable<Text>,
        is_send -> Nullable<Bool>,
        status -> Text,
        task_id -> Nullable<Integer>,
        created_at -> Nullable<Timestamp>,
        updated_at -> Nullable<Timestamp>,
    }
}

diesel::table! {
    email_categories (id) {
        id -> Text,
        category_id -> Text,
        child_full_domains -> Nullable<Text>,
        name -> Nullable<Text>,
        sender_company -> Nullable<Text>,
        sender_domain -> Nullable<Text>,
        avatar_link -> Nullable<Text>,
        created_at -> Timestamp,
        tags -> Nullable<Text>,
        latest_email_at -> Nullable<Timestamp>,
        description -> Nullable<Text>,
        priority -> Nullable<Integer>,
        color -> Nullable<Text>,
        unread_count -> Nullable<Integer>,
        last_accessed_at -> Nullable<Timestamp>,
        is_archived -> Nullable<Bool>,
        custom_data -> Nullable<Text>,
        parent_category_id -> Nullable<Text>,
        class_category_id -> Nullable<Text>,
        visibility -> Nullable<Text>,
        is_synced -> Nullable<Bool>,
    }
}

diesel::table! {
    email_logs (id) {
        id -> Text,
        email_id -> Text,
        snoozed_until -> Timestamp,
        created_at -> Nullable<Timestamp>,
        resurfaced -> Nullable<Bool>,
    }
}

diesel::table! {
    emails (id) {
        id -> Text,
        subject -> Text,
        snippet -> Text,
        from -> Text,
        to -> Text,
        cc -> Nullable<Text>,
        bcc -> Nullable<Text>,
        date -> Timestamp,
        category -> Nullable<Text>,
        labels -> Nullable<Text>,
        attachments -> Nullable<Text>,
        attachment_types -> Nullable<Text>,
        total_attachment_size -> Nullable<Integer>,
        metadata_headers -> Nullable<Text>,
        email_body_url -> Nullable<Text>,
        is_read -> Nullable<Bool>,
        thread_id -> Nullable<Text>,
        thread_summary -> Nullable<Text>,
        priority -> Nullable<Text>,
        urgency_score -> Nullable<Integer>,
        sentiment -> Nullable<Text>,
        actionable_items -> Nullable<Text>,
        language -> Nullable<Text>,
        phishing_risk -> Nullable<Text>,
        sender_reputation -> Nullable<Text>,
        full_domain -> Nullable<Text>,
        main_domain -> Nullable<Text>,
        storage_location -> Nullable<Text>,
        is_flagged -> Nullable<Bool>,
        process_flag -> Nullable<Bool>,
        email_type -> Nullable<Text>,
        is_thread_root -> Nullable<Bool>,
        received_as -> Nullable<Text>,
        parent_email_id -> Nullable<Text>,
        read_receipt_url -> Nullable<Text>,
        reply_suggestion -> Nullable<Text>,
        follow_up_date -> Nullable<Timestamp>,
        meeting_proposed -> Nullable<Bool>,
        meeting_link -> Nullable<Text>,
        is_delegated -> Nullable<Bool>,
        task_status -> Nullable<Text>,
        auto_reply_sent -> Nullable<Bool>,
        flagged_keywords -> Nullable<Text>,
        attachments_downloaded -> Nullable<Bool>,
        shared_with -> Nullable<Text>,
        analytics_score -> Nullable<Integer>,
        response_time -> Nullable<Integer>,
        ai_generated -> Nullable<Bool>,
        is_send -> Nullable<Bool>,
        source_app -> Nullable<Text>,
        created_at -> Nullable<Timestamp>,
        updated_at -> Nullable<Timestamp>,
    }
}

diesel::table! {
    important_domains (id) {
        id -> Text,
        domain -> Text,
        category -> Text,
        name -> Nullable<Text>,
        avatar_link -> Nullable<Text>,
        created_at -> Nullable<Timestamp>,
    }
}

diesel::table! {
    meetings (id) {
        id -> Text,
        title -> Text,
        description -> Nullable<Text>,
        location -> Nullable<Text>,
        start_time -> Timestamp,
        end_time -> Timestamp,
        organizer -> Text,
        attendees -> Nullable<Text>,
        calendar_id -> Text,
        is_all_day -> Nullable<Bool>,
        recurrence_pattern -> Nullable<Text>,
        is_recurring -> Nullable<Bool>,
        created_at -> Nullable<Timestamp>,
        updated_at -> Nullable<Timestamp>,
        reminder_settings -> Nullable<Text>,
        time_zone -> Text,
        visibility -> Nullable<Text>,
        color_code -> Nullable<Text>,
        status -> Nullable<Text>,
        event_url -> Nullable<Text>,
        importance -> Nullable<Integer>,
        category -> Nullable<Text>,
        attachments -> Nullable<Text>,
        is_cancelled -> Nullable<Bool>,
        recurrence_id -> Nullable<Text>,
        sentiment -> Nullable<Text>,
        actionable_items -> Nullable<Text>,
        language -> Nullable<Text>,
        translated_title -> Nullable<Text>,
        event_priority -> Nullable<Integer>,
        custom_fields -> Nullable<Text>,
        related_email_id -> Nullable<Text>,
    }
}

diesel::table! {
    messages (id) {
        id -> Text,
        session_id -> Text,
        content -> Text,
        role -> Text,
        finish_reason -> Text,
        prompt_tokens -> Integer,
        completion_tokens -> Integer,
        created_at -> Timestamp,
    }
}

diesel::table! {
    sessions (id) {
        id -> Text,
        assistant_id -> Text,
        name -> Nullable<Text>,
        created_at -> Timestamp,
    }
}

diesel::table! {
    subtasks (id) {
        id -> Integer,
        parent_task_id -> Integer,
        title -> Text,
        status -> Nullable<Text>,
        priority -> Nullable<Text>,
        assigned_to -> Nullable<Text>,
        due_date -> Nullable<Text>,
        requires_sync -> Nullable<Bool>,
        queue_position -> Nullable<Integer>,
        snoozed_until -> Nullable<Text>,
        auto_executable -> Nullable<Bool>,
        execution_status -> Nullable<Text>,
        execution_attempts -> Nullable<Integer>,
        last_attempted_at -> Nullable<Text>,
        reschedule_count -> Nullable<Integer>,
        created_at -> Nullable<Text>,
    }
}

diesel::table! {
    tasks (id) {
        id -> Integer,
        title -> Text,
        category -> Nullable<Text>,
        sub_type -> Nullable<Text>,
        status -> Nullable<Text>,
        priority -> Nullable<Text>,
        linked_entity -> Nullable<Text>,
        context_type -> Nullable<Text>,
        context_data -> Nullable<Text>,
        due_date -> Nullable<Text>,
        assigned_to -> Nullable<Text>,
        requires_sync -> Nullable<Bool>,
        queue_position -> Nullable<Integer>,
        snoozed_until -> Nullable<Text>,
        auto_executable -> Nullable<Bool>,
        execution_status -> Nullable<Text>,
        execution_attempts -> Nullable<Integer>,
        last_attempted_at -> Nullable<Text>,
        reschedule_count -> Nullable<Integer>,
        created_at -> Nullable<Text>,
    }
}

diesel::joinable!(drafts -> tasks (task_id));
diesel::joinable!(subtasks -> tasks (parent_task_id));

diesel::allow_tables_to_appear_in_same_query!(
    assistants,
    drafts,
    email_categories,
    email_logs,
    emails,
    important_domains,
    meetings,
    messages,
    sessions,
    subtasks,
    tasks,
);
