import { ref, computed } from 'vue'
import { getActiveSnoozedEmails, type EmailLog } from '../commands/snooze'

// Global reactive state for snoozed emails
const snoozedEmails = ref<EmailLog[]>([])
const isLoading = ref(false)

// Computed property to get snoozed thread IDs
const snoozedThreadIds = computed(() => {
  return new Set(snoozedEmails.value.map(email => email.email_id))
})

/**
 * Composable for managing snoozed emails globally
 */
export function useSnoozedEmails() {
  
  /**
   * Load all active snoozed emails from the backend
   */
  const loadSnoozedEmails = async () => {
    if (isLoading.value) return
    
    isLoading.value = true
    try {
      const emails = await getActiveSnoozedEmails()
      snoozedEmails.value = emails
      console.log('Loaded snoozed emails:', emails.length)
    } catch (error) {
      console.error('Failed to load snoozed emails:', error)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Check if a thread is currently snoozed
   */
  const isThreadSnoozed = (threadId: string): boolean => {
    return snoozedThreadIds.value.has(threadId)
  }

  /**
   * Filter out snoozed emails from an email list
   */
  const filterSnoozedEmails = <T extends { thread_id?: string }>(emails: T[]): T[] => {
    return emails.filter(email => {
      return !email.thread_id || !isThreadSnoozed(email.thread_id)
    })
  }

  /**
   * Filter out snoozed email groups from an email group list
   */
  const filterSnoozedEmailGroups = <T extends { thread_id?: string }>(emailGroups: T[][]): T[][] => {
    return emailGroups.filter(emailGroup => {
      const threadId = emailGroup[0]?.thread_id
      return !threadId || !isThreadSnoozed(threadId)
    })
  }

  /**
   * Refresh snoozed emails (call after snooze operations)
   */
  const refreshSnoozedEmails = async () => {
    await loadSnoozedEmails()
  }

  return {
    // State
    snoozedEmails: computed(() => snoozedEmails.value),
    snoozedThreadIds,
    isLoading: computed(() => isLoading.value),
    
    // Methods
    loadSnoozedEmails,
    refreshSnoozedEmails,
    isThreadSnoozed,
    filterSnoozedEmails,
    filterSnoozedEmailGroups
  }
}
