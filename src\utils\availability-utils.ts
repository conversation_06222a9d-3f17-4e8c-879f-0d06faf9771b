import { CalAvailability } from "../models/availability-model"
import { CalSchedule } from "../models/schedule-model"


export interface AvailGroups {
  days: { [key: number]: CalAvailability[] },
  dates: {
    [key: string]: CalAvailability[]
  }
}

export function groupAvailability(disps: CalAvailability[]) {
  const frs: AvailGroups = disps.reduce((acc: any, obj: CalAvailability) => {
    if (!acc["days"]) {
      acc.days = {}
    }
    if (!acc["dates"]) {
      acc.dates = {}
    }
    if (obj.days) {
      obj.days.map((day) => {
        //  console.log("DAY INDEX", day);
        if (!acc["days"][day]) acc["days"][day] = []
        acc["days"][day].push(obj);
      })
    } else if (obj.date) {
      if (!acc["days"][obj.date]) acc["days"][obj.date] = {}
      acc["dates"][obj.date].push(obj);
    }
    return acc;
  }, {})
  //  console.log("ACC", frs);
  return frs
}


// export interface TimeInterval {
//   start: string;
//   end: string;
// }

export interface TimeInterval {
  value: string,
  label: string
}

export function generateTimeIntervals(
  startTime: string = '12:00 AM',
  endTime: string = '24:00 PM',
  intervalMinutes: number = 15
): TimeInterval[] {
  const intervals: TimeInterval[] = [];
  const date = new Date(2023, 0, 1, 0, 0);

  const parseTime = (timeString: string): { hours: number, minutes: number } => {
    const match = timeString.match(/(\d+):(\d+)\s*(AM|PM)/i);
    if (!match) throw new Error('Invalid time format');

    let hours = parseInt(match[1]);
    const minutes = parseInt(match[2]);
    const period = match[3].toUpperCase();

    if (period === 'PM' && hours !== 12) hours += 12;
    if (period === 'AM' && hours === 12) hours = 0;

    return { hours, minutes };
  };

  const formatTime = (hours: number, minutes: number): string => {
    const ampm = hours < 12 ? 'AM' : 'PM';
    const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
    return `${displayHours < 10 ? '0' : ''}${displayHours}:${minutes.toString().padStart(2, '0')} ${ampm}`;
  };

  const { hours: startHours, minutes: startMinutes } = parseTime(startTime);
  const { hours: endHours, minutes: endMinutes } = parseTime(endTime);

  date.setHours(startHours, startMinutes, 0, 0);
  date.setFullYear(2000, 0, 1);
  const endDate = new Date(date);
  endDate.setFullYear(2000, 0, 1);
  endDate.setHours(endHours, endMinutes, 0, 0);
  while (date <= endDate) {
    intervals.push({
      label: formatTime(date.getHours(), date.getMinutes()),
      value: date.toISOString(),
    });
    date.setMinutes(date.getMinutes() + intervalMinutes);
  }

  return intervals;
}

export function timeToISOString(timeString: string): string {
  const isoDateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?Z?$/;

  if (isoDateRegex.test(timeString)) {
    return timeString;
  }
  const date = new Date();
  const [hours, minutes, seconds] = timeString.split(':').map(Number);
  date.setHours(hours, minutes, seconds, 0);
  date.setFullYear(2000, 0, 1);
  return date.toISOString();
}

export function normalizeTime(inputTime: string): string {
  //  console.log("INPUT TIME", inputTime);
  // Remove any whitespace
  inputTime = inputTime.trim();

  // Regular expressions for different time formats
  const formats = [
    // HH:MM:SS (24-hour with seconds)
    /^(\d{2}):(\d{2}):(\d{2})$/,

    // HH:MM AM/PM
    /^(\d{1,2}):(\d{2})\s*([AaPp][Mm])$/,

    // HH AM/PM
    /^(\d{1,2})\s*([AaPp][Mm])$/,

    // HHMM
    /^(\d{3,4})$/,

    // H:MM AM/PM
    /^(\d{1,2}):(\d{2})\s*([AaPp][Mm])$/,

    // HH:MM (24-hour)
    /^(\d{1,2}):(\d{2})$/
  ];

  let hours = 0;
  let minutes = 0;
  let period = 'AM';

  // Try each format
  for (const regex of formats) {
    const match = inputTime.match(regex);

    if (match) {
      switch (match.length) {
        case 4: // HH:MM:SS or HH:MM AM/PM or H:MM AM/PM
          if (match[3].length === 2 && !isNaN(Number(match[3]))) {
            // HH:MM:SS format (24-hour)
            hours = parseInt(match[1]);
            minutes = parseInt(match[2]);
          } else {
            // HH:MM AM/PM or H:MM AM/PM
            hours = parseInt(match[1]);
            minutes = parseInt(match[2]);
            period = match[3].toUpperCase();
          }
          break;

        case 3: // HH AM/PM
          hours = parseInt(match[1]);
          minutes = 0;
          period = match[2].toUpperCase();
          break;

        case 2: // HHMM or HH:MM (24-hour)
          if (match[1].length === 3) {
            // HHMM format
            hours = Math.floor(parseInt(match[1]) / 100);
            minutes = parseInt(match[1]) % 100;
          } else {
            // HH:MM (24-hour)
            hours = parseInt(match[1]);
            minutes = parseInt(match[2]);
          }
          break;
      }
      break;
    }
  }

  // Normalize hours and period
  if (period === 'PM' && hours !== 12) {
    hours += 12;
  } else if (period === 'AM' && hours === 12) {
    hours = 0;
  }

  // Ensure hours and minutes are within valid ranges
  hours = Math.min(Math.max(hours, 0), 23);
  minutes = Math.min(Math.max(minutes, 0), 59);

  // Determine AM/PM
  period = hours < 12 ? 'AM' : 'PM';

  // Convert to 12-hour format
  const displayHours = hours % 12 || 12;

  // Format the result
  const result = `${displayHours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')} ${period}`;
  //  console.log("RESULT", result);
  return result
}


export function manageDayAvailability(
  schedule: CalSchedule,
  dayIndex: number,
  active: boolean
): CalSchedule {
  // Validate day index
  if (dayIndex < 0 || dayIndex > 6) {
    throw new Error('Day index must be between 0 and 6');
  }

  // Create a deep copy of the schedule to avoid direct mutation
  const updatedSchedule: CalSchedule = {
    ...schedule,
    availability: schedule.availability.slice()
  };

  if (active) {
    // Check if a default availability for this day already exists
    const existingDayAvailability = updatedSchedule.availability.find(
      avail => avail.days && avail.days.includes(dayIndex)
    );

    if (!existingDayAvailability) {
      // Create a new default availability for the day
      updatedSchedule.availability.push({
        id: 0, // Generate a temporary unique ID
        scheduleId: schedule.id,
        startTime: '09:00', // Default start time
        endTime: '17:00',   // Default end time
        days: [dayIndex]
      });
    }
  } else {
    // Modify or remove availabilities based on the day
    updatedSchedule.availability = updatedSchedule.availability.reduce<CalAvailability[]>(
      (acc, availability) => {
        if (!availability.days) return acc;

        // Remove the specific day from days array
        const updatedDays = availability.days.filter(day => day !== dayIndex);

        if (updatedDays.length > 0) {
          // If days remain, update the availability
          acc.push({
            ...availability,
            days: updatedDays
          });
        }

        return acc;
      },
      []
    );
  }

  return updatedSchedule;
}


export function addDayToAvailability(
  schedule: CalSchedule,
  dayIndex: number,
  startTime: string,
  endTime: string
): CalSchedule {
  // Validate input
  if (dayIndex < 0 || dayIndex > 6) {
    throw new Error('Day index must be between 0 and 6');
  }

  // Create a deep copy of the schedule
  const updatedSchedule: CalSchedule = {
    ...schedule,
    availability: schedule.availability.slice()
  };

  // Try to find an existing availability with matching start and end times
  const existingAvailability = updatedSchedule.availability.find(
    avail => avail.startTime === startTime && avail.endTime === endTime
  );

  if (existingAvailability) {
    // If existing availability found, add the day if not already present
    if (!existingAvailability.days) {
      existingAvailability.days = [dayIndex];
    } else if (!existingAvailability.days.includes(dayIndex)) {
      existingAvailability.days.push(dayIndex);
    }
  } else {
    // Create a new availability if no matching one exists
    updatedSchedule.availability.push({
      id: 0, // Generate a temporary unique ID
      scheduleId: schedule.id,
      startTime: startTime,
      endTime: endTime,
      days: [dayIndex]
    });
  }

  return updatedSchedule;
}


export function removeDayFromAvailability(
  schedule: CalSchedule,
  dayIndex: number,
  startTime: string,
  endTime: string
): CalSchedule {
  // Validate input
  if (dayIndex < 0 || dayIndex > 6) {
    throw new Error('Day index must be between 0 and 6');
  }

  // Create a deep copy of the schedule
  const updatedSchedule: CalSchedule = {
    ...schedule,
    availability: schedule.availability.slice()
  };

  // Modify availabilities
  updatedSchedule.availability = updatedSchedule.availability.reduce<CalAvailability[]>(
    (acc, availability) => {
      // Check if the availability matches the start and end times
      if (availability.startTime === startTime && availability.endTime === endTime) {
        // If days exists and contains the specified day
        if (availability.days) {
          // Remove the specified day
          const updatedDays = availability.days.filter(day => day !== dayIndex);

          // Only keep the availability if it has remaining days
          if (updatedDays.length > 0) {
            acc.push({
              ...availability,
              days: updatedDays
            });
          }
        }
      } else {
        // Keep availabilities that don't match the time
        acc.push(availability);
      }

      return acc;
    },
    []
  );

  return updatedSchedule;
}

export function moveDayAvailability(
  schedule: CalSchedule,
  dayIndex: number,
  oldStartTime: string,
  oldEndTime: string,
  newStartTime: string,
  newEndTime: string
): CalSchedule {
  // Validate input
  if (dayIndex < 0 || dayIndex > 6) {
    throw new Error('Day index must be between 0 and 6');
  }

  // Create a deep copy of the schedule
  const updatedSchedule: CalSchedule = {
    ...schedule,
    availability: schedule.availability.slice()
  };

  // Find the availability with the old time slot
  const oldAvailabilityIndex = updatedSchedule.availability.findIndex(
    avail =>
      avail.startTime === oldStartTime &&
      avail.endTime === oldEndTime &&
      avail.days?.includes(dayIndex)
  );

  // If no matching old availability found, return original schedule
  if (oldAvailabilityIndex === -1) {
    return schedule;
  }

  // Find or create a new availability for the new time slot
  let newAvailabilityIndex = updatedSchedule.availability.findIndex(
    avail =>
      avail.startTime === newStartTime &&
      avail.endTime === newEndTime
  );

  // Modify the availabilities
  if (newAvailabilityIndex === -1) {
    // Create a new availability if no matching time slot exists
    newAvailabilityIndex = updatedSchedule.availability.length;
    updatedSchedule.availability.push({
      id: 0, // Generate a temporary unique ID
      scheduleId: schedule.id,
      startTime: newStartTime,
      endTime: newEndTime,
      days: []
    });
  }

  // Remove the day from the old availability
  const oldAvailability = updatedSchedule.availability[oldAvailabilityIndex];
  oldAvailability.days = oldAvailability.days?.filter(day => day !== dayIndex);

  // Add the day to the new availability
  const newAvailability = updatedSchedule.availability[newAvailabilityIndex];
  if (!newAvailability.days) {
    newAvailability.days = [dayIndex];
  } else if (!newAvailability.days.includes(dayIndex)) {
    newAvailability.days.push(dayIndex);
  }

  // Remove the old availability if it has no days left
  updatedSchedule.availability = updatedSchedule.availability.filter(
    avail => !avail.days || avail.days.length > 0
  );

  return updatedSchedule;
}


// export interface AvailabilityChanges {
//   toUpdate: CalAvailability[];
//   toCreate: CalAvailability[];
//   toRemove: CalAvailability[];
// }

// export function compareScheduleAvailabilities(
//   originalSchedule: CalSchedule,
//   modifiedSchedule: CalSchedule
// ): AvailabilityChanges {
//   const result: AvailabilityChanges = {
//     toUpdate: [],
//     toCreate: [],
//     toRemove: []
//   };

//   // Create a map of original availabilities for easier comparison
//   const originalAvailabilityMap = new Map(
//     originalSchedule.availability.map(avail => [avail.id, avail])
//   );

//   // Check modified schedule availabilities
//   modifiedSchedule.availability.forEach(modifiedAvail => {
//     //  console.log("ModifiedAvail ###", modifiedAvail);
//     if (modifiedAvail.id === 0) {
//       // New availability to create
//       result.toCreate.push({
//         ...modifiedAvail,
//         id: 0, // Temporary ID generation
//         scheduleId: originalSchedule.id
//       });
//     } else {
//       const originalAvail = originalAvailabilityMap.get(modifiedAvail.id);
//       //  console.log("Origin Avail=>", originalAvail);
//       if (originalAvail) {
//         // Check if availability has changed
//         const hasChanged =
//           originalAvail.startTime !== modifiedAvail.startTime ||
//           originalAvail.endTime !== modifiedAvail.endTime ||
//           !areSetsEqual(
//             new Set(originalAvail.days || []),
//             new Set(modifiedAvail.days || [])
//           );
//         //  console.log("HAS CHANGED", hasChanged);
//         if (hasChanged) {
//           if (modifiedAvail.days && modifiedAvail.days?.length > 0)
//             result.toUpdate.push(modifiedAvail);
//           else
//             result.toRemove.push(modifiedAvail);

//         }
//       }
//     }
//   });

//   return result;
// }

// // Helper function to compare sets
// function areSetsEqual(setA: Set<number>, setB: Set<number>): boolean {
//   if (setA.size !== setB.size) return false;

//   for (const item of setA) {
//     if (!setB.has(item)) return false;
//   }

//   return true;
// }


export interface AvailabilityChanges {
  toUpdate: CalAvailability[];
  toCreate: CalAvailability[];
  toRemove: CalAvailability[];
}

export function compareScheduleAvailabilities(
  originalSchedule: CalSchedule,
  modifiedSchedule: CalSchedule
): AvailabilityChanges {
  const result: AvailabilityChanges = {
    toUpdate: [],
    toCreate: [],
    toRemove: []
  };

  // Create a map of modified availabilities for easier comparison
  const modifiedAvailabilityMap = new Map(
    modifiedSchedule.availability.map(avail => [avail.id, avail])
  );

  // Check original schedule availabilities
  originalSchedule.availability.forEach(originalAvail => {
    const modifiedAvail = modifiedAvailabilityMap.get(originalAvail.id);

    if (!modifiedAvail) {
      // Original availability is not in modified schedule, mark for removal
      result.toRemove.push(originalAvail);
    } else if (
      originalAvail.startTime !== modifiedAvail.startTime ||
      originalAvail.endTime !== modifiedAvail.endTime ||
      !areSetsEqual(
        new Set(originalAvail.days || []),
        new Set(modifiedAvail.days || [])
      )
    ) {
      // Availability exists but has changed
      result.toUpdate.push(modifiedAvail);
    }
  });

  // Check for new availabilities in modified schedule
  modifiedSchedule.availability.forEach(modifiedAvail => {
    if (modifiedAvail.id === 0) {
      // New availability to create
      result.toCreate.push({
        ...modifiedAvail,
        id: 0, // Temporary ID generation
        scheduleId: originalSchedule.id
      });
    }
  });

  return result;
}

// Helper function to compare sets
function areSetsEqual(setA: Set<number>, setB: Set<number>): boolean {
  if (setA.size !== setB.size) return false;

  for (const item of setA) {
    if (!setB.has(item)) return false;
  }

  return true;
}
