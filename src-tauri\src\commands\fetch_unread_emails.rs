use crate::google_api_functions::check_and_refresh_token::check_and_refresh_token;
use crate::google_api_functions::check_and_refresh_token_unread::check_and_refresh_token_unread;
use crate::google_api_functions::fetch_emails::fetch_all_unread_emails;
use crate::google_api_functions::fetch_emails::fetch_fresh_emails;
use crate::google_api_functions::fetch_emails::process_emails_with_flag;

use crate::google_api_functions::refresh_access_token::refresh_access_token;
use crate::models::app_data::AppData;
use crate::models::user_data::User;
use crate::models::user_data::UserData;
use chrono::{ DateTime, Utc };
use reqwest::Client;
use serde_json::json;
use std::sync::Arc;
use tokio::sync::RwLock;

use std::time::{ Duration, Instant };
use tauri::{ Manager, RunEvent, WindowEvent };
use tokio::sync::Mutex;
use tokio::sync::MutexGuard;
use tracing::{ error, info, warn };

use std::env;
use std::error::Error as StdError;

use google_cloud_auth::credentials::CredentialsFile;
use google_cloud_auth::project::Config;
use google_cloud_auth::token::DefaultTokenSourceProvider;
use google_cloud_token::TokenSourceProvider;

use crate::google_api_functions::fetch_emails::load_progress;
use google_cloud_pubsub::client::{ Client as PubSubClient, ClientConfig };
use google_cloud_pubsub::subscription::ReceiveConfig;
use serde_json::Value;
use tokio_util::sync::CancellationToken;

use futures::future::BoxFuture;
use tokio::time::sleep; // To handle async closures

#[tauri::command(async)]
pub async fn fetch_unread_emails(
    app_data: tauri::State<'_, Arc<RwLock<AppData>>>,
    cancel_token: tauri::State<'_, Arc<CancellationToken>>
) -> Result<String, String> {
    println!("Fetching unread emails...");
    std::env::set_var(
        "GOOGLE_APPLICATION_CREDENTIALS",
        "/Users/<USER>/Desktop/AI_Secartery/oway/src-tauri/src/commands/client_secret.json"
    );
    let path = std::env::var("GOOGLE_APPLICATION_CREDENTIALS").unwrap();
    // Set the path to your credentials file
    let credentials_path =
        "/Users/<USER>/Desktop/AI_Secartery/oway/src-tauri/src/commands/client_secret.json";

    if std::path::Path::new(&path).exists() {
        println!("Credentials file found at: {}", path);
    } else {
        eprintln!("Credentials file not found at: {}", path);
    } // Lock app_data to access UserData
    let cancel_token_one: Arc<CancellationToken> = cancel_token.inner().clone();
    check_and_refresh_token(&app_data).await.map_err(|e|
        format!("Failed to refresh token: {}", e)
    )?;
    let mut exprie_in_read: Option<Duration> = None;
    let mut issued_at_read: Option<DateTime<Utc>> = None;
    let mut refresh_token: Option<String> = None;
    let mut access_token: Option<String> = None;

    {
        let app_data_arc = app_data.read().await; // Acquire read lock
        let user_data = app_data_arc.user_data.read().await;

        exprie_in_read = user_data.expire_in.clone(); // Clone if `Duration` is needed later
        issued_at_read = user_data.issued_at.clone();
        refresh_token = user_data.refresh_token.as_ref().map(|t| t.secret().clone());
        access_token = user_data.access_token.as_ref().map(|t| t.secret().clone());
    } // Lock is released here automatically

    // // // Step 3: Fetch unread emails with the access token
    let app_data_clone = app_data.inner().clone(); // Extract and clone the Arc

    let progress_data = load_progress().unwrap_or_else(|_| json!({}));

    let saved_next_page_token = progress_data
        .get("unread_resume")
        .and_then(|v| v.get("nextPageToken"))
        .and_then(|v| v.as_str());

    let saved_all_emails_fetched = progress_data
        .get("allEmailsFetched")
        .and_then(|v| v.as_bool())
        .unwrap_or(false);

    let saved_in_progress = progress_data
        .get("unread_resume")
        .and_then(|v| v.get("inProgress"))
        .and_then(|v| v.as_bool())
        .unwrap_or(false);

    println!("All emails fetched: {}", saved_all_emails_fetched);
    println!("Unread fetch in progress: {}", saved_in_progress);

    if saved_in_progress {
        if
            let (Some(refresh_token), Some(access_token)) = (
                refresh_token.as_deref(),
                access_token.as_deref(),
            )
        {
            if
                let Err(e) = fetch_all_unread_emails(
                    app_data_clone,
                    &refresh_token, // Already a &str
                    &access_token, // Already a &str
                    "https://www.googleapis.com/gmail/v1/users/me/messages?q=is:unread".to_string(),
                    cancel_token_one
                ).await
            {
                eprintln!("Error fetching emails: {}", e);
            } else {
                println!("Emails fetched successfully.");
            }
        } else {
            eprintln!("Missing refresh token or access token. Cannot fetch emails.");
        }
    }
    // Step 4: Clone app_data for spawning notification pool task
    let app_data_arc = app_data.inner().clone(); // Clone the Arc so it can be moved into a background task

    println!("Subscription set to: projects/secura-424307/subscriptions/OwayDev-sub");

    let client = Client::new();

    // Replace these values with your actual access token and subscription details
    // let access_token = "your_access_token";
    let subscription_name = "projects/secura-424307/subscriptions/secura-424307";
    let subscription_url = format!(
        "https://pubsub.googleapis.com/v1/{}",
        subscription_name.clone()
    );
    let app_data_clone_one = app_data.inner().clone(); // Extract and clone the Arc
    let cancel_token = cancel_token.inner().clone(); // Wrap cancel_token in Arc

    println!("User data accessed for refreshing token.");
    // tokio::spawn(async move {
    // Assuming cancel_token_arc is already an Arc<CancellationToken>
    let cancel_token_arc: Arc<CancellationToken> = cancel_token.clone();
    // process_pubsub_loop(app_data_clone, subscription_url, cancel_token_arc).await;

    // match get_latest_history_id(&access_token.clone().unwrap().clone()).await {
    //     Ok(history_id) => println!("📌 Latest history ID: {}", history_id),
    //     Err(e) => eprintln!("❌ Error: {}", e),
    // }

    loop {
        println!("Cloning receive configuration for new loop iteration.");

        // Call `check_and_refresh_token_unread`
        if let Err(e) = check_and_refresh_token_unread(app_data.inner().clone()).await {
            eprintln!("Failed to refresh token: {}", e);
        } else {
            println!("Token refreshed successfully.");
        }

        // 1. Get current time in milliseconds
        let now_ms = Utc::now().timestamp_millis();

        // 2. Load user's watch expiration from your storage (DB or memory)
        let mut needs_renewal = false;
        {
            let app_data_arc = app_data.inner().read().await;
            let user_data = app_data_arc.user_data.read().await;

            if let Some(expiration) = user_data.watch_expiration {
                if now_ms >= expiration - 86_400_000 {
                    println!("⏳ Gmail watch is about to expire — renewing...");
                    needs_renewal = true;
                }
            } else {
                println!("⚠️ No expiration found — setting up watch for the first time.");
                needs_renewal = true;
            }
        }
        // 3. If renewal needed, re-watch
        if needs_renewal {
            if let Some(access_token) = access_token.clone() {
                match
                    initiate_gmail_watch(
                        &access_token,
                        "projects/secura-424307/topics/OwayDev"
                    ).await
                {
                    Ok((new_history_id, new_expiration)) => {
                        let mut app_data_arc = app_data.inner().write().await;
                        let mut user_data = app_data_arc.user_data.write().await;

                        // Update expiration and optionally historyId
                        user_data.watch_expiration = Some(new_expiration);
                        user_data.history_id = Some(new_history_id.clone());

                        println!("✅ Gmail watch renewed successfully.");
                    }
                    Err(e) => {
                        eprintln!("❌ Failed to renew Gmail watch: {}", e);
                        // Optionally: continue or retry later
                    }
                }
            } else {
                eprintln!("❌ Cannot renew watch — missing access token.");
            }
        }
        // let user_data_lock = user_data_arc.lock().await;
        // // Retrieve access token
        // let access_token = match &user_data_lock.access_token {
        //     Some(token) => token.secret().clone(),
        //     None => return Ok(Default::default()),
        // };

        // Retrieve refresh token
        // let refresh_token = match &user_data_lock.refresh_token {
        //     Some(token) => token.secret().clone(),
        //     None => return Ok(Default::default()), // Adjust error message as needed
        // };

        let mut exprie_in_read: Option<Duration> = None;
        let mut issued_at_read: Option<DateTime<Utc>> = None;
        let mut refresh_token: Option<String> = None;
        let mut access_token: Option<String> = None;

        {
            let app_data_arc = app_data.inner().read().await; // Acquire read lock
            let user_data = app_data_arc.user_data.read().await;

            exprie_in_read = user_data.expire_in.clone(); // Clone if `Duration` is needed later
            issued_at_read = user_data.issued_at.clone();
            refresh_token = user_data.refresh_token.as_ref().map(|t| t.secret().clone());
            access_token = user_data.access_token.as_ref().map(|t| t.secret().clone());
        } // Lock is released here automatically

        println!("Access token and refresh token accessed from user data.");

        // Fetch messages from Pub/Sub
        match fetch_pubsub_messages(&access_token.clone().unwrap(), &subscription_url).await {
            Ok(pubsub_messages) => {
                println!("Received Pub/Sub messages: {:?}", pubsub_messages);

                if let Some(received_messages) = pubsub_messages["receivedMessages"].as_array() {
                    for message in received_messages {
                        let ack_id = message["ackId"].as_str().unwrap_or_default();

                        if let Some(encoded_data) = message["message"]["data"].as_str() {
                            if let Ok(decoded_bytes) = base64::decode(encoded_data) {
                                if
                                    let Ok(decoded_json) =
                                        serde_json::from_slice::<serde_json::Value>(&decoded_bytes)
                                {
                                    if let Some(history_id) = decoded_json["historyId"].as_i64() {
                                        println!(
                                            "Processing message with ackId: {} and historyId: {}, emailAddress: {}",
                                            ack_id,
                                            history_id,
                                            decoded_json["emailAddress"]
                                        );

                                        let guessed_start_id = 3104876;
                                        // If history_id is not empty, fetch from that point onward
                                        // Fetch Gmail emails using the historyId
                                        let gmail_url =
                                            format!("https://www.googleapis.com/gmail/v1/users/me/history?startHistoryId={}&historyTypes=messageAdded", history_id);
                                        let cancel_token_two = cancel_token_arc.clone();
                                        if
                                            let (Some(refresh_token), Some(access_token)) = (
                                                refresh_token.as_deref(),
                                                access_token.as_deref(),
                                            )
                                        {
                                            if
                                                let Ok(_) = fetch_fresh_emails(
                                                    app_data.inner().clone(),
                                                    &refresh_token,
                                                    &access_token,
                                                    gmail_url,
                                                    cancel_token_two
                                                ).await
                                            {
                                                println!("Emails fetched successfully.");

                                                // Acknowledge only if fetch was successful
                                                if
                                                    let Err(e) = acknowledge_pubsub_messages(
                                                        &access_token,
                                                        &subscription_url,
                                                        &[ack_id.to_string()]
                                                    ).await
                                                {
                                                    eprintln!("Error acknowledging message: {}", e);
                                                } else {
                                                    println!("Message acknowledged.");
                                                }
                                                process_emails_with_flag(app_data.clone()).await;
                                            } else {
                                                eprintln!("Error fetching emails.");
                                            }
                                        } else {
                                            eprintln!(
                                                "Missing refresh token or access token. Cannot fetch emails."
                                            );
                                        }
                                    } else {
                                        println!("History ID not found in decoded message for ackId: {}", ack_id);
                                    }
                                } else {
                                    println!("Failed to parse decoded message JSON for ackId: {}", ack_id);
                                }
                                //acknowledge_pubsub_messages(&access_token.clone().unwrap(), &subscription_url, &[ack_id.to_string()]);
                            } else {
                                println!("Failed to decode base64 data for ackId: {}", ack_id);
                            } // End of for loop
                        } else {
                            println!("No new messages in Pub/Sub.");
                        }
                    }
                }
            }
            Err(e) => {
                eprintln!("Error receiving Pub/Sub messages: {:?}", e);
            }
        }
        // initiate_gmail_watch(&access_token.clone().unwrap(), "projects/secura-424307/topics/OwayDev").await;
        // Acknowledge the message after processing
        println!("Sleeping for 2 seconds before next iteration...");
        sleep(Duration::from_secs(2)).await;
    }
    // });

    //     loop {
    //         let receive_config_clone = receive_config.clone();
    //         println!("Cloning receive configuration for new loop iteration.");

    //         // Lock before accessing user_data
    //         let user_data_arc = app_data_arc.lock().await.user_data.clone();
    //         println!("User data accessed for refreshing token.");

    //         // Call `check_and_refresh_token_unread`
    //         if let Err(e) = check_and_refresh_token_unread(user_data_arc.clone()).await {
    //             eprintln!("Failed to refresh token: {}", e);
    //         } else {
    //             println!("Token refreshed successfully.");
    //         }

    //         let user_data_lock = user_data_arc.lock().await;
    //         let access_token = user_data_lock.access_token.clone();
    //         let refresh_token = user_data_lock.refresh_token.clone();

    //         println!("Access token and refresh token accessed from user data.");

    //         // Call the receive method with the callback function and cancellation token
    //         match subscription.receive(
    //             move |message, cancel| {  // Use `move` to capture ownership
    //                 println!("Received Pub/Sub message with ID: {}", message.ack_id());

    //                 let progress_data = load_progress().unwrap_or_else(|_| json!({}));
    //                 let history_id = progress_data.get("historyId").and_then(|v| v.as_str());
    //                 let history_id_str = history_id.map_or_else(|| {
    //                     eprintln!("No history ID found in progress data.");
    //                     "No history ID found".to_string()
    //                 }, |id| id.to_string());

    //                 let access_token_cloned = access_token.clone();
    //                 let refresh_token_cloned = refresh_token.clone();

    //                 async move {
    //                     println!("Processing Pub/Sub message...");

    //                     let url = format!(
    //                         "https://www.googleapis.com/gmail/v1/users/me/history?startHistoryId={}",
    //                         history_id_str.clone()
    //                     );
    //                     println!("Fetching emails from URL: {}", url);

    //                     if let Some(access_token_copy) = access_token_cloned.as_ref().map(|t| t.secret().clone()) {
    //                         if let Some(refresh_token_copy) = refresh_token_cloned.as_ref().map(|t| t.secret().clone()) {
    //                             if let Err(e) = fetch_emails(&refresh_token_copy, &access_token_copy, url).await {
    //                                 eprintln!("Error fetching emails: {}", e);
    //                             } else {
    //                                 println!("Emails fetched successfully.");
    //                             }
    //                         } else {
    //                             eprintln!("Refresh token not found.");
    //                         }
    //                     } else {
    //                         eprintln!("Access token not found.");
    //                     }
    //                 }
    //             },
    //             cancel_token.clone(),
    //             receive_config_clone,
    //         ).await {
    //             Ok(_) => println!("Message received and processed successfully."),
    //             Err(e) => eprintln!("Error receiving message: {:?}", e),
    //         };

    //         println!("Sleeping for 2 seconds before next iteration...");
    //         sleep(Duration::from_secs(2)).await;
    //     }
    // });
    // Step 6: Optionally start polling with backoff if necessary (commented out in your original code)
    // poll_with_backoff(&user_data).await; // Uncomment if needed

    return Ok("Fetched unread emails and started notification listener.".to_string());
}

async fn process_pubsub_loop(
    app_data_clone: Arc<RwLock<AppData>>,
    subscription_url: String,
    cancel_token_arc: Arc<CancellationToken>
) -> Result<String, String> {
    loop {
        println!("Cloning receive configuration for new loop iteration.");

        // Call `check_and_refresh_token_unread`
        if let Err(e) = check_and_refresh_token(&app_data_clone).await {
            eprintln!("Failed to refresh token: {}", e);
        } else {
            println!("Token refreshed successfully.");
        }

        let mut expire_in_read: Option<Duration> = None;
        let mut issued_at_read: Option<DateTime<Utc>> = None;
        let mut refresh_token: Option<String> = None;
        let mut access_token: Option<String> = None;

        // Acquire read lock and extract necessary data
        {
            let app_data_arc = app_data_clone.read().await;
            let user_data = app_data_arc.user_data.read().await;

            expire_in_read = user_data.expire_in.clone();
            issued_at_read = user_data.issued_at.clone();
            refresh_token = user_data.refresh_token.as_ref().map(|t| t.secret().clone());
            access_token = user_data.access_token.as_ref().map(|t| t.secret().clone());
        } // Lock is released here automatically

        println!("Access token and refresh token accessed from user data.");

        // Fetch messages from Pub/Sub
        match
            fetch_pubsub_messages(&access_token.clone().unwrap(), &subscription_url.clone()).await
        {
            Ok(pubsub_messages) => {
                println!("Received Pub/Sub messages: {:?}", pubsub_messages);

                if let Some(received_messages) = pubsub_messages["receivedMessages"].as_array() {
                    for message in received_messages {
                        let ack_id = message["ackId"].as_str().unwrap_or_default();
                        let history_id = message["message"]["attributes"]["historyId"]
                            .as_str()
                            .unwrap_or_default();
                        println!(
                            "Processing message with ackId: {} and historyId: {}",
                            ack_id,
                            history_id
                        );

                        // Fetch Gmail emails using the historyId
                        let gmail_url =
                            format!("https://www.googleapis.com/gmail/v1/users/me/history?startHistoryId={}", history_id);
                        let cancel_token_two = cancel_token_arc.clone();
                        if
                            let (Some(refresh_token), Some(access_token)) = (
                                refresh_token.as_deref(),
                                access_token.as_deref(),
                            )
                        {
                            if
                                let Err(e) = fetch_fresh_emails(
                                    app_data_clone.clone(),
                                    &refresh_token,
                                    &access_token,
                                    gmail_url,
                                    cancel_token_two
                                ).await
                            {
                                eprintln!("Error fetching emails: {}", e);
                            } else {
                                println!("Emails fetched successfully.");
                            }
                            // acknowledge_pubsub_messages(
                            //     &access_token.clone(),
                            //     &subscription_url,
                            //     &[ack_id.to_string()],
                            // );
                        } else {
                            eprintln!(
                                "Missing refresh token or access token. Cannot fetch emails."
                            );
                        }
                    }
                } else {
                    println!("No new messages in Pub/Sub.");
                }
            }
            Err(e) => {
                eprintln!("Error receiving Pub/Sub messages: {:?}", e);
            }
        }

        println!("Sleeping for 2 seconds before next iteration...");
        sleep(Duration::from_secs(200)).await;
    }
    return Ok("All emails fetched previously".to_string());
}

async fn acknowledge_pubsub_messages(
    access_token: &str,
    subscription_url: &str,
    ack_ids: &[String]
) -> Result<(), Box<dyn StdError + Send + Sync>> {
    println!("acknowledge_pubsub_messages ...");

    if ack_ids.is_empty() {
        println!("⚠️ No ackIds provided — skipping acknowledgement.");
        return Ok(());
    }

    let client = Client::new();

    let acknowledge_url = format!("{}:acknowledge", subscription_url); // same as pull, just append :acknowledge
    println!("acknowledge_url : {} ...", acknowledge_url);

    let response = client
        .post(&acknowledge_url)
        .bearer_auth(access_token)
        .json(&json!({ "ackIds": ack_ids }))
        .send().await?;

    if response.status().is_success() {
        println!("✅ Acknowledged {} messages successfully.", ack_ids.len());
        Ok(())
    } else {
        let status = response.status();
        let error_body = response.text().await.unwrap_or_default();
        eprintln!("❌ Failed to acknowledge messages. Status: {status}\n{error_body}");
        Err(
            Box::new(
                std::io::Error::new(
                    std::io::ErrorKind::Other,
                    "Failed to acknowledge Pub/Sub messages"
                )
            )
        )
    }
}

async fn fetch_pubsub_messages(
    access_token: &str,
    subscription_url: &str
) -> Result<Value, Box<dyn StdError + Send + Sync>> {
    let client = Client::new();
    let subscription_url_pull = format!("{}:pull", subscription_url); // same as pull, just append :pull
    let response = client
        .post(subscription_url_pull)
        .bearer_auth(access_token)
        .json(
            &json!({
            "returnImmediately": false,
            "maxMessages": 10
        })
        )
        .send().await?;
    let status = response.status().clone();
    let text = response.text().await?;

    println!("\n🔍 HTTP Status: {}\n", status);
    println!("📦 Raw Response Body:\n{}\n", text);

    if status.is_success() {
        let json_response: Value = serde_json::from_str(&text)?;

        if json_response.get("receivedMessages").is_none() {
            println!("⚠️ No messages received — queue might be empty.");
        } else {
            println!("✅ Messages received:");
            if let Some(msgs) = json_response.get("receivedMessages") {
                // for (i, msg) in msgs.as_array().unwrap_or(&vec![]).iter().enumerate() {
                //     println!(
                //         "  [{}] messageId: {}",
                //         i,
                //         msg["message"]["messageId"]
                //             .as_str()
                //             .unwrap_or("(no messageId)")
                //     );
                // }
            }
        }

        Ok(json_response)
    } else {
        println!("❌ Failed to fetch Pub/Sub messages. Status: {}", status);
        println!("Response Body:\n{}", text);
        Ok("".into())
    }
}
// Function to load the last saved historyId
fn load_history_id() -> Option<String> {
    match std::fs::read_to_string("history_id.txt") {
        Ok(history_id) => Some(history_id),
        Err(_) => None, // Return None if the file doesn't exist or an error occurs
    }
}

pub async fn initiate_gmail_watch(
    access_token: &str,
    topic_name: &str
) -> Result<(String, i64), Box<dyn StdError + Send + Sync>> {
    let client = Client::new();

    let response = client
        .post("https://gmail.googleapis.com/gmail/v1/users/me/watch")
        .bearer_auth(access_token)
        .json(
            &json!({
            "topicName": topic_name,
            // "labelIds": ["INBOX", "CATEGORY_PERSONAL", "CATEGORY_UPDATES", "CATEGORY_PROMOTIONS"],
            // "labelFilterAction": "include"
        })
        )
        .send().await?;

    let status = response.status();
    let body = response.text().await?;

    if !status.is_success() {
        eprintln!("❌ Failed to set Gmail watch. Status: {status}");
        eprintln!("Response: {}", body);
        return Err("Failed to initiate Gmail watch".into());
    }

    let json_body: Value = serde_json::from_str(&body)?;

    let history_id = json_body["historyId"].as_str().ok_or("Missing historyId")?.to_string();

    let expiration = json_body["expiration"].as_str().ok_or("Missing expiration")?.parse::<i64>()?;

    println!("✅ Gmail watch started. History ID: {}, Expiration: {}", history_id, expiration);

    Ok((history_id, expiration))
}

pub async fn get_latest_history_id(
    access_token: &str
) -> Result<String, Box<dyn StdError + Send + Sync>> {
    let client = Client::new();
    let url = "https://gmail.googleapis.com/gmail/v1/users/me/profile";

    let response = client.get(url).bearer_auth(access_token).send().await?;

    let status = response.status();
    let body = response.text().await?;

    if !status.is_success() {
        return Err(format!("Failed to fetch profile: {} - {}", status, body).into());
    }

    let json: Value = serde_json::from_str(&body)?;
    match json["historyId"].as_str() {
        Some(history_id) => Ok(history_id.to_string()),
        None => Err("Missing historyId in response".into()),
    }
}
