<template>
    <div
      @click="() => meetingsStore.setSelectedMeeting(meeting)"
      class="flex flex-col p-1 drop-shadow bg-secondary-200 w-full rounded gap-1 cursor-pointer hover:bg-secondary-300/80 border border-secondary-400 hover:border-secondary-500"
    >
      <div class="flex gap-1 items-center text-xs">
        <LinkIcon class="size-3" />
        <div>{{ meeting.url }}</div>
      </div>
      <div class="flex flex-col">
        <div class="text-[9px] text-secondary-700">description</div>
        <div class="text-xs line-clamp-1 truncate text-secondary-800 italic font-semibold">{{ meeting.description }}</div>
      </div>
      <div class="flex mt-1 gap-4 justify-between items-center">
        <div class="flex gap-4">
          <div class="flex gap-1 items-center text-xs">
            <CubeIcon class="size-4" />
            <div>{{ meeting.platform.toLowerCase() }}</div>
          </div>
  
          <div class="flex gap-1 items-center text-xs">
            <CalendarDaysIcon class="size-4" />
            <div>{{ dayjs(meeting.createdAt).format("DD MMM YYYY") }}</div>
          </div>
  
          <div class="flex gap-1 items-center text-xs">
            <ClockIcon class="size-4" />
            <div>{{ dayjs(meeting.createdAt).format("HH:MM A") }}</div>
          </div>
        </div>
        <div class="flex gap-4">
          <div class="flex gap-1 items-center text-xs">
            <div>Recording</div>
            <div class="mt-0.5">
              <CheckBadgeIcon
                class="size-4 text-green-700"
                v-if="meeting.recording && meeting.recordingStatus === MeetingStatus.FINISHED"
              />
              <ExclamationCircleIcon class="size-4 text-red-600" v-else />
            </div>
          </div>
          <div class="flex gap-1 items-center text-xs">
            <div>Summarization</div>
            <div class="mt-0.5">
              <CheckBadgeIcon
                class="size-4 text-green-700"
                v-if="meeting.summarization && meeting.summarizationStatus === MeetingStatus.FINISHED"
              />
              <ExclamationCircleIcon class="size-4 text-red-600" v-else />
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref } from "vue";
  import { MeetingsRecordings, MeetingStatus } from "../../models/meetings-modal";
  import {
    CalendarDaysIcon,
    CheckBadgeIcon,
    ClockIcon,
    CubeIcon,
    ExclamationCircleIcon,
    LinkIcon,
  } from "@heroicons/vue/24/outline";
  import dayjs from "dayjs";
  import { useMeetingsStore } from "../../stores/meetingsStore";
  
  const meetingsStore = useMeetingsStore();
  
  const props = defineProps({
    meeting: {
      type: Object,
      required: true,
    },
  });
  
  const meeting = ref(props.meeting as MeetingsRecordings);
  </script>
  
  <style scoped></style>