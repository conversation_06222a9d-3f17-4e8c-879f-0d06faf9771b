<template>
    <div class="flex rounded-md w-auto h-10 items-center overflow-hidden">
      <button
        v-for="item of items"
        class="h-full px-2 flex justify-center items-center transition-all duration-300 flex-1"
        @click="emit('update:selected', item.value)"
        :class="{
          'bg-secondary-600 text-white': item.value === selected,
          'bg-secondary-100 text-secondary-900': item.value !== selected,
        }"
      >
        <div>{{ item.label }}</div>
      </button>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref } from "vue";
  
  export interface ToggleGroupsItem {
    label: string;
    value: string;
  }
  
  const props = defineProps({
    items: Object,
    selected: String,
    group: String,
  });
  
  const items = ref<ToggleGroupsItem[]>(props.items as ToggleGroupsItem[]);
  
  const emit = defineEmits(["update:selected"]);
  </script>
  
  <style scoped></style>