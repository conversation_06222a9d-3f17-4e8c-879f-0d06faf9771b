<template>
  <div
    class="h-screen w-screen absolute top-0 left-0 flex flex-col bg-primary text-gray-800 p-4 rounded-2xl shadow overflow-hidden"
  >
    <!-- Header -->
    <div class="flex justify-between items-center mb-2 shrink-0">
      <h1 class="text-xl font-semibold tracking-tight">Setup Agent Context</h1>
      <button
        @click="handleJumpToWork"
        class="flex items-center gap-3 px-3 py-1.5 text-primary-800 hover:text-secondary-600 underline rounded-md transition font-semibold"
      >
        <p>Jump to Work</p>
        <ArrowRightIcon class="size-5 mt-0.5" />
      </button>
    </div>

    <!-- Scrollable Grid -->
    <div class="flex-grow min-h-0 overflow-y-auto">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-3 pr-1">
        <div v-for="([key, value], index) in Object.entries(sharedContext)" :key="index" class="flex flex-col">
          <label class="text-xs font-medium text-gray-700 mb-0.5">
            {{ getNameFromAttr(key) }}
          </label>

          <!-- View Mode -->
          <div
            v-if="!keysOnEdit.includes(key)"
            class="flex justify-between items-center bg-gray-50 border border-gray-200 rounded px-2 py-1"
          >
            <span class="text-xs text-gray-600 truncate">{{ getValue(value) }}</span>
            <button @click="() => keysOnEdit.push(key)" class="p-0.5 hover:text-blue-500 transition">
              <PencilIcon class="w-4 h-4" />
            </button>
          </div>

          <!-- Edit Mode -->
          <div v-else class="flex gap-1 mt-0.5">
            <input
              type="text"
              class="flex-1 h-8 px-2 text-xs border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
              :value="value"
              @input="(e: any) => updateContext(key, value, e.target.value)"
            />
            <button @click="() => removeEditKey(key)" class="text-green-600 hover:bg-green-100 rounded p-0.5">
              <CheckIcon class="w-4 h-4" />
            </button>
            <button @click="() => revertChange(key, value)" class="text-red-600 hover:bg-red-100 rounded p-0.5">
              <XMarkIcon class="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { invoke } from "@tauri-apps/api/core";
import { PencilIcon, CheckIcon, XMarkIcon, ArrowRightIcon } from "@heroicons/vue/24/outline";
import { useRouter } from "vue-router";
import { PhysicalSize } from "@tauri-apps/api/dpi";
import { getCurrentWindow } from "@tauri-apps/api/window";
import auth from "../../commands/auth";

const router = useRouter();
const keysOnEdit = ref<string[]>([]);

const sharedContext = ref({
  full_name: "Maaz Yolo",
  job_title: "Founder & CEO",
  organization: "Tech Innovators Inc.",
  target_audience: "Enterprise Software Clients",
  communication_goal: "Project Update and Timeline Review",
  call_to_action: "Schedule follow-up meeting next week",
  tone_preference: "Professional but friendly",
  language_style: "Clear and concise",
  key_points: "Project milestones, budget updates, resource allocation",
  known_preferences: ["Prefers bullet points", "Likes data-driven insights", "Requests action items at the end"],
  personal_sign_off: "Best regards",
  email_signature: `Sarah Johnson\nSenior Product Manager\nTech Innovators Inc.\n+1 (555) 123-4567`,
  work_hours: ["9:00 AM EST", "5:00 PM EST"],
  availability_note: "Available for urgent matters via Slack outside work hours",
  email_length_preference: "Medium (300-500 words)",
  urgency_level: "High",
  preferred_response_patterns: ["Pattern A", "Pattern B"],
  personalization_triggers: ["Trigger 1", "Trigger 2"],
  contextual_data: ["Previous interactions", "Customer notes"],
  business_overview: "Tech Innovators specializes in enterprise software solutions.",
  pricing_info: "Custom solutions start at $10,000.",
  frequently_asked_questions: ["What industries do you serve?", "How do I get support?", "What is your pricing model?"],
  services_offered: ["Custom software development", "24/7 support", "Cloud integration"],
  contact_email: "<EMAIL>",
  contact_phone: "+***********",
  location_address: "123 Innovation Drive, Tech City, CA 90210",
  greeting_message: "Thank you for calling Tech Innovators!",
  follow_up_message: "I’ll follow up with an email summarizing our discussion.",
  dynamic_scripts: ["Script 1: Greeting", "Script 2: Follow-up"],
  escalation_contact: "+****************",
  supported_languages: ["English", "Spanish"],
  sentiment_triggers: ["If frustrated, escalate immediately.", "If satisfied, ask for feedback."],
  additional_resources: "https://www.techinnovators.com/resources",
  adaptive_learning_enabled: true,
  response_time_preferences: "Balanced",
  default_escalation_path: "Escalate to Sarah's assistant.",
  backup_agent_contact: "+****************",
});

function getNameFromAttr(attr: string) {
  return attr.replace(/_/g, " ").replace(/\b\w/g, (c) => c.toUpperCase());
}

function getValue(value: any) {
  return Array.isArray(value) ? value.join(", ") : value;
}

function removeEditKey(key: string) {
  keysOnEdit.value = keysOnEdit.value.filter((k) => k !== key);
}

function updateContext(key: string, oldValue: any, newValue: any) {
  sharedContext.value = {
    ...sharedContext.value,
    [key]: Array.isArray(oldValue) ? newValue.split(",") : newValue,
  };
}

function revertChange(key: string, oldValue: any) {
  updateContext(key, oldValue, oldValue);
  removeEditKey(key);
}

async function handleJumpToWork() {
  const emailKeys = ["personal_sign_off", "email_signature", "email_length_preference", "known_preferences"];
  const phoneKeys = [
    "preferred_response_patterns",
    "personalization_triggers",
    "contextual_data",
    "business_overview",
    "pricing_info",
    "frequently_asked_questions",
    "services_offered",
    "contact_email",
    "contact_phone",
    "location_address",
    "greeting_message",
    "follow_up_message",
    "dynamic_scripts",
    "escalation_contact",
    "supported_languages",
    "sentiment_triggers",
    "additional_resources",
    "adaptive_learning_enabled",
    "response_time_preferences",
    "default_escalation_path",
    "backup_agent_contact",
  ];
  const sharedKeys = [
    "full_name",
    "job_title",
    "organization",
    "target_audience",
    "communication_goal",
    "call_to_action",
    "tone_preference",
    "language_style",
    "key_points",
    "work_hours",
    "availability_note",
    "urgency_level",
  ];

  const extractContext = (keys: string[]) =>
    Object.fromEntries(
      keys.map((key) => [key, (sharedContext.value as Record<string, any>)[key]]).filter(([, val]) => val !== undefined)
    );

  const emailContext = {
    ...extractContext(sharedKeys),
    ...extractContext(emailKeys),
  };

  const phoneContext = {
    ...extractContext(sharedKeys),
    ...extractContext(phoneKeys),
  };

  //  console.log("Email Context:", emailContext);
  //  console.log("Phone Context:", phoneContext);

  try {
    await invoke("initialize_contexts", {
      emailContext,
      phoneContext,
    });
    // await invoke("fetch_all_emails", { message: "fetch_emails" });
    // router.replace("/");
    await auth.openMainWindow();
  } catch (err) {
    console.error("Error initializing contexts:", err);
  }
}

onMounted(() => {
  const appWindow = getCurrentWindow();
  appWindow.setSize(new PhysicalSize(1000, 750));
});
</script>

<style scoped>
.bg-primary {
  background-color: #f9fafb;
}
.text-secondary-700 {
  color: #374151;
}
.bg-secondary-50 {
  background-color: #f8fafc;
}
.bg-accent-500 {
  background-color: #2563eb;
}
.bg-accent-600 {
  background-color: #1d4ed8;
}
.text-secondary-900 {
  color: #111827;
}
</style>
