
import CalApi from "./cal-api-service";
import { CalSchedule } from "../../models/schedule-model";
import { Booking, BookingStatus } from "../../models/booking-model";

interface CalBookingsRes {
  bookings: Booking[]
}

interface CalBookingRes {
  booking: Booking
}

async function getUserBookings(userId: number): Promise<Booking[] | null> {
  try {
    const response = await CalApi.get<CalBookingsRes>(`/api/bookings/${userId}`);
    //  console.log("All bookings =>", response);
    return response.data.bookings;
  } catch (error) {
    console.error("ERR => ", error);
    return null
  }
}

interface CalUpdateSchedule extends Omit<CalSchedule, "id" | "userId" | "availability"> { }

async function changeBookingStatus(id: number, status: BookingStatus): Promise<Booking | null> {
  try {

    const response = await CalApi.patch<CalBookingRes>(`api/bookings/${id}`, { status });
    return response.data.booking;
  } catch (error) {
    console.error("ERR => ", error);
    return null
  }
}

interface CalCreateSchedule extends Omit<CalSchedule, "id" | "availability"> { }

async function createSchedule(sch: Partial<CalCreateSchedule>): Promise<Booking | null> {
  try {
    const response = await CalApi.post<CalBookingRes>(`api/schedules`, sch);
    return response.data.booking;
  } catch (error) {
    console.error("ERR => ", error);
    return null
  }
}


async function cancelBooking(id: number): Promise<{ success: boolean, message: string } | null> {
  try {
    const response = await CalApi.delete(`/bookings/${id}/cancel`);
    return response.data;
  } catch (error) {
    console.error("ERR => ", error);
    return null
  }
}

async function getTodayBookings(id: number) {
  try {
    const response = await CalApi.get<CalBookingsRes>(`/api/bookings/${id}/today`);
    return response.data.bookings;
  } catch (error) {
    console.error("ERR => ", error);
    return null
  }
}

export const calBookingsService = {
  getUserBookings,
  changeBookingStatus,
  createSchedule,
  cancelBooking,
  getTodayBookings
}
