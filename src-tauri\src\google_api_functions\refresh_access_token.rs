use crate::models::app_data::AppData;
use crate::models::user_data::User;
use crate::models::user_data::UserData;
use dotenv::dotenv;
use oauth2::basic::BasicTokenType;
use reqwest::Client;
use serde_json::json;
use std::error::Error;
use std::sync::Arc;
use tauri::{ Manager, RunEvent, WindowEvent };
use tokio::sync::Mutex;
use tokio::sync::MutexGuard;
use tokio::sync::RwLock;
use tracing::{ error, info, warn };

use oauth2::{
    basic::BasicClient,
    reqwest::async_http_client,
    revocation::StandardRevocableToken,
    AccessToken,
    AuthUrl,
    AuthorizationCode,
    ClientId,
    ClientSecret,
    CsrfToken,
    EmptyExtraTokenFields,
    PkceCodeChallenge,
    RedirectUrl,
    RefreshToken,
    RevocationUrl,
    Scope,
    StandardTokenResponse,
    TokenResponse,
    TokenUrl,
};

pub async fn refresh_access_token(refresh_token: &str) -> Result<String, String> {
    println!(" this inside my access token refeshier \n {:#?}", refresh_token);

    //get the google client ID and the client secret from .env file
    dotenv().ok();

    let client_id_string = std::env
        ::var("GOOGLE_CLIENT_ID")
        .unwrap_or_else(|_| "Missing GOOGLE_CLIENT_ID".to_string());
    // println!(
    //     " this inside my access token refeshier  GOOGLE_CLIENT_ID \n {:#?}",
    //     client_id_string
    // );
    let client_secret_string = std::env
        ::var("GOOGLE_CLIENT_SECRET")
        .unwrap_or_else(|_| "Missing GOOGLE_CLIENT_ID".to_string());

    // Convert the String variables to &str
    let client_id: &str = &client_id_string;
    let client_secret: &str = &client_secret_string;
    let token_url = "https://oauth2.googleapis.com/token";

    let params = [
        ("client_id", client_id),
        ("client_secret", client_secret),
        ("refresh_token", refresh_token),
        ("grant_type", "refresh_token"),
    ];

    let client = Client::new();
    let response = match client.post(token_url).form(&params).send().await {
        Ok(resp) => resp,
        Err(err) => {
            eprintln!("Failed to send request: {:?}", err);
            return Err("Failed to send request".to_string());
        }
    };

    if response.status().is_success() {
        let token_response = match
            response.json::<
                oauth2::StandardTokenResponse<EmptyExtraTokenFields, BasicTokenType>
            >().await
        {
            Ok(token) => token,
            Err(err) => {
                eprintln!("Failed to parse token response: {:?}", err);
                return Err("Failed to parse token response".to_string());
            }
        };
        Ok(token_response.access_token().secret().clone())
    } else {
        Err(format!("Failed to refresh access token: {}", response.status()).into())
    }
}
