use diesel::{
    query_dsl::methods::{FilterDsl, OrderDsl}, // <- Add this line
    BoolExpressionMethods,
    ExpressionMethods,
    RunQueryDsl,
};
use std::{thread::sleep, time::Duration};

use diesel::result::Error;

use crate::schema::important_domains::dsl::*;
use crate::{
    db::{establish_db_connection, get_pooled_connection},
    models::important_domain::ImportantDomain,
    schema::important_domains::dsl::{created_at, id, important_domains},
};

pub fn insert(new_domain: ImportantDomain) -> Result<(), String> {
    let conn = &mut get_pooled_connection();
    diesel::insert_into(important_domains)
        .values(&new_domain)
        .execute(conn)
        .map_err(|e| e.to_string())?;
    Ok(())
}

pub fn get_all() -> Vec<ImportantDomain> {
    let conn = &mut get_pooled_connection();
    important_domains
        .order(created_at.desc())
        .load::<ImportantDomain>(conn)
        .expect("Failed to load important domains")
}

pub fn get_by_id(email: String) -> Option<ImportantDomain> {
    let conn = &mut get_pooled_connection();
    important_domains
        .filter(id.eq(email))
        .first::<ImportantDomain>(conn)
        .ok()
}

pub fn update(domain_other: ImportantDomain) -> Result<(), String> {
    let conn = &mut get_pooled_connection();

    diesel::update(important_domains.filter(id.eq(&domain_other.id)))
        .set((
            domain.eq(domain_other.domain),
            category.eq(domain_other.category),
            name.eq(domain_other.name),
            avatar_link.eq(domain_other.avatar_link),
        ))
        .execute(conn)
        .map_err(|e| e.to_string())?;

    Ok(())
}

pub fn delete(email: String) -> Result<(), String> {
    let conn = &mut get_pooled_connection();
    diesel::delete(important_domains.filter(id.eq(email)))
        .execute(conn)
        .map_err(|e| e.to_string())?;
    Ok(())
}

pub fn get_important_domain_by_email(email: &str) -> Option<ImportantDomain> {
    let conn = &mut get_pooled_connection();

    important_domains
        .filter(id.eq(email)) // we're using full email as the primary key (id)
        .first::<ImportantDomain>(conn)
        .ok()
}
