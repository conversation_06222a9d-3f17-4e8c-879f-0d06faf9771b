import axios from "axios";
import { <PERSON><PERSON><PERSON>User, CalUser } from "../../models/user";
import Cal<PERSON>pi from "./cal-api-service";
import { useCurrentUserStore } from "../../stores/currentUser";

interface CalUsersRes {
  users: CalUser[]
}

interface CalUserRes {
  user: CalUser
}

async function createUser(user: CalCreateUser): Promise<CalUser | null> {
  try {
    //  console.log("CREATE USER =>", user);

    const response = await CalApi.post<CalUserRes>("api/users", user);
    //  console.log("CREATE USER =>", response);
    return response.data.user;
  } catch (error) {
    console.error("ERR => ", error);
    return null
  }
}

async function getUserByEmail(email: string) {
  try {
    const response = await CalApi.get<CalUserRes>("/api/users", { params: { email } });
    //  console.log("GET USER BY EMAIL =>", response);


    return response.data.user;///response.data.users.length > 0 ? response.data.users[0] : null;
  } catch (error) {
    console.error("ERR => ", error);
  }
}

async function updateCurrentUser(data: Partial<CalUser>) {
  try {
    const currentUserId = useCurrentUserStore().calInfo?.id;
    if (currentUserId) {
      console.log("🚀 ~ updateCurrentUser ~ currentUserId:", currentUserId, data)
      const response = await CalApi.patch<CalUserRes>(`api/users/${currentUserId}`, data);
      return response.data.user;
    }
    return null;
  } catch (error) {
    console.error("ERR => ", error);
  }
}




export const calUsersService = {
  createUser,
  getUserByEmail,
  updateCurrentUser
}
