use crate::schema::emails;
use chrono::{DateTime, NaiveDateTime, Utc};
use diesel::{Insertable, Queryable};
use serde::{Deserialize, Serialize};

#[derive(Serialize, Queryable, Insertable, Deserialize, Debug, Clone)]
#[diesel(table_name = emails)]
pub struct Email {
    pub id: String,                            // Unique identifier for the email
    pub subject: String,                       // The subject of the email
    pub snippet: String,                       // A short snippet or preview of the email content
    pub from: String,                          // The sender of the email
    pub to: String,                            // Recipients' email addresses (as JSON string)
    pub cc: Option<String>,                    // Email addresses in the CC field (as J<PERSON>N string)
    pub bcc: Option<String>,                   // Email addresses in the BCC field (as JSON string)
    pub date: NaiveDateTime, // The date and time when the email was sent or received
    pub category: Option<String>, // The category of the email (e.g., 'Work', 'Personal', etc.)
    pub labels: Option<String>, // Labels associated with the email (as <PERSON><PERSON><PERSON> string)
    pub attachments: Option<String>, // Identifiers or file names of attachments (as <PERSON><PERSON><PERSON> string)
    pub attachment_types: Option<String>, // Types of attachments (as CSV string)
    pub total_attachment_size: Option<i32>, // Total size of all attachments in bytes
    pub metadata_headers: Option<String>, // Specific headers to include when format is METADATA (as JSON string)
    pub email_body_url: Option<String>,   // URL to fetch the full email body
    pub is_read: Option<bool>,            // Flag to indicate if the email has been read
    pub thread_id: Option<String>,        // ID of the conversation thread this email belongs to
    pub thread_summary: Option<String>,   // Summary of the conversation thread
    pub priority: Option<String>, // The priority of the email (e.g., High Priority, Low Priority)
    pub urgency_score: Option<i32>, // Urgency score calculated based on email content
    pub sentiment: Option<String>, // Sentiment of the email content
    pub actionable_items: Option<String>, // List of actionable items identified in the email content (as JSON string)
    pub language: Option<String>,         // Language detected in the email content
    pub phishing_risk: Option<String>,    // Assessed phishing risk level of the email
    pub sender_reputation: Option<String>, // Reputation level of the sender
    pub full_domain: Option<String>,      // Stores the full domain (e.g., info.twilio.com)
    pub main_domain: Option<String>,      // Stores the main domain (nullable, can be NULL)
    pub storage_location: Option<String>, // Indicates where the email data is stored (e.g., "cloud", "local")
    pub is_flagged: Option<bool>,         // Flag to indicate if the email is marked for follow-up
    pub process_flag: Option<bool>,       // Flag to indicate if the email needs further processing
    pub email_type: Option<String>, // Type of email (e.g., Incoming, Outgoing, Reply, Forward)
    pub is_thread_root: Option<bool>, // Indicates if this is the root email of a thread
    pub received_as: Option<String>, // Indicates how the email was received (e.g., to, cc, bcc)
    pub parent_email_id: Option<String>, // Reference to the parent email in the thread
    pub read_receipt_url: Option<String>, // URL to track if the email was opened
    pub reply_suggestion: Option<String>, // AI-generated reply suggestions (as JSON string)
    pub follow_up_date: Option<NaiveDateTime>, // Suggested follow-up date for the email
    pub meeting_proposed: Option<bool>, // Indicates if the email proposes a meeting
    pub meeting_link: Option<String>, // Link to the meeting if scheduled
    pub is_delegated: Option<bool>, // Indicates if the email was delegated to another user
    pub task_status: Option<String>, // Status of actionable tasks (e.g., Pending, Completed)
    pub auto_reply_sent: Option<bool>, // Indicates if an auto-reply was sent
    pub flagged_keywords: Option<String>, // Important keywords flagged in the email (as JSON string)
    pub attachments_downloaded: Option<bool>, // Indicates if attachments were downloaded
    pub shared_with: Option<String>, // List of users or groups the email is shared with (as JSON string)
    pub analytics_score: Option<i32>, // Custom score for analyzing email performance
    pub response_time: Option<i32>,  // Time taken to respond to the email (in seconds)
    pub ai_generated: Option<bool>,  // Indicates if the email content was AI-generated
    pub is_send: Option<bool>,       // Indicates if the email content was AI-generated
    pub source_app: Option<String>,  // Source application (e.g., email, chat, notifications)
    pub created_at: Option<NaiveDateTime>, // Record creation timestamp
    pub updated_at: Option<NaiveDateTime>, // Record update timestamp
}
