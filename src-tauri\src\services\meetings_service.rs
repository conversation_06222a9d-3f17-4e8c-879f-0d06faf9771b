use crate::schema::meetings;
use crate::schema::meetings::dsl;
use crate::{db::establish_db_connection, db::get_pooled_connection, models::meeting::Meeting};
use diesel::prelude::*;
use diesel::result::Error;
use diesel::Connection;
use std::thread;
use std::time::Duration;

// Function to list meetings by a specific category or any other filter criteria
pub fn list_meetings(category_id: &String) -> Result<Vec<Meeting>, Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        dsl::meetings
            .filter(dsl::category.eq(category_id))
            .order_by(dsl::start_time.desc())
            .load::<Meeting>(conn)
    })
}

// Function to store a new meeting, with retry logic for handling database locks
pub fn store_new_meeting(new_meeting: &Meeting) -> Result<(), Error> {
    let max_attempts = 5; // Maximum number of retry attempts
    let mut attempts = 0;
    let mut backoff = 100; // Initial backoff time in milliseconds

    while attempts < max_attempts {
        // let connection = &mut establish_db_connection();
        let connection = &mut get_pooled_connection();

        let result = connection.transaction::<_, Error, _>(|conn| {
            // Check if the meeting already exists
            let existing_meeting = meetings::dsl::meetings
                .filter(meetings::dsl::id.eq(&new_meeting.id))
                .first::<Meeting>(conn)
                .optional()?;

            if existing_meeting.is_none() {
                // If the meeting doesn't exist, insert it
                diesel::insert_into(meetings::table)
                    .values(new_meeting)
                    .execute(conn)?;
            } else {
                // Optionally, log that the meeting already exists
                println!(
                    "Meeting with id {} already exists, skipping insertion",
                    new_meeting.id
                );
            }

            Ok(())
        });

        match result {
            Ok(_) => return Ok(()), // Success, exit the loop
            Err(diesel::result::Error::DatabaseError(
                diesel::result::DatabaseErrorKind::Unknown,
                info,
            )) if info.message().contains("database is locked") => {
                // If the database is locked, retry after a delay
                attempts += 1;
                println!("Database is locked, attempt {}/{}. Inside store_new_meeting Retrying in {}ms...", attempts, max_attempts, backoff);
                thread::sleep(Duration::from_millis(backoff)); // Wait before retrying
                backoff *= 2; // Exponential backoff
            }
            Err(e) => return Err(e), // For any other error, return immediately
        }
    }

    Err(diesel::result::Error::DatabaseError(
        diesel::result::DatabaseErrorKind::Unknown,
        Box::new("Max retry attempts reached, database is locked.".to_string()),
    ))
}

// Function to delete a meeting by its ID
pub fn delete_meeting(meeting_id: String) -> Result<(), Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        // Delete the meeting from the meetings table
        diesel::delete(dsl::meetings.filter(crate::schema::meetings::dsl::id.eq(meeting_id)))
            .execute(conn)
            .map_err(|err| {
                eprintln!("Error deleting meeting: {}", err);
                err
            })?;

        Ok(())
    })
}

pub fn delete_all_meetings() -> Result<usize, Error> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        // Delete all meetings from the meetings table
        let deleted_rows = diesel::delete(dsl::meetings).execute(conn).map_err(|err| {
            eprintln!("Error deleting all meetings: {}", err);
            err
        })?;

        Ok(deleted_rows)
    })
}
