<template>
    <div class="size-full bg-secondary-200 rounded-md flex justify-center items-center flex-col">
      <div class="text-sm text-center font-medium text-secondary-600">{{ props.label }}</div>
      <div class="text-4xl text-primary-800">{{ props.value }}</div>
    </div>
  </template>
  
  <script setup lang="ts">
  const props = defineProps({
    label: String,
    value: Number,
  });
  </script>
  
  <style scoped></style>