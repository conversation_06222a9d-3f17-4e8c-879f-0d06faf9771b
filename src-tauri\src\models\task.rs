use crate::schema::{subtasks, tasks};
use chrono::NaiveDateTime;
use diesel::prelude::*;
use diesel::AsChangeset;
use diesel::Insertable;
use serde::{Deserialize, Serialize};

// NewTask - Used for inserting new tasks into the database
#[derive(Insertable, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = tasks)]
pub struct NewTask {
    pub id: i32,
    pub title: String,
    pub category: Option<String>,          // Nullable<Text>
    pub sub_type: Option<String>,          // Nullable<Text>
    pub status: Option<String>,            // Nullable<Text>
    pub priority: Option<String>,          // Nullable<Text>
    pub linked_entity: Option<String>,     // Nullable<Text>
    pub context_type: Option<String>,      // Nullable<Text>
    pub context_data: Option<String>,      // Nullable<Text>
    pub due_date: Option<String>,          // Nullable<Text>
    pub assigned_to: Option<String>,       // Nullable<Text>
    pub requires_sync: Option<bool>,       // Nullable<Bool>
    pub queue_position: Option<i32>,       // Nullable<Integer>
    pub snoozed_until: Option<String>,     // Nullable<Text>
    pub auto_executable: Option<bool>,     // Nullable<Bool>
    pub execution_status: Option<String>,  // Nullable<Text>
    pub execution_attempts: Option<i32>,   // Nullable<Integer>
    pub last_attempted_at: Option<String>, // Nullable<Text>
    pub reschedule_count: Option<i32>,     // Nullable<Integer>
    pub created_at: Option<String>,        // Nullable<Text>
}

/// Task - Represents an existing task retrieved from the database
#[derive(Queryable, Identifiable, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = tasks)]
pub struct Task {
    pub id: i32,
    pub title: String,
    pub category: Option<String>,          // Nullable<Text>
    pub sub_type: Option<String>,          // Nullable<Text>
    pub status: Option<String>,            // Nullable<Text>
    pub priority: Option<String>,          // Nullable<Text>
    pub linked_entity: Option<String>,     // Nullable<Text>
    pub context_type: Option<String>,      // Nullable<Text>
    pub context_data: Option<String>,      // Nullable<Text>
    pub due_date: Option<String>,          // Nullable<Text>
    pub assigned_to: Option<String>,       // Nullable<Text>
    pub requires_sync: Option<bool>,       // Nullable<Bool>
    pub queue_position: Option<i32>,       // Nullable<Integer>
    pub snoozed_until: Option<String>,     // Nullable<Text>
    pub auto_executable: Option<bool>,     // Nullable<Bool>
    pub execution_status: Option<String>,  // Nullable<Text>
    pub execution_attempts: Option<i32>,   // Nullable<Integer>
    pub last_attempted_at: Option<String>, // Nullable<Text>
    pub reschedule_count: Option<i32>,     // Nullable<Integer>
    pub created_at: Option<String>,        // Nullable<Text>
}

/// NewSubtask - Used for inserting new subtasks into the database
#[derive(Insertable, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = subtasks)]
pub struct NewSubtask {
    pub id: i32,
    pub parent_task_id: i32,
    pub title: String,
    pub status: Option<String>,            // Nullable<Text>
    pub priority: Option<String>,          // Nullable<Text>
    pub assigned_to: Option<String>,       // Nullable<Text>
    pub due_date: Option<String>,          // Nullable<Text>
    pub requires_sync: Option<bool>,       // Nullable<Bool>
    pub queue_position: Option<i32>,       // Nullable<Integer>
    pub snoozed_until: Option<String>,     // Nullable<Text>
    pub auto_executable: Option<bool>,     // Nullable<Bool>
    pub execution_status: Option<String>,  // Nullable<Text>
    pub execution_attempts: Option<i32>,   // Nullable<Integer>
    pub last_attempted_at: Option<String>, // Nullable<Text>
    pub reschedule_count: Option<i32>,     // Nullable<Integer>
    pub created_at: Option<String>,        // Nullable<Text>
}

/// Subtask - Represents an existing subtask retrieved from the database
#[derive(Queryable, Identifiable, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = subtasks)]
pub struct Subtask {
    pub id: i32,
    pub parent_task_id: i32,
    pub title: String,
    pub status: Option<String>,            // Nullable<Text>
    pub priority: Option<String>,          // Nullable<Text>
    pub assigned_to: Option<String>,       // Nullable<Text>
    pub due_date: Option<String>,          // Nullable<Text>
    pub requires_sync: Option<bool>,       // Nullable<Bool>
    pub queue_position: Option<i32>,       // Nullable<Integer>
    pub snoozed_until: Option<String>,     // Nullable<Text>
    pub auto_executable: Option<bool>,     // Nullable<Bool>
    pub execution_status: Option<String>,  // Nullable<Text>
    pub execution_attempts: Option<i32>,   // Nullable<Integer>
    pub last_attempted_at: Option<String>, // Nullable<Text>
    pub reschedule_count: Option<i32>,     // Nullable<Integer>
    pub created_at: Option<String>,        // Nullable<Text>
}

impl From<NewTask> for Task {
    fn from(new_task: NewTask) -> Self {
        Task {
            id: new_task.id,
            title: new_task.title,
            category: new_task.category,
            sub_type: new_task.sub_type,
            status: new_task.status,
            priority: new_task.priority,
            linked_entity: new_task.linked_entity,
            context_type: new_task.context_type,
            context_data: new_task.context_data,
            due_date: new_task.due_date,
            assigned_to: new_task.assigned_to,
            requires_sync: Some(new_task.requires_sync.unwrap_or(false)),
            queue_position: new_task.queue_position,
            snoozed_until: new_task.snoozed_until,
            auto_executable: Some(new_task.auto_executable.unwrap_or(false)),
            execution_status: Some(
                new_task
                    .execution_status
                    .unwrap_or("not_started".to_string()),
            ),
            execution_attempts: new_task.execution_attempts.or(Some(0)),
            last_attempted_at: new_task.last_attempted_at,
            reschedule_count: Some(new_task.reschedule_count.unwrap_or(0)),
            created_at: Some(
                new_task
                    .created_at
                    .map(|dt| {
                        NaiveDateTime::parse_from_str(&dt, "%Y-%m-%d %H:%M:%S")
                            .unwrap_or_else(|_| chrono::Utc::now().naive_utc())
                    })
                    .unwrap_or_else(|| chrono::Utc::now().naive_utc())
                    .format("%Y-%m-%d %H:%M:%S")
                    .to_string(),
            ),
        }
    }
}

impl From<NewSubtask> for Subtask {
    fn from(new_subtask: NewSubtask) -> Self {
        Subtask {
            id: new_subtask.id,
            parent_task_id: new_subtask.parent_task_id,
            title: new_subtask.title,
            status: new_subtask.status,
            priority: new_subtask.priority,
            assigned_to: new_subtask.assigned_to,
            due_date: new_subtask.due_date,
            requires_sync: Some(new_subtask.requires_sync.unwrap_or(false)),
            queue_position: new_subtask.queue_position,
            snoozed_until: new_subtask.snoozed_until,
            auto_executable: Some(new_subtask.auto_executable.unwrap_or(false)),
            execution_status: Some(
                new_subtask
                    .execution_status
                    .unwrap_or("not_started".to_string()),
            ),
            execution_attempts: Some(new_subtask.execution_attempts.unwrap_or(0)),
            last_attempted_at: new_subtask.last_attempted_at,
            reschedule_count: Some(new_subtask.reschedule_count.unwrap_or(0)),
            created_at: Some(
                new_subtask
                    .created_at
                    .map(|dt| {
                        NaiveDateTime::parse_from_str(&dt, "%Y-%m-%d %H:%M:%S")
                            .unwrap_or_else(|_| chrono::Utc::now().naive_utc())
                    })
                    .unwrap_or_else(|| chrono::Utc::now().naive_utc())
                    .format("%Y-%m-%d %H:%M:%S")
                    .to_string(),
            ),
        }
    }
}

/// Structure to store task stats
#[derive(serde::Serialize)]
pub struct TaskStats {
    pub active: i64,
    pub pending: i64,
    pub ai_executed: i64,
    pub escalated: i64,
}
