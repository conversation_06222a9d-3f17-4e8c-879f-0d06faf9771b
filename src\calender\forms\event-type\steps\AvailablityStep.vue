<template>
  <div class="space-y-4">
    <!-- Schedule Selection -->
    <div class="bg-primary rounded-lg shadow-sm border border-primary-300 p-4">
      <h2 class="text-lg font-semibold text-base-500 mb-4">Availability Schedule</h2>
      <div>
        <label class="block text-sm font-medium text-secondary-700 mb-1">Select Schedule</label>
        <select
          class="w-full px-3 py-2 bg-primary border border-primary-400 rounded-lg outline-none focus:border-secondary-600 transition-colors duration-200 text-base-500"
          v-model="selectedScheduleId"
        >
          <option value="" disabled>Select an availability schedule</option>
          <option v-for="s of schedules" :key="s.id" :value="s.id">{{ s.name }}</option>
        </select>
      </div>
    </div>

    <!-- Schedule Preview -->
    <div class="bg-primary rounded-lg shadow-sm border border-primary-300 p-4" v-if="selectedSchedule">
      <h2 class="text-lg font-semibold text-base-500 mb-4">Schedule Preview</h2>
      <div class="space-y-2">
        <div
          :key="selectedScheduleId"
          class="flex items-center justify-between p-2.5 bg-primary-200 hover:bg-primary-300 border border-primary-400 rounded-lg transition-all duration-200"
          v-for="(value, index) of DAYS"
          :class="{ 'bg-secondary-100 border-secondary-300': dayActive(index) }"
        >
          <div class="flex items-center gap-3">
            <h3 class="text-sm font-medium text-base-500 w-20">{{ value }}</h3>
          </div>
          <div class="flex-1 text-right">
            <div v-if="dayActive(index)" class="space-y-1">
              <div v-for="(av, i) of availability.days[index]" :key="i" class="text-xs text-secondary-600">
                {{ av.startTime }} - {{ av.endTime }}
              </div>
            </div>
            <div v-else class="text-xs text-secondary-500 italic">Unavailable</div>
          </div>
        </div>

        <!-- Timezone Info -->
        <div class="pt-3 mt-3 border-t border-primary-300 flex items-center text-sm gap-2">
          <GlobeAltIcon class="w-4 h-4 text-secondary-600" />
          <span class="text-secondary-600">{{ selectedSchedule?.timeZone }}</span>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="bg-primary rounded-lg shadow-sm border border-primary-300 p-8 text-center">
      <div class="text-secondary-400 mb-2">
        <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1"
            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
          ></path>
        </svg>
      </div>
      <p class="text-sm text-secondary-600">Select a schedule to preview availability</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { useCurrentUserStore } from "../../../../stores/currentUser";
import { CalSchedule } from "../../../../models/schedule-model";
import { calSchedulesService } from "../../../../services/schedules-cal-service";
import { AvailGroups, groupAvailability } from "../../../../utils/availability-utils";
import { DAYS } from "../../../../models/availability-model";
import { GlobeAltIcon } from "@heroicons/vue/24/outline";
const props = defineProps({
  selectedId: {
    type: Number,
  },
});
const session = useCurrentUserStore();
const schedules = ref<CalSchedule[]>([]);
const selectedScheduleId = ref<number | undefined>(props.selectedId ?? session.calInfo?.defaultScheduleId ?? undefined);

const selectedSchedule = computed<CalSchedule | undefined>(() => {
  return schedules.value.find((s) => s.id === selectedScheduleId.value);
});

const availability = computed<AvailGroups>(() => {
  if (selectedSchedule.value) return groupAvailability(selectedSchedule.value.availability);
  return { days: {}, dates: {} };
});

async function getSchedules() {
  if (session.calInfo?.id) {
    schedules.value = (await calSchedulesService.getUserSchedules(session.calInfo?.id)) ?? [];
    console.log("Loaded schedules:", schedules.value);

    // If no schedule is selected but we have schedules, select the default or first one
    if (!selectedScheduleId.value && schedules.value.length > 0) {
      selectedScheduleId.value = session.calInfo?.defaultScheduleId ?? schedules.value[0].id;
      console.log("Auto-selected schedule:", selectedScheduleId.value);
    }
  }
}

function dayActive(index: number) {
  const active = Object.keys(availability.value.days ?? []).includes(index.toString());
  return active;
}

const emit = defineEmits<{
  (e: "change", value: number): void;
}>();

watch(selectedScheduleId, (value) => {
  console.log("Selected schedule changed to:", value, "Schedule object:", selectedSchedule.value);
  emit("change", value ?? session.calInfo?.defaultScheduleId ?? 0);
});

watch(
  selectedSchedule,
  (schedule) => {
    console.log("Selected schedule object changed:", schedule);
    console.log("Availability data:", availability.value);
  },
  { deep: true }
);

onMounted(async () => {
  console.log("AvailabilityStep mounted, session:", session.calInfo);
  await getSchedules();
});
</script>

<style scoped></style>
