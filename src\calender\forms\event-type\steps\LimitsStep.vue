<template>
  <div class="size-full flex flex-col gap-4">
    <div class="p-5 rounded-lg border-2 border-primary-200 flex flex-col gap-2">
      <div class="flex justify-between items-center gap-4">
        <div class="flex-1 flex flex-col">
          <label class="text-sm font-semibold">Before event</label>
          <select v-model="editData.beforeEventBuffer" class="bg-transparent rounded-md py-1">
            <option value="0">No buffer time</option>
            <option v-for="time of timeBuffer" :value="time">{{ time }} Minutes</option>
          </select>
        </div>
        <div class="flex-1 flex flex-col">
          <label class="text-sm font-semibold">After event</label>
          <select v-model="editData.afterEventBuffer" class="bg-transparent rounded-md py-1">
            <option value="0">No buffer time</option>
            <option v-for="time of timeBuffer" :value="time">{{ time }} Minutes</option>
          </select>
        </div>
      </div>
      <div class="flex justify-between items-center gap-4">
        <div class="flex-1 flex flex-col">
          <label class="text-sm font-semibold">Minimum Notice</label>
          <div class="flex gap-1">
            <input
              :value="convertTime(editData.minimumBookingNotice, 'minutes', selectedTimeUnit.toLowerCase())"
              @change="(e:Event)=>editData.minimumBookingNotice = convertTime(parseInt((e.target as HTMLInputElement).value),selectedTimeUnit.toLowerCase(),'minutes')"
              type="number"
              class="flex-1 py-1 bg-transparent border outline-none rounded-md text-primary-900"
            />
            <select
              v-model="selectedTimeUnit"
              @change="() => (editData.minimumBookingNotice = 0)"
              class="bg-transparent rounded-md py-1"
            >
              <option v-for="unit of timeUnits" :value="unit">{{ unit }}</option>
            </select>
          </div>
        </div>
        <div class="flex-1 flex flex-col">
          <label class="text-sm font-semibold">Time-slot intervals</label>
          <select v-model="editData.slotInterval" class="bg-transparent rounded-md py-1">
            <option value="0">Use event length (default)</option>
            <option v-for="time of timeBuffer" :value="time">{{ time }} Minutes</option>
          </select>
        </div>
      </div>
    </div>
    <ToggleAccordion
      id="limit-booking-freq"
      title="Limit booking frequency"
      sub-title="Limit how many times this event can be booked"
      @on-toggle="limitBookingToggle"
    >
      <div class="flex flex-col gap-2">
        <div
          v-if="editData.bookingLimits"
          v-for="key of Object.keys(editData.bookingLimits)"
          class="flex gap-1 max-w-40"
        >
          <input
            type="number"
            min="1"
            value="1"
            @change="(e) => changeBookingLimit(key, parseInt((e.target as HTMLInputElement).value))"
            class="flex-1 py-1 bg-transparent border outline-none rounded-md text-primary-900"
          />
          <select class="bg-transparent rounded-md py-1" :value="key">
            <option v-for="limit of limitVariantArray" :value="limit.value">{{ limit.label }}</option>
          </select>
          <button
            v-if="Object.keys(editData.bookingLimits).length > 1"
            @click="() => deleteBookingLimit(key)"
            class="p-1 bg-red-200 rounded-md text-red-500 hover:bg-red-300 transition-colors duration-300"
          >
            <TrashIcon class="size-5" />
          </button>
        </div>
        <div
          v-if="!availBookingLimits || (availBookingLimits && availBookingLimits.length > 0)"
          @click="addBookingLimit"
          class="flex justify-start items-center gap-1 hover:text-primary-800 transition-colors duration-200 cursor-pointer"
        >
          <PlusIcon class="size-5" />
          <div>Add Limit</div>
        </div>
      </div>
    </ToggleAccordion>

    <ToggleAccordion
      id="only-show-the-first-slot"
      :can-open="false"
      title="Only show the first slot of each day as available"
      sub-title="This will limit your availability for this event type to one slot per day, scheduled at the earliest available time."
      @on-toggle="(value:boolean)=>editData.onlyShowFirstAvailableSlot = value"
    >
    </ToggleAccordion>

    <ToggleAccordion
      id="limit-booking-duration"
      title="Limit total booking duration"
      sub-title="Limit total amount of time that this event can be booked"
      @on-toggle="limitDurationToggle"
    >
      <div class="flex flex-col gap-2">
        <div
          v-if="editData.durationLimits"
          v-for="key of Object.keys(editData.durationLimits)"
          class="flex gap-1 max-w-80"
        >
          <div class="flex max-w-56">
            <input
              type="number"
              min="15"
              step="15"
              value="15"
              @change="(e) => changeDurationLimit(key, parseInt((e.target as HTMLInputElement).value))"
              class="flex-1 w-24 py-1 bg-transparent border outline-none rounded-l-md border-r-0 text-primary-900"
            />
            <div class="flex items-center px-2 bg-primary-300 text-primary-900 rounded-r-md">Minutes</div>
          </div>
          <select class="bg-transparent rounded-md py-1" :value="key">
            <option v-for="limit of limitVariantArray" :value="limit.value">{{ limit.label }}</option>
          </select>
          <button
            v-if="Object.keys(editData.durationLimits).length > 1"
            @click="() => deleteDurationLimit(key)"
            class="p-1 bg-red-200 rounded-md text-red-500 hover:bg-red-300 transition-colors duration-300"
          >
            <TrashIcon class="size-5" />
          </button>
        </div>
        <div
          v-if="!availDurationLimits || (availDurationLimits && availDurationLimits.length > 0)"
          @click="addDurationLimit"
          class="flex justify-start items-center gap-1 hover:text-primary-800 transition-colors duration-200 cursor-pointer"
        >
          <PlusIcon class="size-5" />
          <div>Add Limit</div>
        </div>
      </div>
    </ToggleAccordion>

    <ToggleAccordion
      id="limit-future-bookings"
      title="Limit future bookings"
      sub-title="Limit how far in the future this event can be booked"
      @on-toggle="(value:boolean)=>editData.periodType = value?'ROLLING':'UNLIMITED'"
    >
      <div class="flex flex-col gap-4">
        <div class="flex justify-start items-start gap-2">
          <input
            type="radio"
            name="limit-future-bookings-group"
            :checked="editData.periodType !== 'RANGE'"
            @change="(e:Event)=>editData.periodType = (e.target as HTMLInputElement).checked ?'ROLLING':'RANGE'"
            class="py-1 bg-transparent border outline-none rounded-md text-primary-900"
          />
          <div class="flex flex-col gap-1">
            <div class="flex justify-start items-center gap-2">
              <input
                type="number"
                v-model="editData.periodDays"
                min="1"
                class="flex-1 py-1 bg-transparent border outline-none rounded-md text-primary-900 w-20"
              />
              <select
                @change="(e:Event)=> editData.periodCountCalendarDays = (e.target as HTMLSelectElement).value === 'bd'? false:true"
                :value="editData.periodCountCalendarDays ? 'cd' : 'bd'"
                class="flex-1 py-1 bg-transparent border outline-none rounded-md text-primary-900"
              >
                <option value="bd">Business days</option>
                <option value="cd">Calendar days</option>
              </select>
              <div>into the future</div>
            </div>
            <div class="flex items-center gap-2">
              <input
                type="checkbox"
                @change="(e:Event)=>editData.periodType = (e.target as HTMLInputElement).checked ? 'ROLLING_WINDOW':'ROLLING'"
                id="always-x-days-available"
                class="py-1 bg-transparent border outline-none rounded-md text-primary-900"
              />
              <label for="always-x-days-available">Always {{ editData.periodDays }} days available</label>
            </div>
          </div>
        </div>
        <div class="flex justify-start items-center gap-2">
          <input
            type="radio"
            name="limit-future-bookings-group"
            :checked="editData.periodType === 'RANGE'"
            @change="(e:Event)=>editData.periodType = (e.target as HTMLInputElement).checked ?'RANGE':'ROLLING'"
            class="py-1 bg-transparent border outline-none rounded-md text-primary-900"
          />
          <div class="flex gap-1 items-center">
            <div>Within a date range :</div>
            <div class="flex gap-1 items-center">
              <div>from</div>
              <input
                :value="
                  new Date(editData.periodEndDate ?? new Date().toISOString().split('T')[0]).toISOString().split('T')[0]
                "
                type="date"
                @change="(e:Event)=> editData.periodStartDate = new Date((e.target as HTMLInputElement).value).toISOString()"
                class="flex-1 py-1 bg-transparent border outline-none rounded-md text-primary-900"
              />
            </div>
            <div class="flex gap-1 items-center">
              <div>to</div>
              <input
                type="date"
                :value="
                  new Date(editData.periodEndDate ?? new Date().toISOString().split('T')[0]).toISOString().split('T')[0]
                "
                @change="(e:Event)=> editData.periodEndDate = new Date((e.target as HTMLInputElement).value).toISOString()"
                class="flex-1 py-1 bg-transparent border outline-none rounded-md text-primary-900"
              />
            </div>
          </div>
        </div>
      </div>
    </ToggleAccordion>

    <ToggleAccordion
      id="offset-start-times"
      title="Offset start times"
      sub-title="Offset timeslots shown to bookers by a specified number of minutes"
      @on-toggle="(value:boolean)=>value ? undefined:editData.offsetStart = 0"
    >
      <div class="flex flex-col gap-2">
        <label class="text-sm text-primary-900 font-semibold">Offset by</label>
        <div class="flex max-w-56">
          <input
            type="number"
            min="0"
            v-model="editData.offsetStart"
            class="flex-1 w-24 py-1 bg-transparent border outline-none rounded-l-md border-r-0 text-primary-900"
          />
          <div class="flex items-center px-2 bg-primary-300 text-primary-900 rounded-r-md">Minutes</div>
        </div>
      </div>
    </ToggleAccordion>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import ToggleAccordion from "../../../../components/ui/ToggleAccordion.vue";
import { defaultEventLimits, EventTypeLimits, LimitsVariants } from "../../../../models/event-type-model";
import { PlusIcon, TrashIcon } from "@heroicons/vue/24/outline";
import { convertTime, getDefinedAttributes, removeProperty, TimeUnit } from "../../../../utils/utils";

const timeBuffer = [5, 10, 15, 20, 30, 45, 60, 90, 120];
const timeUnits: TimeUnit[] = ["Minutes", "Hours", "Days"];
const selectedTimeUnit = ref<TimeUnit>("Minutes");

const limitVariantArray: { label: string; value: string }[] = [
  { label: "Per day", value: "PER_DAY" },
  { label: "Per week", value: "PER_WEEK" },
  { label: "Per month", value: "PER_MONTH" },
  { label: "Per year", value: "PER_YEAR" },
];
const limitVariant = ["PER_DAY", "PER_WEEK", "PER_MONTH", "PER_YEAR"];

const props = defineProps({
  limits: {
    type: Object,
    default: null,
  },
});

const data = ref<EventTypeLimits>((props.limits as EventTypeLimits) ?? { ...defaultEventLimits });
const editData = ref<EventTypeLimits>({ ...data.value });

const availBookingLimits = computed(() => {
  if (editData.value.bookingLimits) {
    const definedAttributes = getDefinedAttributes(editData.value.bookingLimits);
    return limitVariant.filter((key) => !definedAttributes.includes(key as keyof LimitsVariants));
  }
  return null;
});

const availDurationLimits = computed(() => {
  if (editData.value.durationLimits) {
    const definedAttributes = getDefinedAttributes(editData.value.durationLimits);
    return limitVariant.filter((key) => !definedAttributes.includes(key as keyof LimitsVariants));
  }
  return null;
});

function limitBookingToggle(value: boolean) {
  if (value)
    editData.value = {
      ...editData.value,
      bookingLimits: {
        PER_DAY: 1,
      },
    };
  else
    editData.value = {
      ...editData.value,
      bookingLimits: null,
    };
}

function limitDurationToggle(value: boolean) {
  if (value)
    editData.value = {
      ...editData.value,
      durationLimits: {
        PER_DAY: 15,
      },
    };
  else
    editData.value = {
      ...editData.value,
      durationLimits: null,
    };
}

function changeBookingLimit(key: string, value: number) {
  editData.value = {
    ...editData.value,
    bookingLimits: {
      ...editData.value.bookingLimits,
      [key]: value,
    },
  };
}

function changeDurationLimit(key: string, value: number) {
  editData.value = {
    ...editData.value,
    durationLimits: {
      ...editData.value.durationLimits,
      [key]: value,
    },
  };
}

function addBookingLimit() {
  if (availBookingLimits.value) {
    // const definedAttributes = getDefinedAttributes(editData.value.bookingLimits);
    const availAttributes = availBookingLimits.value; //limitVariant.filter((key) => !definedAttributes.includes(key as keyof LimitsVariants));
    const chosenOne = availAttributes.length > 0 ? availAttributes[0] : null;
    if (chosenOne) {
      editData.value = {
        ...editData.value,
        bookingLimits: {
          ...editData.value.bookingLimits,
          [chosenOne]: 1,
        },
      };
    }
    //  console.log("available =>", availAttributes);
  }
}

function addDurationLimit() {
  if (availDurationLimits.value) {
    // const definedAttributes = getDefinedAttributes(editData.value.bookingLimits);
    const availAttributes = availDurationLimits.value; //limitVariant.filter((key) => !definedAttributes.includes(key as keyof LimitsVariants));
    const chosenOne = availAttributes.length > 0 ? availAttributes[0] : null;
    if (chosenOne) {
      editData.value = {
        ...editData.value,
        durationLimits: {
          ...editData.value.durationLimits,
          [chosenOne]: 15,
        },
      };
    }
    //  console.log("available =>", availAttributes);
  }
}

function deleteBookingLimit(key: string) {
  if (editData.value.bookingLimits) {
    const bookinglimits = removeProperty(editData.value.bookingLimits, key as keyof LimitsVariants);
    editData.value = {
      ...editData.value,
      bookingLimits: {
        ...bookinglimits,
      },
    };
  }
}

function deleteDurationLimit(key: string) {
  if (editData.value.durationLimits) {
    const durationLimit = removeProperty(editData.value.durationLimits, key as keyof LimitsVariants);
    editData.value = {
      ...editData.value,
      durationLimits: {
        ...durationLimit,
      },
    };
  }
}

const emit = defineEmits<{
  (e: "change", value: EventTypeLimits): void;
}>();

watch(
  editData,
  (value) => {
    //  console.log("Limits Data =>", value);
    emit("change", value);
  },
  { deep: true }
);
</script>

<style scoped></style>
