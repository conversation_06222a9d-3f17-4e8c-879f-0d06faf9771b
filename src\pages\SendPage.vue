<template>
  <div class="relative z-0 flex size-full">
    <!-- Sidebar for Settings -->
    <aside class="w-1/3 border-r px-2 pb-2">
      <div
        v-if="!emailContextOpen"
        class="relative flex flex-col bg-primary-300/20 size-full p-4 drop-shadow-md rounded-md overflow-y-auto custom-scrollbar"
      >
        <button
          @click="emailContextOpen = true"
          class="flex justify-end items-center gap-2 text-sm text-primary-900 hover:text-secondary-700"
        >
          <p>Email Context</p>
          <ArrowRightIcon class="size-4" />
        </button>
        <h2 class="text-xl font-semibold text-dark-400 mt-2">What your email about</h2>

        <!-- <h2 class="text-lg font-semibold mb-4">Compose New Email</h2> -->

        <form @submit.prevent="generateEmail">
          <!-- BCC -->
          <!-- <label class="block mt-4 mb-2 text-gray-700">BCC</label>
        <input
          v-model="bcc"
          class="block w-full p-2 border rounded-md"
          type="email"
          placeholder="Enter BCC emails"
        /> -->

          <!-- Tone Select -->
          <div class="mt-2 mb-2 flex flex-col gap-1">
            <label class="block text-sm text-dark-500">Select tone/voice</label>
            <select
              v-model="tone"
              class="block w-full p-2 border-none rounded-md border-primary-400x text-sm bg-primary-300/50"
            >
              <option>Convincing</option>
              <option>Casual</option>
              <option>Formal</option>
              <option>Professional</option>
              <option>Urgent</option>
            </select>
          </div>

          <div class="mt-2 mb-2 flex flex-col gap-1">
            <label class="block text-dark-500 text-sm">Audience (Optional)</label>
            <div class="flex flex-wrap justify-between items-center gap-1">
              <div
                v-for="audience of audiences"
                @click="
                  selAudiences.includes(audience)
                    ? (selAudiences = selAudiences.filter((a) => a != audience))
                    : selAudiences.push(audience)
                "
                class="border-2 px-1.5 rounded-md py-0.5 text-sm cursor-pointer"
                :class="
                  selAudiences.includes(audience)
                    ? 'text-white bg-dark-400 border-dark-400'
                    : 'text-secondary-600 border-secondary-400'
                "
              >
                {{ audience }}
              </div>
            </div>
          </div>
          <!-- Key Points Textarea -->
          <div class="mt-2 mb-2 flex flex-col gap-1">
            <label class="block text-sm text-dark-500">Key points</label>
            <textarea
              v-model="keyPoints"
              class="block w-full p-2 border-none rounded-md border-primary-400x text-sm bg-primary-300/50 h-60"
              placeholder="Enter key points..."
            ></textarea>
          </div>

          <div class="mt-2 mb-2 flex flex-col gap-1">
            <label class="block text-sm text-dark-500">Email length ({{ emailLength }} words)</label>
            <input
              type="range"
              step="50"
              min="50"
              max="1000"
              v-model="emailLength"
              class="block w-full p-0.5 border rounded-md  border-primary-600x bg-primary-600/30 text-sm"
            ></input>
          </div>

          <div class="mt-2 mb-2 flex w-full justify-between items-center gap-1">
            <label class="block text-sm text-dark-500 font-bold">Use Email Context</label>
            <ToggleButton :value="emailContextEnabled" @changed="(value:boolean)=>emailContextEnabled=value" id="email-context-toggle" />
          </div>
          <!-- Error Message Display -->
          <p v-if="errorMessage" class="text-red-600 mt-1">{{ errorMessage }}</p>

          <!-- Generate Email Button -->
          <button
            @click="generateEmail"
            :disabled="isGenerating"
            class="mt-4 w-full bg-dark-400 hover:bg-dark-400/80 transition-colors duration-200 text-white p-2 rounded-md"
            type="button"
          >
            Generate Email
          </button>
        </form>
      </div>
      <div
        v-if="emailContextOpen"
        class="relative flex flex-col bg-primary-300/20 size-full p-4 drop-shadow-md rounded-md overflow-y-auto custom-scrollbar"
      >
        <button
          @click="emailContextOpen = false"
          class="flex justify-start items-center gap-2 text-sm text-primary-900 hover:text-secondary-700"
        >
          <ArrowLeftIcon class="size-4" />
          <p>Email Context</p>
        </button>
        <h1 class="text-xl font-semibold text-dark-400 mt-2">Email Context</h1>
        <div>
          <EmailContextInput :data="emailContext" @on-update="(value:EmailContext)=>updateEmailContext(value)" />
        </div>
      </div>
    </aside>
    <!-- Email Editor -->
    <!-- Email Editor -->
    <main class="w-2/3 flex-grow flex flex-col justify-between px-10 overflow-y-auto pb-10  custom-scrollbar">
      <div>
        <h2 class="text-xl font-semibold mb-4">Email Snippet</h2>

        <!-- Subject Field -->
        <div class="flex items-center space-x-4 mb-4">
          <label class="block text-dark-700 w-1/4 font-semibold">Subject</label>
          <input
            v-model="emailSubject"
            class="block w-full p-2 border rounded-md flex-1 border-primary-700 bg-primary-600/20"
            type="text"
            placeholder="Enter subject"
          />
        </div>

        <!-- Recipient Field with Error Message -->
        <div class="flex flex-col items-center mb-1">
          <div class="w-full flex items-center space-x-4">
            <label class="block text-dark-700 w-1/4 font-semibold">Recipient</label>
            <input
              v-model="recipient"
              class="block w-full p-2 border rounded-md flex-1 border-primary-700 bg-primary-600/20"
              type="email"
              placeholder="Enter recipient email"
            />
          </div>
          <div class="flex justify-end items-center w-full gap-5 underline text-sm px-2">
            <div
              @click="ccIsOpen = !ccIsOpen"
              class="text-secondary-600 hover:text-dark-400 cursor-pointer transition-all duration-200"
            >
              cc
            </div>
            <div
              @click="bccIsOpen = !bccIsOpen"
              class="text-secondary-600 hover:text-dark-400 cursor-pointer transition-all duration-200"
            >
              bcc
            </div>
          </div>
        </div>
        <p v-if="recipientErrorMessage" class="text-red-600 mt-1">{{ recipientErrorMessage }}</p>

        <!-- CC Field -->
        <div class="flex items-center space-x-4 mb-4" v-if="ccIsOpen">
          <label class="block text-dark-700 w-1/4 font-semibold">Cc</label>
          <input
            v-model="cc"
            class="block w-full p-2 border rounded-md flex-1 border-primary-700 bg-primary-600/20"
            type="email"
            placeholder="Enter cc emails"
          />
        </div>

        <div class="flex items-center space-x-4 mb-4" v-if="bccIsOpen">
          <label class="block text-dark-700 w-1/4 font-semibold">Bcc</label>
          <input
            v-model="bcc"
            class="block w-full p-2 border rounded-md flex-1 border-primary-700 bg-primary-600/20"
            type="email"
            placeholder="Enter bcc emails"
          />
        </div>

        <!-- Email Body -->
        <div class="mb-4">
          <!-- <textarea
          v-model="emailBody"
          class="w-full h-48 p-4 border rounded-md border-primary-700 bg-primary-600/20"
          placeholder="Type your email here..."
        ></textarea> -->
          <ckeditor v-if="editor" v-model="emailBody" :editor="editor" :config="config" />
        </div>

        <!-- Attachments Field -->
        <div class="flex flex-col mb-4 gap-2 ">
          <div class="flex items-center space-x-4">
            <label class="block text-dark-700 w-1/4 font-semibold">Attachments</label>

            <button
              @click="openFiles"
              v-if="attachments.length <= 0"
              class="flex items-center gap-2 bg-primary-700/20 border-primary-700 border py-2 px-4 rounded-md"
            >
              <PaperClipIcon class="size-5" />
              <p>Upload files</p>
            </button>
          </div>
          <div
            v-if="attachments.length > 0"
            class="w-full border-dashed p-1 border-secondary-400 border rounded flex flex-col gap-1"
          >
            <div class="flex justify-center items-center flex-wrap gap-2">
              <div
                v-for="file of attachments.slice(0, showAllFiles ? attachments.length : 4)"
                class="group relative flex items-center gap-1 max-w-28 bg-secondary-400/10 px-1 rounded-md"
              >
                <div class="size-10 flex justify-center items-center rounded-md">
                  <i :class="`fa-regular ${getFileIcon(file.name)} text-2xl`"></i>
                </div>
                <div class="text-secondary-800 text-[10px] line-clamp-2" :title="file.name">{{ file.name }}</div>
                <button
                  @click="() => removeFile(file.path)"
                  class="group-hover:flex size-4 absolute -top-1 -right-1 rounded-full hidden justify-center items-center bg-red-300 hover:bg-red-500 transition-all duration-200 text-white"
                >
                  <XMarkIcon class="size-3" />
                </button>
              </div>
              <button
                @click="openFiles"
                class="text-secondary-500 bg-primary-700/20 rounded hover:bg-primary-700/30 transition-all duration-200 size-10 flex justify-center items-center"
              >
                <PlusIcon class="size-6" />
              </button>
            </div>
            <div v-if="attachments.length > 4" class="flex justify-center items-center flex-col">
              <button
                @click="showAllFiles = !showAllFiles"
                class="flex gap-1 items-center justify-center text-[10px] bg-secondary-200 px-2 py-0.5 rounded-lg"
              >
                <ChevronDownIcon v-if="!showAllFiles" class="size-3" />
                <ChevronUpIcon v-if="showAllFiles" class="size-3" />
                {{ showAllFiles ? "Hide" : "Show all" }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Send Email Button -->
      <button
        @click="sendEmail"
        :disabled="isSending"
        class="mt-4 w-full bg-dark-300 text-white p-2 rounded-md flex justify-center items-center gap-2 hover:bg-dark-400 transition-colors duration-200"
        type="submit"
      >
        <PaperAirplaneIcon class="size-4" />
        <p>Send Email</p>
      </button>
    </main>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { onMounted } from "vue";
import { invoke } from "@tauri-apps/api/core"; // Assuming you're using Tauri for backend interaction
import { PaperAirplaneIcon } from "@heroicons/vue/24/solid";
import EmailContextInput from "../components/ui/EmailContextInput.vue";
import {
  ArrowLeftIcon,
  ArrowRightIcon,
  ChevronDownIcon,
  ChevronUpDownIcon,
  ChevronUpIcon,
  PaperClipIcon,
  PlusIcon,
  XMarkIcon,
} from "@heroicons/vue/24/outline";
import { useCurrentUserStore } from "../stores/currentUser";
import { EmailContext } from "../types";
import { Ckeditor, useCKEditorCloud } from "@ckeditor/ckeditor5-vue";

import "./ckeditor.css";
import { open } from "@tauri-apps/plugin-dialog";
import { AttachmentFile, getFileIcon, parseFilePath } from "../utils/file";
import ToggleButton from "../components/ui/ToggleButton.vue";

interface GeneratedEmail {
  subject:string,
  body:string
}

const cloud = useCKEditorCloud({
  version: "44.1.0",
  premium: true,
});

const editor = computed(() => {
  if (!cloud.data.value) {
    return null;
  }

  return cloud.data.value.CKEditor.ClassicEditor;
});

const config = computed(() => {
  if (!cloud.data.value) {
    return null;
  }

  const {
    Font,
    Essentials,
    Paragraph,
    Bold,
    Italic,
    Bookmark,
    Underline,
    Strikethrough,
    Subscript,
    Superscript,
    Code,
    Link,
    AutoLink,
    List,
    GeneralHtmlSupport,
  } = cloud.data.value.CKEditor;
  return {
    licenseKey: import.meta.env.VITE_CKEDITOR_API_KEY,
    plugins: [
      Font,
      Essentials,
      Paragraph,
      Bold,
      Italic,
      Underline,
      Strikethrough,
      Subscript,
      Superscript,
      Code,
      Bookmark,
      Link,
      AutoLink,
      List,
      GeneralHtmlSupport,
    ],
    toolbar: [
      "undo",
      "redo",
      "|",
      "fontSize",
      "fontFamily",
      "fontColor",
      "fontBackgroundColor",
      "|",
      "bold",
      "italic",
      "underline",
      "strikethrough",
      "|",
      "link",
      "code",
      "bookmark",
      "bulletedList",
      "numberedList",
    ],
  };
});





const ccIsOpen = ref(false);
const bccIsOpen = ref(false);

const currentUserStore = useCurrentUserStore();

const audiences = ["Subscribers", "Customers", "Internal Email", "Leads", "Investors", "Partners", "Candidates"];
const selAudiences = ref<string[]>(currentUserStore.currentEmailContext?.target_audience.split(",") ?? []);
const emailLength = ref(currentUserStore.currentEmailContext?.email_length ?? 250);
const emailContextEnabled = ref(true)
const partialContext = computed<Partial<EmailContext>>(()=>{
  const miniContext = {target_audience:selAudiences.value.join(","),email_length:emailLength.value}

  return miniContext;
})


watch(partialContext,(value)=>{
  const oldContext = currentUserStore.currentEmailContext;
  const newContext = {
    ...oldContext,
    ...value
  }
  currentUserStore.storeEmailContextInfo(newContext as EmailContext)
},{deep:true})



const emailContextOpen = ref(false);

const files = ref<string[]>([]);
const showAllFiles = ref(false);

// Reactive state variables
const recipient = ref("");
const emailSubject = ref("");
const errorMessage = ref(""); // Error message variable
const recipientErrorMessage = ref(""); // Error message variable

const cc = ref("");
const bcc = ref("");
const tone = ref("Convincing");
const keyPoints = ref("");
const emailBody = ref("");
const attachments = computed<AttachmentFile[]>(() =>
  files.value.length > 0 ? files.value.map((file) => parseFilePath(file)) : []
);
const isSending = ref(false); // Flag to prevent multiple submissions when sending
const isGenerating = ref(false); // Flag to prevent multiple submissions when generating

let emailContextTest = ref<EmailContext>();

const emailContext = currentUserStore.currentEmailContext;

watch(attachments, (value) => {
  //  console.log("####", value);
});

// Handle file upload
// const handleFileUpload = (event) => {
//   const files = Array.from(event.target.files);
//   attachments.value = files;
// };

function removeFile(file: string) {
  files.value = files.value.filter((f) => f != file);
}

async function openFiles() {
  const selected = await open({
    multiple: true,
    directory: false,
    title: "Email attachments",
  });
  //  console.log("Selected Files =>", selected);
  if (Array.isArray(selected)) {
    files.value = [...new Set([...files.value, ...selected])];
    return;
  } else if (selected === null) {
    return;
  } else {
    files.value = [...new Set([...files.value, selected])];
    return;
  }
}

function cleanJsonString(jsonString: string): string {
  // Trim leading and trailing spaces
  let cleanedString = jsonString.trim();

  // Remove newline characters
  cleanedString = cleanedString.replace(/\n/g, '');

  return cleanedString;
}

// Method to generate email content
const generateEmail = async () => {
  if (isGenerating.value) return; // Prevent multiple submissions

  // Check if key points are empty
  if (!keyPoints.value.trim()) {
    errorMessage.value = "Please enter some key points about the email."; // Set error message
    return;
  }

  isGenerating.value = true; // Lock generating
  errorMessage.value = ""; // Clear error message if any

  //  console.log("Generating email with the following details:", {
  //   tone: tone.value,
  //   keyPoints: keyPoints.value,
  //   emailContext:emailContextEnabled.value
  // });

  try {
    const data = await invoke<string>("fetch_all_emails", {
      message: "generate_email",
      tone: tone.value,
      keyPoints: keyPoints.value,
      enableEmailContext:emailContextEnabled.value
    });
    const cleanedData = cleanJsonString(data)
    //  console.log(cleanedData);
    const me_data = JSON.parse(cleanedData) as GeneratedEmail;
    emailBody.value = me_data.body;
    emailSubject.value = me_data.subject;
    //  console.log(me_data);
  } catch (err) {
    console.error("Error generating email:", err);
  } finally {
    isGenerating.value = false; // Unlock generating
  }
};

// Method to send the email

// Method to send the email
const sendEmail = async () => {
  // if (isSending.value) return; // Prevent multiple submissions

  // // Check if the recipient email is empty
  // if (!recipient.value.trim()) {
  //   recipientErrorMessage.value = "Please enter the recipient's email."; // Set error message
  //   return;
  // }

  isSending.value = true; // Lock sending
  recipientErrorMessage.value = ""; // Clear error message if any

  //  console.log("Sending email with the following details:", {
  //   recipient: recipient.value,
  //   cc: cc.value,
  //   bcc: bcc.value,
  //   tone: tone.value,
  //   keyPoints: keyPoints.value,
  //   emailBody: emailBody.value,
  //   attachments: attachments.value,
  // });

  try {
    // Prepare form data for attachments
    // const formData = new FormData();
    // formData.append("recipient", recipient.value);
    // formData.append("cc", cc.value);
    // formData.append("bcc", bcc.value);
    // formData.append("tone", tone.value);
    // formData.append("keyPoints", keyPoints.value);
    // formData.append("emailBody", emailBody.value);
    // attachments.value.forEach((file) => {
    //   formData.append("attachments", file);
    // });

    // Backend API call to send email
    // Backend API call to send email
    await invoke("send_email", {
      recipient: recipient.value, //formData.get("recipient") || "",
      cc: cc.value, //formData.get("cc") || "",
      bcc: bcc.value, //formData.get("bcc") || "",
      subject: emailSubject.value, // formData.get("subject") || "",
      body: emailBody.value || "",
      attachments: attachments.value.map((a) => a.path), //Array.from(formData.getAll("attachments")) || [],
    });

    alert("Email sent successfully!");
  } catch (error) {
    console.error("Error sending email:", error);
    alert("Failed to send email.");
  } finally {
    isSending.value = false; // Unlock sending
  }
};

// // Function to handle login
// const getUserInfo = async () => {
//   try {
//     const data = await invoke("js2rs", {
//       message: "get_email_context",
//     });
//     const me_data = JSON.parse(data as string);

//     // Assign values from the returned JSON object to emailContextTest
//     emailContextTest.full_name = me_data.full_name || "";
//     emailContextTest.job_title = me_data.job_title || "";
//     emailContextTest.organization = me_data.organization || "";
//     emailContextTest.target_audience = me_data.target_audience || "";
//     emailContextTest.communication_goal = me_data.communication_goal || "";
//     emailContextTest.call_to_action = me_data.call_to_action || "";
//     emailContextTest.tone_preference = me_data.tone_preference || "";
//     emailContextTest.language_style = me_data.language_style || "";
//     emailContextTest.key_points = me_data.key_points || "";
//     emailContextTest.known_preferences = me_data.known_preferences || [];
//     emailContextTest.personal_sign_off = me_data.personal_sign_off || "";
//     emailContextTest.email_signature = me_data.email_signature || "";
//     emailContextTest.work_hours = me_data.work_hours || [];
//     emailContextTest.availability_note = me_data.availability_note || "";
//     emailContextTest.email_length_preference = me_data.email_length_preference || "";
//     emailContextTest.urgency_level = me_data.urgency_level || "";

//     //  console.log("Email Context Updated:", emailContextTest);
//   } catch (error) {
//     console.error("Error fetching email context:", error);
//   }
//  finally {
//     loading.value = false;
//   }
// };

function updateEmailContext(value:EmailContext){
  currentUserStore.storeEmailContextInfo(value)
}

// Automatically call on_login when component is mounted
onMounted(async () => {
  //  console.log(`MainLayout mounted()`);
  // await getUserInfo();
  // emailContextTest = currentUserStore.currentEmailContext;
  //  console.log("CurrentUserStore after initialization:", currentUserStore.currentEmailContext);

  //  console.log(emailContextTest);
});
</script>
<style>
.ck-content {
  height: 300px !important;
}
</style>
