CREATE TABLE email_categories (
    id VARCHAR(50) PRIMARY KEY NOT NULL,
    category_id VARCHAR(50) NOT NULL,
    child_full_domains TEXT,  -- New column to store the child full domains as JSON string
    name VA<PERSON><PERSON><PERSON>(255),
    sender_company VARCHAR(255),
    sender_domain VARCHAR(255),
    avatar_link VARCHAR(255),  -- Column for storing avatar link
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    tags VARCHAR(255),  -- Column for storing tags as a comma-separated string
    latest_email_at TIMESTAMP,  -- Timestamp of the latest email in the category
    description TEXT,  -- Column for a brief description of the category
    priority INT,  -- Column to assign a priority to the category
    color VARCHAR(7),  -- Column to store a hex color code for the category (e.g., #FFFFFF)
    unread_count INT,  -- Column to track the number of unread emails in the category
    last_accessed_at TIMESTAMP,  -- Timestamp to track when the category was last accessed
    is_archived BOOLEAN,  -- Boolean column to mark the category as archived
    custom_data TEXT,  -- Use TEXT to store <PERSON>SO<PERSON> as a string    parent_category_id VARCHAR(50),  -- Column to support hierarchical categories
    parent_category_id VARCHAR(50),
    class_category_id VARCHAR(50),
    visibility VARCHAR(50),  -- Column to control the visibility of the category (e.g., "public", "private")
    is_synced BOOLEAN  -- Boolean column to track synchronization status with external services
);



-- Insert default categories into the email_categories table
INSERT INTO email_categories (id, category_id, name, created_at, description, priority, color, tags) VALUES
    -- Finance Category
    ('1', 'finance', 'Finance', CURRENT_TIMESTAMP, 'Handles financial emails.', 1, '#FF5733', 'invoices,payments,budget,tax,expenses,accounts'),

    -- Human Resources (HR) Category
    ('2', 'hr', 'Human Resources', CURRENT_TIMESTAMP, 'Manages HR emails.', 2, '#33FF57', 'job applications,employee announcements,benefits,payroll,performance reviews,recruitment'),

    -- Marketing Category
    ('3', 'marketing', 'Marketing', CURRENT_TIMESTAMP, 'Handles marketing emails.', 3, '#3357FF', 'campaigns,social media,lead generation,marketing collateral,branding'),

    -- Customer Support Category
    ('4', 'customer_support', 'Customer Support', CURRENT_TIMESTAMP, 'Handles customer support emails.', 4, '#FFC300', 'support tickets,feedback,reviews,complaints,helpdesk,queries'),

    -- Project Updates Category
    ('5', 'project_updates', 'Project Updates', CURRENT_TIMESTAMP, 'Tracks project updates.', 5, '#42A5F5', 'progress reports,meeting summaries,milestones,task assignments,deadlines'),

    -- IT Support Category
    ('6', 'it_support', 'IT Support', CURRENT_TIMESTAMP, 'Manages IT issues.', 6, '#8E24AA', 'downtime,password resets,security alerts,software updates,technical support'),

    -- Legal Category
    ('7', 'legal', 'Legal', CURRENT_TIMESTAMP, 'Handles legal agreements.', 7, '#E53935', 'contracts,agreements,compliance,policy updates,legal notices,terms'),

    -- Operations Category
    ('8', 'operations', 'Operations', CURRENT_TIMESTAMP, 'Oversees operational emails.', 8, '#FB8C00', 'supply chain,logistics,shipping,workflow,process updates,delivery'),

    -- Education and Training Category
    ('9', 'education_training', 'Education & Training', CURRENT_TIMESTAMP, 'Handles training resources.', 9, '#4CAF50', 'training invitations,online courses,certifications,workshops,webinars'),

    -- Social and Community Category
    ('10', 'social_community', 'Social & Community', CURRENT_TIMESTAMP, 'Manages social and community emails.', 10, '#AB47BC', 'team events,community updates,social activities,networking'),

    -- Newsletters Category
    ('11', 'newsletters', 'Newsletters', CURRENT_TIMESTAMP, 'Collects subscribed newsletters.', 11, '#29B6F6', 'industry updates,company newsletters,blog posts,subscriptions'),

    -- Alerts and Notifications Category
    ('12', 'alerts_notifications', 'Alerts & Notifications', CURRENT_TIMESTAMP, 'Tracks alerts and notifications.', 12, '#FF7043', 'alerts,notifications,reminders,deadlines,system updates'),

    -- Personal and Administrative Category
    ('13', 'personal_administrative', 'Personal & Administrative', CURRENT_TIMESTAMP, 'Handles personal and admin emails.', 13, '#78909C', 'travel approvals,personal invitations,admin tasks,expense approvals'),

    -- Custom Category
    ('14', 'custom', 'Custom', CURRENT_TIMESTAMP, 'Placeholder for user-defined categories.', 14, '#A9A9A9', ''),

     -- Needs Reply
    ('15', 'needs_reply', 'Needs Reply', CURRENT_TIMESTAMP, 'Emails that require a reply or further information.', 15, '#FF6F61', 'follow-up,waiting for info,urgent reply,please respond'),

    -- Waiting Response
    ('16', 'waiting_response', 'Waiting Response', CURRENT_TIMESTAMP, 'You are waiting for a reply from others.', 16, '#FFD54F', 'awaiting response,response pending,follow up needed'),

    -- FYI Read Later
    ('17', 'fyi_read_later', 'FYI / Read Later', CURRENT_TIMESTAMP, 'Informational emails to read later.', 17, '#90CAF9', 'just FYI,read when free,no action required'),

    -- Delegated / Handled
    ('18', 'delegated_handled', 'Delegated / Handled', CURRENT_TIMESTAMP, 'Handled by a delegate or assistant.', 18, '#A5D6A7', 'forwarded,assigned to,handled by team'),

    -- Calendar Scheduling
    ('19', 'calendar_scheduling', 'Calendar Scheduling', CURRENT_TIMESTAMP, 'Related to meeting coordination.', 19, '#4DB6AC', 'schedule meeting,calendar,book time,find slot'),

    -- Clients / VIPs
    ('20', 'clients_vips', 'Clients / VIPs', CURRENT_TIMESTAMP, 'From VIPs or important clients.', 20, '#BA68C8', 'client message,vip,partner,important'),

    -- Ads & Newsletters
    ('21', 'ads_newsletters', 'Ads & Newsletters', CURRENT_TIMESTAMP, 'Promotional or newsletter emails.', 21, '#CE93D8', 'unsubscribe,promo,offer,sale,newsletter,subscription');
    