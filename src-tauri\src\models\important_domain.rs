use crate::schema::important_domains::{self};
use chrono::NaiveDateTime;
use diesel::prelude::Identifiable;
use diesel::{Insertable, Queryable};
use serde::{Deserialize, Serialize};
use std::clone::Clone; // This line is optional because <PERSON>lone is part of the Rust prelude

#[derive(Insertable, Queryable, Identifiable, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = important_domains)]
pub struct ImportantDomain {
    pub id: String,
    pub domain: String,
    pub category: String, // "vip" or "team"
    pub name: Option<String>,
    pub avatar_link: Option<String>,
    pub created_at: Option<NaiveDateTime>, // ✅ FIXED: Make this optional
}
