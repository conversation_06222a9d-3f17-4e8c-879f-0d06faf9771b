<template>
  <div class="size-full overflow-hidden relative">
    <div class="size-full relative overflow-hiddenx" ref="drawing"></div>
    <EventCard v-for="event of events" :key="event.label" :event="event" />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { Circle, Path, Svg, SVG } from "@svgdotjs/svg.js";
import EventCard from "./EventCard.vue";
import { amplitude, endStraight, marginLabel, startStraight, startX, yMid } from "./params";

const events = ref([
  { label: "midnight", time: "00:00" },
  { label: "Breakfast", time: "07:30" },
  { label: "Meeting", time: "09:45" },
  { label: "Lunch", time: "12:00" },
  { label: "Gym", time: "18:00" },
  { label: "Bedtime", time: "23:00" },
]);

watch(events, (value) => {
  //  console.log("Events", value);
});

const drawing = ref();
const draw = ref<Svg>(SVG());

// Params
const waveWidth = computed(() => (drawing.value.offsetWidth - startStraight - endStraight) / events.value.length);

let pathString = ` M ${startX},${yMid} L ${startX + startStraight},${yMid}`;
let hourLabels: any[] = []; // will store label positions for each hour

const path = ref<Path>();
const timeCircle = ref<Circle>();

const totalLength = computed(() =>
  path.value && path.value.length ? path.value.length() - startStraight - endStraight : 0
);

const curveLength = computed(() => (totalLength.value - startStraight - endStraight) / 24);

function timeToMinutes(timeStr: string) {
  const [hh, mm] = timeStr.split(":");
  return parseInt(hh, 10) * 60 + parseInt(mm, 10);
}

// function drawCurvedLine() {
//   let xPos = startX + startStraight;
//   let directionUp = true;
//   for (let hour = 0; hour < 24; hour++) {
//     const xEnd = xPos + waveWidth;
//     const cp1x = xPos + waveWidth / 4;
//     const cp2x = xPos + (3 * waveWidth) / 4;
//     const cpY = directionUp ? yMid - amplitude : yMid + amplitude;
//     pathString += ` C ${cp1x} ${cpY}, ${cp2x} ${cpY}, ${xEnd} ${yMid}`;
//     const labelX = xPos + waveWidth / 2;
//     const labelY = directionUp ? yMid - amplitude - marginLabel + 50 : yMid + amplitude + marginLabel - 50;
//     const hourStr = hour.toString().padStart(2, "0") + ":00";
//     hourLabels.push({ time: hourStr, x: labelX, y: labelY });

//     xPos += waveWidth;
//     directionUp = !directionUp;
//   }

//   // 3. Append a straight line at the end.
//   pathString += ` L ${xPos + endStraight} ${yMid}`;
//   const totalWidth = xPos + endStraight + startX;
//   draw.value = SVG().addTo(drawing.value).size(totalWidth, "100%");

//   path.value = draw.value.path(pathString).fill("none").stroke({ color: "#C47D65", width: 4, linecap: "round" });
// }

function drawCurvedLine() {
  let xPos = startX + startStraight;
  let directionUp = true;
  //  console.log("wave width", waveWidth.value);

  events.value = events.value.map((evt) => {
    const xEnd = xPos + waveWidth.value;
    const cp1x = xPos + waveWidth.value / 4;
    const cp2x = xPos + (3 * waveWidth.value) / 4;
    const cpY = directionUp ? yMid - amplitude : yMid + amplitude;
    pathString += ` C ${cp1x} ${cpY}, ${cp2x} ${cpY}, ${xEnd} ${yMid}`;
    const labelX = xPos + waveWidth.value / 2;
    const labelY = directionUp ? yMid - amplitude - marginLabel + 60 : yMid + amplitude + marginLabel - 60;
    const minutes = timeToMinutes(evt.time);
    const time = {
      hour: Math.trunc(minutes / 60),
      minutes: minutes % 60,
    };
    const hourStr = time.hour.toString().padStart(2, "0") + ":" + time.minutes.toString().padStart(2, "0");
    hourLabels.push({ time: hourStr, x: labelX, y: labelY });

    xPos += waveWidth.value;
    directionUp = !directionUp;

    return {
      ...evt,
      pos: {
        x: labelX,
        y: labelY,
      },
    };
  });

  // 3. Append a straight line at the end.
  pathString += ` L ${xPos + endStraight} ${yMid}`;
  const totalWidth = xPos + endStraight + startX;
  draw.value = SVG().addTo(drawing.value).size(totalWidth, "100%");

  path.value = draw.value.path(pathString).fill("none").stroke({ color: "#C47D65", width: 4, linecap: "round" });
  drawHours();
}

function drawHours() {
  const lineWidth = 40;
  hourLabels.forEach((label) => {
    //  console.log("Hours =>", label);
    draw.value
      .text(label.time)
      .fill("#CE7F80")
      .font({ size: 15, family: "Arial", weight: "bold" })
      .center(label.x, label.y + (label.y > yMid ? marginLabel : -marginLabel));
    if (path.value) {
      const lineY1 = label.y > yMid ? label.y - 10 - lineWidth / 2 : label.y + 10 - lineWidth / 2;
      const lineY2 = label.y > yMid ? label.y - 10 + lineWidth / 2 : label.y + 10 + lineWidth / 2;
      draw.value.line(label.x, lineY1, label.x, lineY2).stroke({ color: "#C47D65", width: 2 });
      // draw.value.circle(20).fill("#C47D65").center(label.x, label.y);
    }
  });
}

function updateCurrentTime() {
  if (path.value && timeCircle.value) {
    const now = new Date();
    //  console.log("Time => ", now);
    const nowMinutes = now.getHours() * 60 + now.getMinutes(); // 0..1440
    const fraction = nowMinutes / 1440;
    // Map current time onto the curved section (offset by the starting straight line).
    const dist = startStraight + fraction * totalLength.value + curveLength.value / 2;
    const pt = path.value.node.getPointAtLength(dist);
    timeCircle.value.center(pt.x, pt.y);
  }
}

function drawEvents() {
  let distByMinute = totalLength.value / 1440;
  // Map event times only onto the curved section.
  const curvedLength = 24 * waveWidth.value; // horizontal extent of curved segments

  events.value = events.value.map((evt) => {
    const evtMinutes = timeToMinutes(evt.time); // 0 ... 1440
    const fraction = evtMinutes / 1440;
    // Offset the distance by the starting straight segment.
    const dist = startStraight + totalLength.value * fraction + curveLength.value / 2; //startStraight + fraction * curvedLength;
    const pt = path.value?.node.getPointAtLength(dist);
    // Draw a marker.
    if (pt) {
      draw.value.line(pt.x, pt.y - 15, pt.x, pt.y + 15).stroke({ color: "#C47D65", width: 2 });
      draw.value
        .text(evt.label)
        .font({ size: 14, family: "Arial" })
        .center(pt.x, pt.y - 35);

      function isBetween(value: number, x1: number, x2: number): boolean {
        return value >= x1 && value <= x2;
      }

      //  console.log("x-->", Math.abs(amplitude - pt.y));

      const offset = isBetween(Math.abs(amplitude - pt.y) - 20, 60, 140)
        ? Math.trunc(evtMinutes / 60) % 2 == 0
          ? 1
          : -1
        : 0;
      return {
        ...evt,
        pos: {
          x: pt.x,
          y: pt.y,
          offset,
        },
      };
    }
    return {
      ...evt,
    };
  });
}

onMounted(() => {
  drawCurvedLine();
  // drawHours();
  // drawEvents();

  //  console.log("Drawing width", drawing.value.offsetWidth);

  timeCircle.value = draw.value
    .circle(14)
    .fill("#C47D65")
    .center(startX + startStraight, yMid);

  updateCurrentTime();
  setInterval(updateCurrentTime, 60_000);
});
</script>

<style scoped></style>
