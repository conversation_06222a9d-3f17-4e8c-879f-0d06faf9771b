<template>
  <div ref="clockEl"></div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { SVG } from "@svgdotjs/svg.js";
import { parseTimeString } from "../../../utils/utils";

const props = defineProps({
  time: { type: String, required: true },
  theme: { type: String, default: "dark" },
});

const clockEl = ref();

function drawClock(centerX: number, centerY: number, radius: number, hour: number, minute: number, second: number) {
  clockEl.value.innerHTML = "";

  const draw = SVG()
    .addTo(clockEl.value)
    .size(centerX * 2, centerY * 2);

  draw
    .circle(radius * 2)
    .center(centerX, centerY)
    .fill(props.theme === "light" ? "#fff" : "#2C4B46");

  // Draw hour numbers dynamically
  for (let i = 1; i <= 12; i++) {
    let angle = (i * 30 - 90) * (Math.PI / 180);
    let numX = centerX + Math.cos(angle) * (radius * 0.75);
    let numY = centerY + Math.sin(angle) * (radius * 0.75);
    draw
      .text(i.toString())
      .move(numX, numY - radius * 0.45)
      .fill(props.theme === "light" ? "#2C4B46" : "white")
      .font({ size: Math.max(radius * 0.2, 8), anchor: "middle" });
  }

  function drawHand(length: number, width: number, color: string, angle: number) {
    let x = centerX + Math.cos(((angle - 90) * Math.PI) / 180) * length;
    let y = centerY + Math.sin(((angle - 90) * Math.PI) / 180) * length;
    return draw.line(centerX, centerY, x, y).stroke({ width: width, color: color }).attr("stroke-linecap", "round");
  }

  // Calculate hand angles
  let hourAngle = (hour % 12) * 30 + minute * 0.5; // Each hour = 30 degrees, each minute moves it by 0.5 degrees
  let minuteAngle = minute * 6; // Each minute = 6 degrees
  let secondAngle = second * 6; // Each second = 6 degrees

  // Draw hands
  drawHand(radius * 0.45, 2, props.theme === "light" ? "#2C4B46" : "white", hourAngle); // Hour hand
  drawHand(radius * 0.65, 2, props.theme === "light" ? "#2C4B46" : "white", minuteAngle); // Minute hand
  drawHand(radius * 0.8, 1, "red", secondAngle); // Second hand
}

onMounted(() => {
  // Example Usage
  const time = parseTimeString(props.time);
  drawClock(30, 30, 30, time.getHours(), time.getMinutes(), time.getSeconds());
});
</script>

<style scoped></style>
