use crate::google_auth::get_token::get_token;
use crate::models::cal_user::CalUser;
use chrono::{ DateTime, Utc };
use diesel::expression::is_aggregate::No;
use home::home_dir;
use oauth2::{
    basic::BasicClient,
    reqwest::async_http_client,
    revocation::StandardRevocableToken,
    AccessToken,
    AuthUrl,
    AuthorizationCode,
    ClientId,
    ClientSecret,
    CsrfToken,
    EmptyExtraTokenFields,
    PkceCodeChallenge,
    RedirectUrl,
    RefreshToken,
    RevocationUrl,
    Scope,
    StandardTokenResponse,
    TokenResponse,
    TokenUrl,
};
use serde_json::json;
use std::fs;
use std::fs::read_to_string;
use std::time::{ Duration, Instant };
use tracing::{ error, info, warn };

use crate::models::phone_context::PhoneCallContext;
use crate::models::user_perference::EmailContext;

use super::{ email, phone_context };

pub const MAIN_DATA_FILENAME: &str = r#".tauri_oauth"#;

#[derive(serde::Serialize, serde::Deserialize, Debug, Clone, Default)]
/// # User
/// struct for the google user data
pub struct User {
    pub id: String,
    pub email: String,
    pub verified_email: bool,
    pub name: String,
    pub given_name: String,
    pub family_name: Option<String>,
    pub picture: Option<String>,
    // pub locale: String,
}

#[derive(serde::Serialize, serde::Deserialize, Debug, Clone, Default)]
pub struct UserCredentials {
    google_id: String,
    email: String,
    name: String,
    access_token: Option<String>,
    refresh_token: Option<String>, // Make it optional,
    token_expiry: Option<String>, // Make it optional
    watch_expiration: Option<i64>, // in ms
    history_id: Option<String>,
}

impl UserCredentials {
    pub fn new() -> Self {
        UserCredentials {
            google_id: "".to_string(),
            email: "".to_string(),
            name: "".to_string(),
            access_token: Some("".to_string()),
            refresh_token: Some("".to_string()),
            token_expiry: Some("".to_string()),
            watch_expiration: Some(0),
            history_id: Some("".to_string()),
        }
    }
}

impl User {
    pub fn new(
        id: String,
        email: String,
        verified_email: bool,
        name: String,
        given_name: String,
        family_name: Option<String>,
        picture: Option<String>
    ) -> Self {
        User {
            id,
            email,
            verified_email,
            name,
            given_name,
            family_name,
            picture,
        }
    }
}

#[derive(serde::Serialize, serde::Deserialize, Debug, Clone, Default)]
pub struct UserData {
    pub user: User,
    pub cal_info: Option<CalUser>,
    pub refresh_token: Option<oauth2::RefreshToken>,
    pub expire_in: Option<Duration>,
    pub access_token: Option<oauth2::AccessToken>,
    pub issued_at: Option<DateTime<Utc>>, // Add this field
    pub email_context: Option<EmailContext>,
    pub phone_context: Option<PhoneCallContext>,
    pub watch_expiration: Option<i64>, // in ms
    pub history_id: Option<String>,
}

impl UserData {
    ///constructor from user_data as clone()
    pub fn new(
        user_data: &User,
        refresh_token: Option<RefreshToken>,
        expire_in: Option<Duration>,
        access_token: Option<AccessToken>
    ) -> Self {
        info!("UserData new()");

        UserData {
            user: user_data.clone(),
            cal_info: None,
            refresh_token: None,
            expire_in: None,
            access_token: None,
            issued_at: None,
            email_context: Some(EmailContext::default_profile()),
            phone_context: Some(PhoneCallContext::default_context()),
            watch_expiration: None,
            history_id: None,
        }
    }

    ///consturctor from file
    pub async fn init_user_data() -> Self {
        info!("UserData init_main_data()");

        let home_dir = home_dir().unwrap_or("".into());

        let file_and_path = format!(
            "{}/{}",
            home_dir.to_str().unwrap_or("").to_string(),
            MAIN_DATA_FILENAME
        );
        println!("file_and_path: {}", file_and_path);
        let user_data_string = read_to_string(file_and_path.clone()).unwrap_or("".to_string());
        // info!("user_data: {:#?}", user_data_string.clone());
        // info!("user_data: {:#?}", file_and_path.clone());

        let user_data = match serde_json::from_str::<UserData>(&user_data_string) {
            Ok(mut result) => {
                match result.cal_info {
                    Some(ref _rs) => result.clone(),
                    None => {
                        eprint!("undefined cal_info,trying to get it ....");
                        let cal_info = CalUser::get_user_by_email(&&result.user.email).await;
                        result.set_cal_info(cal_info);
                        result
                    }
                }
            }
            Err(err) => {
                warn!(?err, "warn: ");
                UserData {
                    user: User::new(
                        "".to_string(),
                        "".to_string(),
                        true,
                        "".to_string(),
                        "".to_string(),
                        Some("".to_string()),
                        Some("".to_string())
                    ),
                    cal_info: None,
                    refresh_token: None,
                    expire_in: None,
                    access_token: None,
                    issued_at: None,
                    email_context: Some(EmailContext::default_profile()),
                    phone_context: Some(PhoneCallContext::default_context()),
                    watch_expiration: None,
                    history_id: None,
                }
            }
        };
        // info!("user_data: {:#?}", user_data);
        return user_data;
    }

    ///set and save the main_data
    ///
    pub fn set_watch_expiration(&mut self, watch_expiration: Option<i64>) {
        self.watch_expiration = watch_expiration.clone();
        self.save_me();
    }

    pub fn set_history_id(&mut self, history_id: Option<String>) {
        self.history_id = history_id.clone();
        self.save_me();
    }

    pub fn set_email_context(&mut self, email_context: Option<EmailContext>) {
        self.email_context = email_context.clone();
        info!("user_data: {:#?}", email_context);

        self.save_me();
    }
    pub fn set_phone_context(&mut self, phone_context: Option<PhoneCallContext>) {
        self.phone_context = phone_context.clone();
        self.save_me();
    }
    pub fn set(&mut self, email: String, name: String) {
        self.user.email = email;
        self.user.name = name;
        self.save_me();
    }

    pub fn set_token(&mut self, refresh_token: Option<oauth2::RefreshToken>) {
        self.refresh_token = refresh_token.clone();
        self.save_me();
    }

    pub fn set_access_token(&mut self, access_token: Option<oauth2::AccessToken>) {
        self.access_token = access_token.clone();
        self.save_me();
    }

    pub fn set_exprie_in(&mut self, expire_in: Option<Duration>) {
        self.expire_in = expire_in.clone();
        self.save_me();
    }

    pub fn set_issued_at(&mut self) {
        self.issued_at = Some(Utc::now());
        self.save_me();
    }

    pub fn set_cal_info(&mut self, cal_info: Option<CalUser>) {
        self.cal_info = cal_info;
        self.save_me();
    }

    ///save refresh token from UserData in file
    pub fn save_me(&self) {
        info!("UserData save_me()");

        let home_dir = home_dir().unwrap_or("".into());

        let file_and_path = format!(
            "{}/{}",
            home_dir.to_str().unwrap_or("").to_string(),
            MAIN_DATA_FILENAME
        );

        // Serialize UserData into JSON
        let main_data_json = match self.to_json() {
            Ok(json) => json,
            Err(err) => {
                error!(?err, "Error serializing UserData to JSON.");
                return;
            }
        };
        match fs::write(file_and_path, main_data_json) {
            Ok(_) => {}
            Err(err) => {
                error!(?err, "Error: ");
            }
        };
    }

    /// Serialize `UserData` into a JSON string
    pub fn to_json(&self) -> Result<String, serde_json::Error> {
        // Use `to_json` from `email_context` and `phone_context` if available
        let email_context_json = self.email_context
            .as_ref()
            .map(|context| context.to_json())
            .unwrap_or_default();
        let phone_context_json = self.phone_context
            .as_ref()
            .map(|context| context.to_json())
            .unwrap_or_default();

        // Merge into the main structure and serialize
        let mut data = json!(self);
        if !email_context_json.is_null() {
            data["email_context"] = serde_json::from_value(email_context_json).unwrap_or(json!({}));
        }
        if !phone_context_json.is_null() {
            data["phone_context"] = serde_json::from_value(phone_context_json).unwrap_or(json!({}));
        }

        serde_json::to_string(&data)
    }

    /// ## log_in()
    /// call ``get_token()`` OAuth and then get the userdate <br>
    /// ``bool`` Return value returns ``logged_in`` if the OAuth
    /// was successful and supplied an access token.
    /// In addition, this token is then used to retrieve the
    /// user data is retrieved.
    /// Only if both actions were successful ``true`` is returned.
    /// is returned.
    pub async fn log_in(&mut self, handle: &tauri::AppHandle) -> bool {
        let l_do: i32 = 'block: {
            let (l_access_token, l_refresh_token, l_expires_in) = match
                get_token(handle, self.user.email.clone(), self.refresh_token.clone()).await
            {
                Ok(token) => token,
                Err(e) => {
                    error!("error - Access token could not be retrieved {}", e);

                    self.user.name = "".to_string();
                    self.user.email = "".to_string();
                    self.refresh_token = None;

                    self.save_me();

                    return false;
                }
            };

            let url = format!(
                "https://www.googleapis.com/oauth2/v1/userinfo?alt=json&access_token={:?}",
                l_access_token.clone().unwrap().secret()
            );

            info!(?l_expires_in, "this my expire time");

            let resp = match reqwest::get(url).await {
                Ok(res) =>
                    match res.text().await {
                        Ok(res_text) => res_text,
                        Err(e) => {
                            error!("error - userinfo could not be retrieved {}", e);
                            return false;
                        }
                    }
                Err(e) => {
                    error!("error - userinfo could not be retrieved {}", e);
                    return false;
                }
            };

            let userinfo: User = match serde_json::from_str(&resp) {
                Ok(result) => {
                    println!("Successfully deserialized user info: {:?}", result);

                    // info!(?result, "this my result after deserilzation");
                    result
                }
                Err(e) => {
                    info!(?e, "this my result after deserilzation");
                    User {
                        id: "".to_string(),
                        email: "".to_string(),
                        verified_email: false,
                        name: "".to_string(),
                        given_name: "".to_string(),
                        family_name: Some("".to_string()),
                        picture: Some("".to_string()),
                        // locale: "".to_string(),
                    }
                }
            };

            let token_expiry_str = l_expires_in.map(|duration| {
                (Utc::now() + chrono::Duration::from_std(duration).unwrap()).to_rfc3339()
            });
            let refresh_token_str = l_refresh_token.clone().map(|token| token.secret().to_string());
            let access_token_str = l_access_token.clone().map(|token| token.secret().to_string());
            let watch_expiration = None; // Set this to the appropriate value if needed
            let history_id = None; // Set this to the appropriate value if needed
            let user_credentials = UserCredentials {
                google_id: userinfo.id.clone().to_string(),
                email: userinfo.clone().email.to_string(),
                name: userinfo.clone().name.to_string(),
                access_token: access_token_str.clone(),
                refresh_token: refresh_token_str.clone(),
                token_expiry: token_expiry_str,
                watch_expiration: watch_expiration,
                history_id: history_id,
            };

            // let client = reqwest::Client::new();
            // // Convert the struct to a JSON string
            let user_credentials_json =
                json!({
                "google_id": user_credentials.google_id.to_string(),
                "email": user_credentials.email.clone(),
                "name": user_credentials.name.clone(),
                "username": user_credentials.email.split('@').next().unwrap_or("unknown").to_string(),
                "access_token": Some(user_credentials.access_token.clone()),
                "refresh_token": Some(user_credentials.refresh_token.clone()),
                "token_expiry": Some(user_credentials.token_expiry.clone()),
                "watch_expiration": user_credentials.watch_expiration.clone(),
                "history_id": user_credentials.history_id.clone(),
            });

            // Clone necessary values to avoid move errors
            let access_token_clone = user_credentials.access_token.clone();
            let refresh_token_clone = user_credentials.refresh_token.clone();
            let token_expiry_clone = user_credentials.token_expiry.clone();
            // Extract the email provider
            let email_provider = user_credentials.email
                .split('@') // Split the email by '@'
                .nth(1) // Get the domain part (e.g., "example.com")
                .unwrap_or("unknown") // Fallback to "unknown" if no '@' found
                .split('.') // Split the domain by '.' (e.g., "example" in "example.com")
                .next() // Get the first part (e.g., "example")
                .unwrap_or("unknown") // Fallback to "unknown" if no '.' found
                .to_string(); // Convert to String

            let url = "http://data.oway.life:8080/api/users/manage_user_and_email/";
            // Assume `user_credentials` is already defined
            let payload =
                json!({
                "email": user_credentials.email.clone(),
                "name": user_credentials.name.clone(),
                "phone_number": "123456789", // Replace with user_credentials.phone_number if available
                "email_provider": email_provider.clone(), // Use the extracted email provider
                "access_token": user_credentials.access_token.clone(),
                "refresh_token": user_credentials.refresh_token.clone(),
                "token_expiry": user_credentials.token_expiry.clone(),
            });
            println!("{}", payload);

            let client = reqwest::Client::new();
            match client.post(url).json(&payload).send().await {
                Ok(response) => {
                    if response.status().is_success() {
                        true;
                    } else {
                        eprintln!(
                            "{}",
                            format!(
                                "Failed to store user credentials, status: {}",
                                response.status()
                            )
                        );
                        false;
                    }
                }
                Err(err) => {
                    eprintln!("{}", format!("Failed to send request: {}", err));
                    false;
                }
            }

            self.user = userinfo;

            info!(?self.user, "user information after assigning ....");

            if self.user.email.is_empty() {
                //if invalide email clear token
                self.set_token(None);
                self.set_access_token(None);
                self.set_exprie_in(None);
                self.set_issued_at();
                self.set_email_context(None);
                self.set_phone_context(None);
                self.save_me();

                return false;
            }
            info!(?self.user, "user information after assigning ....");
            match CalUser::get_user_by_email(&self.user.email).await {
                Some(info) => {
                    println!("get user by email {}", info.id);
                    self.cal_info = Some(info);
                }
                None => {
                    let user_name = if self.user.given_name.is_empty() {
                        String::from("unknown")
                    } else {
                        format!(
                            "{} {}",
                            self.user.given_name,
                            self.user.family_name.clone().unwrap_or("".to_string())
                        )
                            .trim()
                            .to_string()
                    };
                    match CalUser::create_user(user_name, self.user.email.clone()).await {
                        Some(info) => {
                            println!("user created {}", info.id);
                            self.cal_info = Some(info);
                        }
                        None => {
                            error!("error - user could not be created");
                            return false;
                        }
                    }
                    return false;
                }
            }

            self.save_me();

            if l_refresh_token.is_some() {
                info!("refresh_token found");
                self.set_token(l_refresh_token);

                // return true;
            }
            if l_access_token.is_some() {
                info!("l_access_token found");

                self.set_access_token(l_access_token);

                // return true;
            }
            if l_expires_in.is_some() {
                info!("l_access_token found");

                self.set_exprie_in(l_expires_in);
                self.set_issued_at();
                return true;
            }
            return false;
            99
        };

        false
    }

    pub async fn log_in_new_email(&mut self, handle: &tauri::AppHandle) -> bool {
        let l_do: i32 = 'block: {
            let (l_access_token, l_refresh_token, l_expires_in) = match
                get_token(handle, self.user.email.clone(), self.refresh_token.clone()).await
            {
                Ok(token) => token,
                Err(e) => {
                    error!("error - Access token could not be retrieved {}", e);

                    self.user.name = "".to_string();
                    self.user.email = "".to_string();
                    self.refresh_token = None;

                    self.save_me();

                    return false;
                }
            };

            let url = format!(
                "https://www.googleapis.com/oauth2/v1/userinfo?alt=json&access_token={:?}",
                l_access_token.clone().unwrap().secret()
            );

            info!(?l_expires_in, "this my expire time");

            let resp = match reqwest::get(url).await {
                Ok(res) =>
                    match res.text().await {
                        Ok(res_text) => res_text,
                        Err(e) => {
                            error!("error - userinfo could not be retrieved {}", e);
                            return false;
                        }
                    }
                Err(e) => {
                    error!("error - userinfo could not be retrieved {}", e);
                    return false;
                }
            };

            let userinfo: User = match serde_json::from_str(&resp) {
                Ok(result) => {
                    println!("Successfully deserialized user info: {:?}", result);

                    // info!(?result, "this my result after deserilzation");
                    result
                }
                Err(e) => {
                    info!(?e, "this my result after deserilzation");
                    User {
                        id: "".to_string(),
                        email: "".to_string(),
                        verified_email: false,
                        name: "".to_string(),
                        given_name: "".to_string(),
                        family_name: Some("".to_string()),
                        picture: Some("".to_string()),
                        // locale: "".to_string(),
                    }
                }
            };

            let token_expiry_str = l_expires_in.map(|duration| {
                (Utc::now() + chrono::Duration::from_std(duration).unwrap()).to_rfc3339()
            });
            let refresh_token_str = l_refresh_token.clone().map(|token| token.secret().to_string());
            let access_token_str = l_access_token.clone().map(|token| token.secret().to_string());
            let watch_expiration = None; // Set this to the appropriate value if needed
            let history_id = None; // Set this to the appropriate value if needed

            let user_credentials = UserCredentials {
                google_id: userinfo.id.clone().to_string(),
                email: userinfo.clone().email.to_string(),
                name: userinfo.clone().name.to_string(),
                access_token: access_token_str.clone(),
                refresh_token: refresh_token_str.clone(),
                token_expiry: token_expiry_str,
                watch_expiration: watch_expiration,
                history_id: history_id,
            };

            // let client = reqwest::Client::new();
            // // Convert the struct to a JSON string
            let user_credentials_json =
                json!({
                "google_id": user_credentials.google_id.to_string(),
                "email": user_credentials.email.clone(),
                "name": user_credentials.name.clone(),
                "username": user_credentials.email.split('@').next().unwrap_or("unknown").to_string(),
                "access_token": Some(user_credentials.access_token.clone()),
                "refresh_token": Some(user_credentials.refresh_token.clone()),
                "token_expiry": Some(user_credentials.token_expiry.clone())
            });

            // Clone necessary values to avoid move errors
            let access_token_clone = user_credentials.access_token.clone();
            let refresh_token_clone = user_credentials.refresh_token.clone();
            let token_expiry_clone = user_credentials.token_expiry.clone();
            // Extract the email provider
            let email_provider = user_credentials.email
                .split('@') // Split the email by '@'
                .nth(1) // Get the domain part (e.g., "example.com")
                .unwrap_or("unknown") // Fallback to "unknown" if no '@' found
                .split('.') // Split the domain by '.' (e.g., "example" in "example.com")
                .next() // Get the first part (e.g., "example")
                .unwrap_or("unknown") // Fallback to "unknown" if no '.' found
                .to_string(); // Convert to String
            let url = "http://localhost:9000/api/users/manage_user_and_email/";
            // Assume `user_credentials` is already defined
            let payload =
                json!({
                "email": user_credentials.email.clone(),
                "name": user_credentials.name.clone(),
                "phone_number": "123456789", // Replace with user_credentials.phone_number if available
                "email_provider": email_provider.clone(), // Use the extracted email provider
                "access_token": user_credentials.access_token.clone(),
                "refresh_token": user_credentials.refresh_token.clone(),
                "token_expiry": user_credentials.token_expiry.clone(),
            });
            println!("{}", payload);

            let client = reqwest::Client::new();
            match client.post(url).json(&payload).send().await {
                Ok(response) => {
                    if response.status().is_success() {
                        true;
                    } else {
                        eprintln!(
                            "{}",
                            format!(
                                "Failed to store user credentials, status: {}",
                                response.status()
                            )
                        );
                        false;
                    }
                }
                Err(err) => {
                    eprintln!("{}", format!("Failed to send request: {}", err));
                    false;
                }
            }

            self.user = userinfo;

            info!(?self.user, "user information after assigning ....");

            if self.user.email.is_empty() {
                //if invalide email clear token
                self.set_token(None);
                self.set_access_token(None);
                self.set_exprie_in(None);
                self.set_issued_at();
                self.set_email_context(None);
                self.set_phone_context(None);
                self.save_me();
                return false;
            }

            info!(?self.user, "user information after assigning ....");
            match CalUser::get_user_by_email(&self.user.email).await {
                Some(info) => {
                    println!("get user by email {}", info.id);
                    self.cal_info = Some(info);
                }
                None => {
                    let user_name = if self.user.given_name.is_empty() {
                        String::from("unknown")
                    } else {
                        format!(
                            "{} {}",
                            self.user.given_name,
                            self.user.family_name.clone().unwrap_or("".to_string())
                        )
                            .trim()
                            .to_string()
                    };
                    match CalUser::create_user(user_name, self.user.email.clone()).await {
                        Some(info) => {
                            println!("user created {}", info.id);
                            self.cal_info = Some(info);
                        }
                        None => {
                            error!("error - user could not be created");
                            return false;
                        }
                    }
                    return false;
                }
            }

            self.save_me();

            if l_refresh_token.is_some() {
                info!("refresh_token found");
                self.set_token(l_refresh_token);

                // return true;
            }
            if l_access_token.is_some() {
                info!("l_access_token found");

                self.set_access_token(l_access_token);

                // return true;
            }

            if l_expires_in.is_some() {
                info!("l_access_token found");

                self.set_exprie_in(l_expires_in);
                self.set_issued_at();
                return true;
            }
            return false;
            99
        };

        false
    }
}
