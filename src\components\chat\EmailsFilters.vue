<template>
  <div>
    <div class="w-full flex justify-start items-center">
      <button class="bg-primary-400/30 text-secondary-800 px-1 rounded" @click="isOpen = !isOpen">
        <div class="flex justify-start items-center gap-1">
          <FunnelIcon class="size-4" />
          {{ selectedFilter }}
        </div>
      </button>
    </div>
    <div class="absolute top-8 bg-primary-100 w-72 max-h-72 z-[999999] overflow-hiddenx" v-if="isOpen">
      <div class="w-full px-0 h-10">
        <input
          type="text"
          class="w-full bg-primary-200/40 border-none"
          placeholder="type to search..."
          v-model="searchValue"
        />
      </div>
      <div class="max-h-[calc(288px-40px)] w-full p-1 overflow-y-auto custom-scrollbar">
        <div class="w-full flex flex-col gap-1 overflow-auto">
          <div
            v-for="filter of filtersArray"
            class="flex gap-1 items-center w-full px-2 py-0.5 drop-shadow-sm text-sm rounded-md cursor-pointer transition-all duration-200"
            :class="filter == props.selectedFilter ? 'bg-indigo-300' : 'bg-primary-300/40 hover:bg-secondary-400/40'"
            @click="
              selectedFilter = filter;
              isOpen = false;
            "
          >
            <HashtagIcon class="size-4" />
            <div>{{ filter }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { FunnelIcon, HashtagIcon } from "@heroicons/vue/24/outline";
import { ref, watch } from "vue";

const props = defineProps({
  filters: {
    type: Object,
    required: true,
  },
  selectedFilter: String,
});

const emit = defineEmits(["onSelect"]);

const filtersArray = ref<string[]>(props.filters as string[]);
const selectedFilter = ref<string>(props.selectedFilter || "");
const searchValue = ref("");
const isOpen = ref(false);

watch(selectedFilter, (value) => {
  emit("onSelect", value);
});

watch(searchValue, (value) => {
  search(value);
});

function search(value: string) {
  if (value) {
    filtersArray.value = (props.filters as string[]).filter((s) => s.toLowerCase().includes(value.toLowerCase()));
  } else filtersArray.value = props.filters as string[];
}
</script>

<style scoped></style>
