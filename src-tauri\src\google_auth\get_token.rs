use dotenv::dotenv;
use oauth2::{
    basic::BasicClient,
    reqwest::async_http_client,
    revocation::StandardRevocableToken,
    AccessToken,
    AuthUrl,
    AuthorizationCode,
    ClientId,
    ClientSecret,
    CsrfToken,
    EmptyExtraTokenFields,
    PkceCodeChallenge,
    RedirectUrl,
    RefreshToken,
    RevocationUrl,
    Scope,
    StandardTokenResponse,
    TokenResponse,
    TokenUrl,
};
use std::io::{ BufRead, BufReader, Write };
use std::time::{ Duration, Instant };
use tauri::{ Manager, RunEvent, WindowEvent };
use tracing::{ error, info, warn };

/// # get_token
/// Function to determine the access token for access to gmail
///
/// https://developers.google.com/identity/protocols/
pub async fn get_token(
    handle: &tauri::AppHandle,
    email: String,
    refresh_token: Option<oauth2::RefreshToken>
) -> Result<
    (Option<AccessToken>, Option<oauth2::RefreshToken>, Option<Duration>),
    Box<dyn std::error::Error>
> {
    //get the google client ID and the client secret from .env file
    dotenv().ok();

    let google_client_id = ClientId::new(std::env::var("GOOGLE_CLIENT_ID")?);
    let google_client_secret = ClientSecret::new(std::env::var("GOOGLE_CLIENT_SECRET")?);
    let auth_url = AuthUrl::new("https://accounts.google.com/o/oauth2/v2/auth".to_string())?; //.expect("Invalid authorization endpoint URL");
    let token_url = TokenUrl::new("https://www.googleapis.com/oauth2/v3/token".to_string())?; //.expect("Invalid token endpoint URL");

    // Set up the config for the Google OAuth2 process.
    let client = BasicClient::new(
        google_client_id,
        Some(google_client_secret),
        auth_url,
        Some(token_url)
    )
        // This example will be running its own server at http://127.0.0.1:1421
        // See below for the server implementation.
        .set_redirect_uri(
            RedirectUrl::new("http://127.0.0.1:1421".to_string())? //.expect("Invalid redirect URL"),
        )
        // Google supports OAuth 2.0 Token Revocation (RFC-7009)
        .set_revocation_uri(
            RevocationUrl::new("https://oauth2.googleapis.com/revoke".to_string())? //.expect("Invalid revocation endpoint URL"),
        ); //.set_introspection_uri(introspection_url);

    if refresh_token.is_some() {
        println!("get_token() refresh_token found");

        match
            client
                .exchange_refresh_token(&refresh_token.unwrap().clone())
                .request_async(async_http_client).await
        {
            Ok(token_response) => {
                let access_token = token_response.access_token().clone();
                let refresh_token = token_response.refresh_token().cloned();
                let experies_in: Option<Duration> = token_response.expires_in().clone();

                return Ok((Some(access_token), refresh_token, experies_in));
            }
            Err(_) => {}
        }
        println!("get_token() refresh_token not valid, login required");
    }

    // Google supports Proof Key for Code Exchange (PKCE - https://oauth.net/2/pkce/).
    // Create a PKCE code verifier and SHA-256 encode it as a code challenge.
    let (pkce_code_challenge, pkce_code_verifier) = PkceCodeChallenge::new_random_sha256();

    // Generate the authorization URL to which we'll redirect the user.
    let (authorize_url, csrf_state) = client
        .authorize_url(CsrfToken::new_random)
        // This example is requesting access to the "gmail" features and the user's profile.
        //.add_scope(Scope::new("https://mail.google.com".into()))
        .add_scope(Scope::new("profile email".into()))
        .add_scope(Scope::new("https://www.googleapis.com/auth/gmail.readonly".into()))
        .add_scope(Scope::new("https://www.googleapis.com/auth/gmail.modify".into()))
        .add_scope(Scope::new("https://www.googleapis.com/auth/gmail.send".into()))
        .add_scope(Scope::new("https://www.googleapis.com/auth/gmail.labels".into()))
        .add_scope(Scope::new("https://www.googleapis.com/auth/calendar".into()))
        // Add the Pub/Sub scope here
        .add_scope(Scope::new("https://www.googleapis.com/auth/pubsub".into()))
        // This example is also requesting access to the user's profile and email.
        .add_scope(Scope::new("email".into()))
        .add_extra_param("access_type", "offline")
        .add_extra_param("login_hint", email)
        //.add_extra_param("prompt", "none")
        .set_pkce_challenge(pkce_code_challenge)
        .url();

    println!("The authorization URL is:\n{}\n", authorize_url.to_string());

    // let handle = window.app_handle();

    let login_window = tauri::WebviewWindowBuilder
        ::new(
            handle,
            "Google_Login" /* the unique window label */,
            tauri::WebviewUrl::External(
                authorize_url.to_string().parse()? //.expect("error WindowBuilder WindowUrl parse"),
            )
        )
        .build()?; //.expect("error WindowBuilder build");
    login_window.set_title("Google Login");
    login_window.set_always_on_top(true);

    // A very naive implementation of the redirect server.
    let listener = std::net::TcpListener::bind("127.0.0.1:1421")?; //.expect("error TcpListener bind");
    let local_addr = listener.local_addr()?;

    let timer = timer::Timer::new();

    let _guard = timer.schedule_with_delay(chrono::Duration::seconds(300), move || {
        //the time out as connect to close server
        let _ = std::net::TcpStream::connect(local_addr);
    });

    login_window.on_window_event(move |event| {
        if let WindowEvent::CloseRequested { api, .. } = &event {
            info!("event close-requested");
            let _ = std::net::TcpStream::connect(local_addr); //connect to server to close it
        };
    });

    //this is blocking listener! we use guard schedule for time out
    for stream in listener.incoming() {
        let _ = login_window.is_visible()?; //check if login_window is visible

        if let Ok(mut stream) = stream {
            info!("listener stream");

            let code;
            let state;
            let errorinfo;
            {
                let mut reader = BufReader::new(&stream);

                let mut request_line = String::new();
                reader.read_line(&mut request_line)?;

                let redirect_url = match request_line.split_whitespace().nth(1) {
                    Some(url_data) => url_data,
                    _ => {
                        login_window.close()?;
                        break;
                    }
                };
                println!("redirect_url: \n{}", redirect_url.clone());
                let url = url::Url::parse(&("http://localhost".to_string() + redirect_url))?;

                use std::borrow::Cow;
                //extract code from url
                let code_pair = url
                    .query_pairs()
                    .find(|pair| {
                        let &(ref key, _) = pair;
                        key == "code"
                    })
                    .unwrap_or((Cow::from(""), Cow::from("")));

                let (_, value) = code_pair;
                code = AuthorizationCode::new(value.into_owned());

                //extract state from url
                let state_pair = url
                    .query_pairs()
                    .find(|pair| {
                        let &(ref key, _) = pair;
                        key == "state"
                    })
                    .unwrap_or((Cow::from(""), Cow::from("")));

                let (_, value) = state_pair;
                state = CsrfToken::new(value.into_owned());

                //extract error from url
                let errorinfo_pair = url
                    .query_pairs()
                    .find(|pair| {
                        let &(ref key, _) = pair;
                        key == "error"
                    })
                    .unwrap_or((Cow::from(""), Cow::from("")));

                let (_, value) = errorinfo_pair;
                errorinfo = String::from(value.into_owned());
            }

            //if error found
            if !errorinfo.is_empty() {
                login_window.close()?;
                Err(errorinfo)?;
            }

            let message = "Verification completed, please close window.";
            let response = format!(
                "HTTP/1.1 200 OK\r\ncontent-length: {}\r\n\r\n{}",
                message.len(),
                message
            );
            stream.write_all(response.as_bytes())?;

            println!("Google returned the following code:\n{}\n", code.secret());
            println!(
                "Google returned the following state:\n{} (expected `{}`)\n",
                state.secret(),
                csrf_state.secret()
            );

            // Exchange the code with a token.
            let token_response = match
                client
                    .exchange_code(code)
                    .set_pkce_verifier(pkce_code_verifier)
                    .request_async(async_http_client).await
            {
                Ok(res) => res,
                Err(err) => {
                    login_window.close()?;
                    Err("--  no permission --")?
                }
            };

            println!("\n{:#?}", token_response);

            println!(
                "\naccess-token:\n{:#?}\ntoken_type:\n{:#?}\
                \nexpires_in\n{:#?}\nrefresh_token\n{:#?}\
                \nscopes\n{:#?}\nextra_fields\n{:#?}",
                token_response.access_token().clone(),
                token_response.token_type().clone(),
                token_response.expires_in().clone(),
                token_response.refresh_token().clone(),
                token_response.scopes().clone(),
                token_response.extra_fields().clone()
            );

            let access_token = token_response.access_token().clone();
            let refresh_token = token_response.refresh_token().cloned();
            let experies_in: Option<Duration> = token_response.expires_in().clone();

            println!("Google returned the following token:\n{:?}\n", access_token);

            // // Revoke the obtained token
            // let token_response = token_response.unwrap();
            // let token_to_revoke: StandardRevocableToken = match token_response.refresh_token() {
            //     Some(token) => token.into(),
            //     None => token_response.access_token().into(),
            // };

            // client
            //     .revoke_token(token_to_revoke)
            //     .unwrap()
            //     .request_async(async_http_client).await
            //     //.request(http_client)
            //     .expect("Failed to revoke token");

            login_window.close()?; //.expect("error closw login window");

            return Ok((Some(access_token), refresh_token, experies_in));
            // The server will terminate itself after revoking the token.
            break;
        } else {
            println!("error on stream");
            break;
        }
    } //listener.incoming() loop

    Err("-- login window time out --")?

    //return "".to_string(); //token_result.access_token().clone();
}
