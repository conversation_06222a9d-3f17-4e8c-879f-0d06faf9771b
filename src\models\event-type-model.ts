
export type PeriodTypeEnum = 'UNLIMITED' | 'ROLLING' | 'ROLLING_WINDOW' | 'RANGE';
export type SchedulingTypeEnum = 'ROUND_ROBIN' | 'COLLECTIVE' | 'MANAGED' | null;
export type EventLocationType = "inPerson" | "attendeeInPerson" | "somewhereElse" | "link" | "phone" | "userPhone";

export interface RecurringEvent {
  interval: number;
  count: number;
  freq: number;
}

export interface EventLocation {
  type: EventLocationType,
  address?: string,
  link?: string,
  hostPhoneNumber?: string
}

export interface EventTypeSetup {
  title: string;
  slug: string;
  description: string;
  length: number;
  locations: EventLocation[];
  hidden: boolean;
  requiresConfirmation: boolean;
}

export const defaultEventSetup: EventTypeSetup = {
  title: "",
  description: "",
  length: 15,
  locations: [],
  slug: "",
  hidden: false,
  requiresConfirmation: false
};

export interface LimitsVariants {
  PER_DAY?: number;
  PER_WEEK?: number;
  PER_MONTH?: number;
  PER_YEAR?: number;
}

export interface EventTypeLimits {
  beforeEventBuffer: number;
  afterEventBuffer: number;
  minimumBookingNotice: number;
  slotInterval: number | null;
  durationLimits: LimitsVariants | null;
  bookingLimits: LimitsVariants | null;
  onlyShowFirstAvailableSlot: boolean;
  periodType: PeriodTypeEnum;
  periodStartDate: string | null;
  periodEndDate: string | null;
  periodDays: number | null;
  periodCountCalendarDays: boolean;
  offsetStart: number;
}

export const defaultEventLimits: EventTypeLimits = {
  afterEventBuffer: 0,
  beforeEventBuffer: 0,
  bookingLimits: null,
  durationLimits: null,
  minimumBookingNotice: 0,
  onlyShowFirstAvailableSlot: false,
  periodCountCalendarDays: false,
  periodDays: null,
  periodEndDate: null,
  periodStartDate: null,
  periodType: "UNLIMITED",
  slotInterval: null,
  offsetStart: 0,
};

export interface EventType {
  id?: number;
  title: string;
  slug: string;
  length: number;
  hidden: boolean;
  position: number;
  userId: number;
  teamId: number | null;
  scheduleId: number;
  eventName: string | null;
  timeZone: string | null;
  periodType: PeriodTypeEnum;
  periodStartDate: string | null;
  periodEndDate: string | null;
  periodDays: number | null;
  periodCountCalendarDays: boolean;
  requiresConfirmation: boolean;
  recurringEvent?: RecurringEvent;
  disableGuests: boolean;
  hideCalendarNotes: boolean;
  minimumBookingNotice: number;
  beforeEventBuffer: number;
  afterEventBuffer: number;
  schedulingType: SchedulingTypeEnum;
  price: number;
  currency: string;
  slotInterval: number | null;
  // parentId: number | null;
  successRedirectUrl: string | null;
  description: string;
  locations: EventLocation[];
  metadata: any | null;
  seatsPerTimeSlot?: number;
  seatsShowAttendees: boolean;
  seatsShowAvailabilityCount: boolean;
  bookingFields: any[];
  bookingLimits: any | null;
  onlyShowFirstAvailableSlot: boolean;
  durationLimits: any | null;
  children: any[];
  hosts: any[];
  customInputs?: any[];
  link?: string;
  hashedLink?: any[];
  offsetStart: number;
}

export const defaultEventType: EventType = {
  id: 0,
  title: "",
  slug: "",
  length: 15,
  hidden: false,
  position: 0,
  userId: 0,
  teamId: null,
  scheduleId: 0,
  eventName: null,
  timeZone: null,
  periodType: "UNLIMITED",
  periodStartDate: null,
  periodEndDate: null,
  periodDays: null,
  periodCountCalendarDays: false,
  requiresConfirmation: false,
  // recurringEvent: null,
  disableGuests: false,
  hideCalendarNotes: false,
  minimumBookingNotice: 0,
  beforeEventBuffer: 0,
  afterEventBuffer: 0,
  schedulingType: null,
  price: 0,
  currency: "USD",
  slotInterval: null,
  // parentId: null,
  successRedirectUrl: null,
  description: "",
  locations: [],
  metadata: null,
  seatsPerTimeSlot: 0,
  seatsShowAttendees: false,
  seatsShowAvailabilityCount: false,
  bookingFields: [],
  bookingLimits: null,
  onlyShowFirstAvailableSlot: false,
  durationLimits: null,
  children: [],
  hosts: [],
  customInputs: [],
  hashedLink: [],
  offsetStart: 0
};