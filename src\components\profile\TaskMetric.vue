<template>
  <div class="size-full p-2 bg-secondary-200 rounded-md flex justify-center items-center flex-col">
    <div class="text-xs text-center font-semibold text-secondary-900">{{ label }}</div>
    <div class="text-3xl text-primary-800">{{ value ?? 0 }}</div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  label: String,
  value: Number,
});
</script>

<style scoped></style>
