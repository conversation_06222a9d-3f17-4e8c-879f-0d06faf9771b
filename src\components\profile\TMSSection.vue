<template>
  <div class="p-4 h-[90vh] flex flex-col gap-4 size-full overflow-hidden">
    <!-- Task Overview Metrics -->
    <div class="grid grid-cols-4 gap-4">
      <TaskMetric label="✅ Active" :value="stats.active" />
      <TaskMetric label="⏳ Pending" :value="stats.pending" />
      <TaskMetric label="🚀 AI Executed" :value="stats.ai_executed" />
      <TaskMetric label="❌ Escalated" :value="stats.escalated" />
    </div>

    <!-- Main Content Grid Layout -->
    <div class="grid grid-cols-2 gap-4 flex-grow">
      <!-- Left Column -->
      <div class="flex flex-col gap-4">
        <!-- Live Task Queue -->
        <div class="bg-white/40 pt-4 rounded-md shadow flex-grow overflow-hidden h-1/2 min-h-1/2 custom-scrollbar">
          <h2 class="text-xs font-semibold mb-4 px-2">📌 Live Task Queue</h2>
          <div class="overflow-y-auto max-h-60 custom-scrollbar px-2 max-h-60">
            <table class="w-full border-collapse text-sm">
              <thead>
                <tr class="bg-primary-500 text-xs">
                  <th class="p-1 text-left">Task Name</th>
                  <th class="p-1">Category</th>
                  <th class="p-1">Execution</th>
                  <!-- <th class="p-1">Actions</th> -->
                  <th class="p-1">Status</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="task in queueTasks" :key="task.id" class="border-b">
                  <td :title="task.title" class="p-1 line-clamp-1x text-nowrap max-w-28 truncate">{{ task.title }}</td>
                  <td class="p-1">{{ task.category }}</td>
                  <td class="p-1">{{ task.execution_status }}</td>
                  <td class="p-1">{{ task.execution_status != "not_started" ? task.status : "/" }}</td>
                  <td class="p-1">
                    <!-- <a v-if="task.actionLink" :href="task.actionLink" class="text-blue-500 text-xs">{{
                    task.actionText
                  }}</a> -->
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- AI Execution Logs -->
        <div class="bg-white/40 pt-4 rounded-md shadow flex-grow overflow-hidden h-1/2 min-h-1/2 custom-scrollbar">
          <h2 class="text-xs font-semibold mb-4 px-2">✅ Completed Tasks</h2>
          <table class="w-full border-collapse text-sm">
            <thead>
              <tr class="bg-primary-500 text-xs">
                <th class="p-1 text-left">Task Name</th>
                <th class="p-1">Category</th>
                <th class="p-1">Status</th>
                <th class="p-1">Action</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="task in completedTasks" class="border-b">
                <td :title="task.title" class="p-1 line-clamp-1x text-nowrap max-w-28 truncate">{{ task.id }}</td>
                <td class="p-1">{{ task.category }}</td>
                <td class="p-1">{{ task.status }}</td>
                <td class="p-1">
                  <div
                    class="hover:text-blue-500 cursor-pointer transition-colors duration-150"
                    v-if="task.status === 'waiting_for_response'"
                    @click="() => goToTaskDraft(task.id)"
                  >
                    ✍️ View draft
                  </div>
                  <div class="pl-2" v-else>-</div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Right Column -->
      <div class="flex flex-col gap-4">
        <!-- Agent Workload & Escalations -->
        <div class="grid grid-cols-2 gap-4">
          <div class="bg-white p-4 rounded-md shadow">
            <h2 class="text-lg font-semibold mb-4">🏢 Agent Workload</h2>
            <ul>
              <li v-for="agent in agents" :key="agent.name" class="mb-2 text-sm">
                {{ agent.name }} (✅ {{ agent.workload }}%)
              </li>
            </ul>
          </div>
          <div class="bg-white p-4 rounded-md shadow">
            <h2 class="text-lg font-semibold mb-4">🚨 Escalations</h2>
            <ul>
              <li v-for="issue in escalations" :key="issue" class="mb-2 text-sm">
                {{ issue }}
              </li>
            </ul>
          </div>
        </div>
        <!-- Manual Task Input -->
        <div class="bg-white p-4 rounded-md shadow">
          <h2 class="text-lg font-semibold mb-4">➕ Add a New Task</h2>
          <form @submit.prevent="addTask">
            <div class="flex gap-4">
              <input v-model="newTaskTitle" type="text" placeholder="Task Title" class="p-2 border rounded w-full" />
              <select v-model="newTaskCategory" class="p-2 border rounded">
                <option value="email">📩 Email</option>
                <option value="calendar">📅 Calendar</option>
                <option value="phone_call">📞 Phone Call</option>
              </select>
              <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded">Add Task</button>
            </div>
          </form>
        </div>
        <!-- Follow-Ups & Upcoming Tasks -->
        <div class="bg-white/40 p-4 rounded-md shadow flex-grow overflow-auto">
          <h2 class="text-lg font-semibold mb-4">📅 Follow-Ups & Upcoming Tasks</h2>
          <ul>
            <li v-for="draft in drafts" :key="draft.id" class="flex justify-start items-center gap-2 mb-2 text-sm">
              <div class="w-11/12 truncate">{{ draft.subject }}</div>
              <div @click="()=>goToTaskDraft(draft.task_id!)" class="text-blue-500 underline cursor-pointer">View</div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import TaskMetric from "@/components/profile/TaskMetric.vue";
import { ref, onMounted, onUnmounted } from "vue";
import { invoke } from "@tauri-apps/api/core";
import { Draft, Task, TasksStat } from "../../models/task-model";
import { AppTasksService } from "../../commands/tasks";
import { useRouter } from "vue-router";

const router = useRouter();

// Task Overview Metrics
const stats = ref<TasksStat>({
  active: 12,
  pending: 5,
  ai_executed: 30,
  escalated: 3,
});

const drafts = ref<Draft[]>([]);

// Live Task Queue Data
const tasks = ref<Task[]>([]);
const completedTasks = ref<Task[]>([]);
const queueTasks = ref<Task[]>([]);

// Agent Workload Data
const agents = ref([
  { name: "Agent A", workload: 40 },
  { name: "Agent B", workload: 20 },
]);

// Escalation Issues
const escalations = ref(["🔥 Overdue Task", "❗ AI Failure"]);

// AI Execution Logs
const aiLogs = ref([
  { time: "10:35 AM", action: "📩 AI Reply", details: "Thanks for your inquiry..." },
  { time: "10:40 AM", action: "📅 AI Scheduled", details: "Investor call Wed 3 PM" },
  { time: "10:45 AM", action: "📞 AI Call", details: "Call completed" },
]);

// Upcoming Tasks & Follow-Ups
const upcomingTasks = ref([
  { name: "📩 Follow Up with CEO", action: "✍️ Review Draft", link: "#" },
  { name: "📞 Call Partner X", action: "📞 View Call Notes", link: "#" },
  { name: "📅 Product Demo", action: "📅 Open Event", link: "#" },
]);

async function fetchDashboardData() {
  // try {
  //   stats.value = await invoke("get_task_stats");
  //   taskQueue.value = await invoke("list_tasks");
  //   // agents.value = await invoke("get_agent_workload");
  //   // escalations.value = await invoke("get_escalations");
  //   // aiLogs.value = await invoke("get_ai_execution_logs");
  //   // upcomingTasks.value = await invoke("get_upcoming_tasks");
  // } catch (error) {
  //   console.error("Error fetching dashboard data:", error);
  // }
  //  console.log("$$$ Fetching Dash Data");
  // const [inQueue, completed, stat, draft] = await Promise.all([
  //   AppTasksService.listTasks("in_queue"),
  //   AppTasksService.listTasks("completed"),
  //   AppTasksService.getTasksStat(),
  //   AppTasksService.getDrafts(),
  // ]);
  const [inQueue, completed, stat, draft] = await Promise.all([
    AppTasksService.listTasks("in_queue").catch((e) => {
      console.error("Failed to fetch in_queue tasks", e);
      return []; // fallback value
    }),
    AppTasksService.listTasks("completed").catch((e) => {
      console.error("Failed to fetch completed tasks", e);
      return []; // fallback value
    }),
    AppTasksService.getTasksStat().catch((e) => {
      console.error("Failed to fetch task stats", e);
      return { total: 0, completed: 0, inQueue: 0 }; // fallback value
    }),
    AppTasksService.getDrafts().catch((e) => {
      console.error("Failed to fetch drafts", e);
      return []; // fallback value
    }),
  ]);
  //  console.log(completed, inQueue);
  completedTasks.value = completed ?? [];
  queueTasks.value = inQueue ?? [];
  stats.value = stat;
  drafts.value = draft;
}

// Manual Task Input
const newTaskTitle = ref("");
const newTaskCategory = ref("email");

async function goToTaskDraft(taskId: number) {
  let draft = await AppTasksService.getTaskDraft(taskId);
  //  console.log("Task Draft =>", draft);
  if (draft?.thread_id) await router.push(`/emails?emailId=${draft?.thread_id}`);
}

async function addTask() {
  if (!newTaskTitle.value.trim()) return alert("Task title is required!");

  try {
    await invoke("new_task", {
      title: newTaskTitle.value,
      category: newTaskCategory.value,
      priority: "medium",
    });
    newTaskTitle.value = ""; // Clear input field
    fetchDashboardData(); // Refresh task list
  } catch (error) {
    console.error("Error adding task:", error);
  }
}

let refreshInterval: NodeJS.Timer;
function startAutoRefresh(intervalMs = 3000) {
  fetchDashboardData();
  refreshInterval = setInterval(fetchDashboardData, intervalMs);
}

onMounted(() => {
  startAutoRefresh();
});

onUnmounted(() => {
  if (refreshInterval) clearInterval(refreshInterval);
});
</script>

<style scoped>
table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}
th,
td {
  padding: 6px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}
</style>
