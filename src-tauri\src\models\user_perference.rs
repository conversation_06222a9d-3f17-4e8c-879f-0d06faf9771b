use directories::BaseDirs;
use serde_json::json;
use std::fs;
use std::path::PathBuf;

#[derive(Debug, Clone, serde::Deserialize, serde::Serialize)]
pub struct EmailContext {
    // User details and preferences (all fields from your struct)
    pub full_name: String,
    pub job_title: String,
    pub organization: Option<String>,
    pub target_audience: String,
    pub communication_goal: String,
    pub call_to_action: Option<String>,
    pub tone_preference: String,
    pub language_style: String,
    pub key_points: String,
    pub known_preferences: Option<Vec<String>>,
    pub personal_sign_off: String,
    pub email_signature: Option<String>,
    pub work_hours: Option<(String, String)>,
    pub availability_note: Option<String>,
    pub email_length_preference: String,
    pub urgency_level: String,
}

impl EmailContext {
    pub fn new(
        full_name: String,
        job_title: String,
        organization: Option<String>,
        target_audience: String,
        communication_goal: String,
        call_to_action: Option<String>,
        tone_preference: String,
        language_style: String,
        key_points: String,
        known_preferences: Option<Vec<String>>,
        personal_sign_off: String,
        email_signature: Option<String>,
        work_hours: Option<(String, String)>,
        availability_note: Option<String>,
        email_length_preference: String,
        urgency_level: String,
    ) -> Self {
        EmailContext {
            full_name,
            job_title,
            organization,
            target_audience,
            communication_goal,
            call_to_action,
            tone_preference,
            language_style,
            key_points,
            known_preferences,
            personal_sign_off,
            email_signature,
            work_hours,
            availability_note,
            email_length_preference,
            urgency_level,
        }
    }

    pub fn update_from_json(&mut self, json_data: serde_json::Value) -> Result<(), String> {
        if let Some(full_name) = json_data.get("full_name").and_then(|v| v.as_str()) {
            self.full_name = full_name.to_string();
        }
        if let Some(job_title) = json_data.get("job_title").and_then(|v| v.as_str()) {
            self.job_title = job_title.to_string();
        }
        if let Some(organization) = json_data.get("organization").and_then(|v| v.as_str()) {
            self.organization = Some(organization.to_string());
        }
        if let Some(target_audience) = json_data.get("target_audience").and_then(|v| v.as_str()) {
            self.target_audience = target_audience.to_string();
        }
        if let Some(communication_goal) =
            json_data.get("communication_goal").and_then(|v| v.as_str())
        {
            self.communication_goal = communication_goal.to_string();
        }
        if let Some(call_to_action) = json_data.get("call_to_action").and_then(|v| v.as_str()) {
            self.call_to_action = Some(call_to_action.to_string());
        }
        if let Some(tone_preference) = json_data.get("tone_preference").and_then(|v| v.as_str()) {
            self.tone_preference = tone_preference.to_string();
        }
        if let Some(language_style) = json_data.get("language_style").and_then(|v| v.as_str()) {
            self.language_style = language_style.to_string();
        }
        if let Some(key_points) = json_data.get("key_points").and_then(|v| v.as_str()) {
            self.key_points = key_points.to_string();
        }
        if let Some(known_prefs_raw) = json_data.get("known_preferences") {
            if known_prefs_raw.is_array() {
                self.known_preferences = Some(
                    known_prefs_raw
                        .as_array()
                        .unwrap()
                        .iter()
                        .filter_map(|v| v.as_str().map(String::from))
                        .collect(),
                );
            } else if let Some(pref_str) = known_prefs_raw.as_str() {
                // Try to parse the string manually
                let cleaned = pref_str
                    .trim_matches(&['[', ']'][..])
                    .split(',')
                    .map(|s| s.trim_matches(&['\'', '"', ' '][..]).to_string())
                    .collect::<Vec<_>>();
                self.known_preferences = Some(cleaned);
            }
        }
        if let Some(personal_sign_off) = json_data.get("personal_sign_off").and_then(|v| v.as_str())
        {
            self.personal_sign_off = personal_sign_off.to_string();
        }
        if let Some(email_signature) = json_data.get("email_signature").and_then(|v| v.as_str()) {
            self.email_signature = Some(email_signature.to_string());
        }
        let start = json_data.get("work_hours_start").and_then(|v| v.as_str());
        let end = json_data.get("work_hours_end").and_then(|v| v.as_str());

        if let (Some(start), Some(end)) = (start, end) {
            self.work_hours = Some((start.to_string(), end.to_string()));
            println!("✅ Set work_hours: ({}, {})", start, end);
        } else {
            println!("ℹ️ work_hours_start or work_hours_end missing or null");
        }
        if let Some(availability_note) = json_data.get("availability_note").and_then(|v| v.as_str())
        {
            self.availability_note = Some(availability_note.to_string());
        }
        if let Some(email_length_preference) = json_data
            .get("email_length_preference")
            .and_then(|v| v.as_str())
        {
            self.email_length_preference = email_length_preference.to_string();
        }
        if let Some(urgency_level) = json_data.get("urgency_level").and_then(|v| v.as_str()) {
            self.urgency_level = urgency_level.to_string();
        }

        println!("🔄 Attempting to save profile...");
        self.save_profile();
        println!("✅ Profile saved.");
        Ok(())
    }

    pub fn to_json(&self) -> serde_json::Value {
        json!({
            "full_name": self.full_name,
            "job_title": self.job_title,
            "organization": self.organization,
            "target_audience": self.target_audience,
            "communication_goal": self.communication_goal,
            "call_to_action": self.call_to_action,
            "tone_preference": self.tone_preference,
            "language_style": self.language_style,
            "key_points": self.key_points,
            "known_preferences": self.known_preferences,
            "personal_sign_off": self.personal_sign_off,
            "email_signature": self.email_signature,
            "work_hours": self.work_hours,
            "availability_note": self.availability_note,
            "email_length_preference": self.email_length_preference,
            "urgency_level": self.urgency_level,
        })
    }

    // Initializes EmailContext by loading data from a file
    pub fn init_user_email_context() -> Self {
        let base_dirs = BaseDirs::new().expect("Failed to get base directory");
        let file_path = base_dirs.home_dir().join("user_email_context.json");

        // Attempt to read and deserialize the profile file
        match fs::read_to_string(&file_path) {
            Ok(data) => match serde_json::from_str(&data) {
                Ok(profile) => profile,
                Err(_) => Self::default_profile(),
            },
            Err(_) => Self::default_profile(),
        }
    }

    // Default profile in case of any errors in file loading
    pub fn default_profile() -> Self {
        EmailContext {
            full_name: "".to_string(),
            job_title: "".to_string(),
            organization: None,
            target_audience: "".to_string(),
            communication_goal: "".to_string(),
            call_to_action: None,
            tone_preference: "".to_string(),
            language_style: "".to_string(),
            key_points: "".to_string(),
            known_preferences: None,
            personal_sign_off: "".to_string(),
            email_signature: None,
            work_hours: None,
            availability_note: None,
            email_length_preference: "".to_string(),
            urgency_level: "".to_string(),
        }
    }

    // Save current profile data to a file
    pub fn save_profile(&self) {
        let base_dirs = BaseDirs::new().expect("Failed to get base directory");
        let file_path = base_dirs.home_dir().join("user_email_context.json");

        if let Ok(json_data) = serde_json::to_string(self) {
            if let Err(err) = fs::write(&file_path, json_data) {
                eprintln!("Error saving profile: {:?}", err);
            }
        }
    }

    // Update specific fields and save the profile
    pub fn update_profile(
        &mut self,
        full_name: Option<String>,
        job_title: Option<String>,
        target_audience: Option<String>,
        communication_goal: Option<String>,
        tone_preference: Option<String>,
    ) {
        if let Some(name) = full_name {
            self.full_name = name;
        }
        if let Some(title) = job_title {
            self.job_title = title;
        }
        if let Some(audience) = target_audience {
            self.target_audience = audience;
        }
        if let Some(goal) = communication_goal {
            self.communication_goal = goal;
        }
        if let Some(tone) = tone_preference {
            self.tone_preference = tone;
        }
        self.save_profile();
    }

    pub fn update_tone_and_key_points(&mut self, tone_preference: String, key_points: String) {
        self.tone_preference = tone_preference;
        self.key_points = key_points;
        self.save_profile();
    }

    /// Generate a personalized email prompt based on the user's profile
    pub fn generate_personalized_prompt(&self) -> String {
        let organization = match &self.organization {
            Some(org) => format!("at {}", org),
            None => "".to_string(),
        };

        let work_hours_info = match &self.work_hours {
            Some(hours) => format!("They work from {} to {}.", hours.0, hours.1),
            None => "".to_string(),
        };

        let availability_info = match &self.availability_note {
            Some(note) => format!("Their availability: {}.", note),
            None => "".to_string(),
        };

        let known_preferences_info = match &self.known_preferences {
            Some(preferences) => format!(
                "The recipient prefers the following: {}.",
                preferences.join(", ")
            ),
            None => "".to_string(),
        };

        let call_to_action_info = match &self.call_to_action {
            Some(cta) => format!("The desired outcome is: {}.", cta),
            None => "".to_string(),
        };

        format!(
            "Compose an email for {} who is a {} {}. The tone should be {} and the language style should be {}.
            The email is intended for {} with the goal of {}. {} {}
            The email should cover the following key points: {}.
            {} The email should be {} in length and should convey a sense of {} urgency.
            They prefer to sign off with '{}', and their signature is '{}'.",
            self.full_name,
            self.job_title,
            organization,
            self.tone_preference,
            self.language_style,
            self.target_audience,
            self.communication_goal,
            call_to_action_info,
            known_preferences_info,
            self.key_points,
            availability_info,
            self.email_length_preference,
            self.urgency_level,
            self.personal_sign_off,
            self.email_signature.clone().unwrap_or_default()
        )
    }
}
