<template>
  <div class="fixed inset-0 bg-black/50 flex items-center justify-center px-6 z-50">
    <div class="bg-white rounded-xl shadow-lg w-full max-w-2xl p-6 space-y-6">
      <div class="flex justify-between items-center">
        <h2 class="text-xl font-bold text-gray-800">Setup Email Assistant</h2>
        <button @click="$emit('close')" class="text-gray-500 hover:text-red-500 transition-all">
          <XMarkIcon class="size-6" />
        </button>
      </div>

      <div class="space-y-4">
        <div>
          <label class="block text-sm font-semibold text-gray-700">Days of Email to Summarize</label>
          <input
            v-model.number="config.summarizeDaysBack"
            type="number"
            min="1"
            max="30"
            class="mt-1 w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-2 focus:ring-secondary-400"
          />
        </div>

        <div class="flex items-center gap-3">
          <input v-model="config.autoCleanupEnabled" type="checkbox" id="cleanup" class="rounded" />
          <label for="cleanup" class="text-sm text-gray-700">Auto clean categories daily</label>
        </div>

        <div class="flex items-center gap-3">
          <input v-model="config.snoozeResurfaceEnabled" type="checkbox" id="resurface" class="rounded" />
          <label for="resurface" class="text-sm text-gray-700">Auto resurface expired snoozes</label>
        </div>
      </div>

      <div class="pt-4 flex justify-end">
        <button
          @click="saveConfig"
          class="bg-secondary-500 hover:bg-secondary-600 text-white px-6 py-2 rounded-lg shadow-md transition"
        >
          Save Preferences
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { XMarkIcon } from "@heroicons/vue/24/outline";
import { EmailLog, UserRoleConfig } from "../../models/cm-model";
import { useCurrentUserStore } from "../../stores/currentUser";
import { invoke } from "@tauri-apps/api/core";

const emit = defineEmits(["close", "saved"]);

const currentUser = useCurrentUserStore().currentUser;

const config = ref<UserRoleConfig>({
  userId: currentUser?.id ?? "0",
  summarizeDaysBack: 5,
  autoCleanupEnabled: true,
  snoozeResurfaceEnabled: true,
  autoAddVipDomain: true,
});

onMounted(async () => {
  // try {
  //   // const saved = await invoke<UserRoleConfig>("get_user_role_config", {
  //   //   userId: currentUser.id,
  //   // });
  //   if (saved) config.value = saved;
  // } catch (err) {
  //   console.warn("No saved CM config, using default");
  // }
});

async function saveConfig() {
  await invoke("set_user_role_config", { config: config.value });
  emit("saved", config.value);
  emit("close");
}
</script>
