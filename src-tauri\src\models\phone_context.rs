use directories::BaseDirs;
use serde_json::json;
use std::fs;
use std::path::PathBuf;
use tokio::sync::Mutex;

#[derive(Debug, Clone, serde::Deserialize, serde::Serialize)]
pub struct PhoneCallContext {
    // User-specific context
    pub full_name: String,
    pub job_title: String,
    pub organization: Option<String>,

    // Targeting and communication details
    pub target_audience: String,
    pub communication_goal: String,
    pub call_to_action: Option<String>,
    pub tone_preference: String,
    pub language_style: String,
    pub preferred_response_patterns: Option<Vec<String>>,
    pub personalization_triggers: Option<Vec<String>>,
    pub key_points: String,

    // Known caller preferences
    pub contextual_data: Option<Vec<String>>,

    // Business details
    pub business_overview: Option<String>,
    pub pricing_info: Option<String>,
    pub frequently_asked_questions: Option<Vec<String>>,
    pub services_offered: Option<Vec<String>>,

    // Availability and contact details
    pub work_hours: Option<(String, String)>,
    pub availability_note: Option<String>,
    pub contact_email: Option<String>,
    pub contact_phone: Option<String>,
    pub location_address: Option<String>,

    // AI agent configuration
    pub urgency_level: String,
    pub greeting_message: String,
    pub follow_up_message: Option<String>,
    pub dynamic_scripts: Option<Vec<String>>,
    pub escalation_contact: Option<String>,
    pub supported_languages: Option<Vec<String>>,
    pub sentiment_triggers: Option<Vec<String>>,
    pub additional_resources: Option<String>,

    // Advanced conversational configurations
    pub adaptive_learning_enabled: bool,
    pub response_time_preferences: Option<String>,
    pub default_escalation_path: Option<String>,
    pub backup_agent_contact: Option<String>,
}

impl PhoneCallContext {
    pub fn new(
        full_name: String,
        job_title: String,
        organization: Option<String>,
        target_audience: String,
        communication_goal: String,
        call_to_action: Option<String>,
        tone_preference: String,
        language_style: String,
        preferred_response_patterns: Option<Vec<String>>,
        personalization_triggers: Option<Vec<String>>,
        key_points: String,
        contextual_data: Option<Vec<String>>,
        business_overview: Option<String>,
        pricing_info: Option<String>,
        frequently_asked_questions: Option<Vec<String>>,
        services_offered: Option<Vec<String>>,
        work_hours: Option<(String, String)>,
        availability_note: Option<String>,
        contact_email: Option<String>,
        contact_phone: Option<String>,
        location_address: Option<String>,
        urgency_level: String,
        greeting_message: String,
        follow_up_message: Option<String>,
        dynamic_scripts: Option<Vec<String>>,
        escalation_contact: Option<String>,
        supported_languages: Option<Vec<String>>,
        sentiment_triggers: Option<Vec<String>>,
        additional_resources: Option<String>,
        adaptive_learning_enabled: bool,
        response_time_preferences: Option<String>,
        default_escalation_path: Option<String>,
        backup_agent_contact: Option<String>,
    ) -> Self {
        PhoneCallContext {
            full_name,
            job_title,
            organization,
            target_audience,
            communication_goal,
            call_to_action,
            tone_preference,
            language_style,
            preferred_response_patterns,
            personalization_triggers,
            key_points,
            contextual_data,
            business_overview,
            pricing_info,
            frequently_asked_questions,
            services_offered,
            work_hours,
            availability_note,
            contact_email,
            contact_phone,
            location_address,
            urgency_level,
            greeting_message,
            follow_up_message,
            dynamic_scripts,
            escalation_contact,
            supported_languages,
            sentiment_triggers,
            additional_resources,
            adaptive_learning_enabled,
            response_time_preferences,
            default_escalation_path,
            backup_agent_contact,
        }
    }

    // Initializes PhoneCallContext by loading data from a file
    pub fn init_user_phone_context() -> Self {
        let base_dirs = BaseDirs::new().expect("Failed to get base directory");
        let file_path = base_dirs.home_dir().join("user_phone_context.json");

        match fs::read_to_string(&file_path) {
            Ok(data) => match serde_json::from_str(&data) {
                Ok(context) => context,
                Err(_) => Self::default_context(),
            },
            Err(_) => Self::default_context(),
        }
    }

    // Default context in case of errors
    pub fn default_context() -> Self {
        PhoneCallContext {
            full_name: "".to_string(),
            job_title: "".to_string(),
            organization: None,
            target_audience: "".to_string(),
            communication_goal: "".to_string(),
            call_to_action: None,
            tone_preference: "".to_string(),
            language_style: "".to_string(),
            preferred_response_patterns: None,
            personalization_triggers: None,
            key_points: "".to_string(),
            contextual_data: None,
            business_overview: None,
            pricing_info: None,
            frequently_asked_questions: None,
            services_offered: None,
            work_hours: None,
            availability_note: None,
            contact_email: None,
            contact_phone: None,
            location_address: None,
            urgency_level: "".to_string(),
            greeting_message: "Thank you for calling our business!".to_string(),
            follow_up_message: None,
            dynamic_scripts: None,
            escalation_contact: None,
            supported_languages: None,
            sentiment_triggers: None,
            additional_resources: None,
            adaptive_learning_enabled: true,
            response_time_preferences: None,
            default_escalation_path: None,
            backup_agent_contact: None,
        }
    }
    pub fn to_json(&self) -> serde_json::Value {
        json!({
            "full_name": self.full_name,
            "job_title": self.job_title,
            "organization": self.organization,
            "target_audience": self.target_audience,
            "communication_goal": self.communication_goal,
            "call_to_action": self.call_to_action,
            "tone_preference": self.tone_preference,
            "language_style": self.language_style,
            "preferred_response_patterns": self.preferred_response_patterns,
            "personalization_triggers": self.personalization_triggers,
            "key_points": self.key_points,
            "contextual_data": self.contextual_data,
            "business_overview": self.business_overview,
            "pricing_info": self.pricing_info,
            "frequently_asked_questions": self.frequently_asked_questions,
            "services_offered": self.services_offered,
            "work_hours": self.work_hours,
            "availability_note": self.availability_note,
            "contact_email": self.contact_email,
            "contact_phone": self.contact_phone,
            "location_address": self.location_address,
            "urgency_level": self.urgency_level,
            "greeting_message": self.greeting_message,
            "follow_up_message": self.follow_up_message,
            "dynamic_scripts": self.dynamic_scripts,
            "escalation_contact": self.escalation_contact,
            "supported_languages": self.supported_languages,
            "sentiment_triggers": self.sentiment_triggers,
            "additional_resources": self.additional_resources,
            "adaptive_learning_enabled": self.adaptive_learning_enabled,
            "response_time_preferences": self.response_time_preferences,
            "default_escalation_path": self.default_escalation_path,
            "backup_agent_contact": self.backup_agent_contact,
        })
    }

    pub fn update_from_json(&mut self, json_data: serde_json::Value) -> Result<(), String> {
        if let Some(full_name) = json_data.get("full_name").and_then(|v| v.as_str()) {
            self.full_name = full_name.to_string();
        }
        if let Some(job_title) = json_data.get("job_title").and_then(|v| v.as_str()) {
            self.job_title = job_title.to_string();
        }
        if let Some(organization) = json_data.get("organization").and_then(|v| v.as_str()) {
            self.organization = Some(organization.to_string());
        }
        if let Some(target_audience) = json_data.get("target_audience").and_then(|v| v.as_str()) {
            self.target_audience = target_audience.to_string();
        }
        if let Some(communication_goal) =
            json_data.get("communication_goal").and_then(|v| v.as_str())
        {
            self.communication_goal = communication_goal.to_string();
        }
        if let Some(call_to_action) = json_data.get("call_to_action").and_then(|v| v.as_str()) {
            self.call_to_action = Some(call_to_action.to_string());
        }
        if let Some(tone_preference) = json_data.get("tone_preference").and_then(|v| v.as_str()) {
            self.tone_preference = tone_preference.to_string();
        }
        if let Some(language_style) = json_data.get("language_style").and_then(|v| v.as_str()) {
            self.language_style = language_style.to_string();
        }
        if let Some(preferred_response_patterns) = json_data
            .get("preferred_response_patterns")
            .and_then(|v| v.as_array())
        {
            self.preferred_response_patterns = Some(
                preferred_response_patterns
                    .iter()
                    .filter_map(|v| v.as_str().map(String::from))
                    .collect(),
            );
        }
        if let Some(personalization_triggers) = json_data
            .get("personalization_triggers")
            .and_then(|v| v.as_array())
        {
            self.personalization_triggers = Some(
                personalization_triggers
                    .iter()
                    .filter_map(|v| v.as_str().map(String::from))
                    .collect(),
            );
        }
        if let Some(key_points_array) = json_data.get("key_points").and_then(|v| v.as_array()) {
            self.key_points = key_points_array
                .iter()
                .filter_map(|v| v.as_str())
                .collect::<Vec<_>>()
                .join(", ");
        } else if let Some(key_points_string) = json_data.get("key_points").and_then(|v| v.as_str())
        {
            self.key_points = key_points_string.to_string();
        }
        if let Some(contextual_data) = json_data.get("contextual_data").and_then(|v| v.as_array()) {
            self.contextual_data = Some(
                contextual_data
                    .iter()
                    .filter_map(|v| v.as_str().map(String::from))
                    .collect(),
            );
        }
        if let Some(business_overview) = json_data.get("business_overview").and_then(|v| v.as_str())
        {
            self.business_overview = Some(business_overview.to_string());
        }
        if let Some(pricing_info) = json_data.get("pricing_info").and_then(|v| v.as_str()) {
            self.pricing_info = Some(pricing_info.to_string());
        }
        if let Some(frequently_asked_questions) = json_data
            .get("frequently_asked_questions")
            .and_then(|v| v.as_array())
        {
            self.frequently_asked_questions = Some(
                frequently_asked_questions
                    .iter()
                    .filter_map(|v| v.as_str().map(String::from))
                    .collect(),
            );
        }
        if let Some(services_offered) = json_data.get("services_offered").and_then(|v| v.as_array())
        {
            self.services_offered = Some(
                services_offered
                    .iter()
                    .filter_map(|v| v.as_str().map(String::from))
                    .collect(),
            );
        }
        if let Some(work_hours) = json_data.get("work_hours").and_then(|v| v.as_array()) {
            if work_hours.len() == 2 {
                if let (Some(start), Some(end)) = (work_hours[0].as_str(), work_hours[1].as_str()) {
                    self.work_hours = Some((start.to_string(), end.to_string()));
                }
            }
        }
        if let Some(availability_note) = json_data.get("availability_note").and_then(|v| v.as_str())
        {
            self.availability_note = Some(availability_note.to_string());
        }
        if let Some(contact_email) = json_data.get("contact_email").and_then(|v| v.as_str()) {
            self.contact_email = Some(contact_email.to_string());
        }
        if let Some(contact_phone) = json_data.get("contact_phone").and_then(|v| v.as_str()) {
            self.contact_phone = Some(contact_phone.chars().take(15).collect());
        }
        if let Some(location_address) = json_data.get("location_address").and_then(|v| v.as_str()) {
            self.location_address = Some(location_address.to_string());
        }
        if let Some(urgency_level) = json_data.get("urgency_level").and_then(|v| v.as_str()) {
            self.urgency_level = urgency_level.to_string();
        }
        if let Some(greeting_message) = json_data.get("greeting_message").and_then(|v| v.as_str()) {
            self.greeting_message = greeting_message.to_string();
        }
        if let Some(follow_up_message) = json_data.get("follow_up_message").and_then(|v| v.as_str())
        {
            self.follow_up_message = Some(follow_up_message.to_string());
        }
        if let Some(dynamic_scripts) = json_data.get("dynamic_scripts").and_then(|v| v.as_array()) {
            self.dynamic_scripts = Some(
                dynamic_scripts
                    .iter()
                    .filter_map(|v| v.as_str().map(String::from))
                    .collect(),
            );
        }
        if let Some(escalation_contact) =
            json_data.get("escalation_contact").and_then(|v| v.as_str())
        {
            self.escalation_contact = Some(escalation_contact.to_string());
        }
        if let Some(supported_languages) = json_data
            .get("supported_languages")
            .and_then(|v| v.as_array())
        {
            self.supported_languages = Some(
                supported_languages
                    .iter()
                    .filter_map(|v| v.as_str().map(String::from))
                    .collect(),
            );
        }
        if let Some(sentiment_triggers) = json_data
            .get("sentiment_triggers")
            .and_then(|v| v.as_array())
        {
            self.sentiment_triggers = Some(
                sentiment_triggers
                    .iter()
                    .filter_map(|v| v.as_str().map(String::from))
                    .collect(),
            );
        }
        if let Some(additional_resources) = json_data
            .get("additional_resources")
            .and_then(|v| v.as_str())
        {
            self.additional_resources = Some(additional_resources.to_string());
        }
        if let Some(adaptive_learning_enabled) = json_data
            .get("adaptive_learning_enabled")
            .and_then(|v| v.as_bool())
        {
            self.adaptive_learning_enabled = adaptive_learning_enabled;
        }
        if let Some(response_time_preferences) = json_data
            .get("response_time_preferences")
            .and_then(|v| v.as_str())
        {
            self.response_time_preferences = Some(response_time_preferences.to_string());
        }
        if let Some(default_escalation_path) = json_data
            .get("default_escalation_path")
            .and_then(|v| v.as_str())
        {
            self.default_escalation_path = Some(default_escalation_path.to_string());
        }
        if let Some(backup_agent_contact) = json_data
            .get("backup_agent_contact")
            .and_then(|v| v.as_str())
        {
            self.backup_agent_contact = Some(backup_agent_contact.to_string());
        }

        self.save_context(); // Save updated context to file
        Ok(())
    }

    // Save current context to a file
    pub fn save_context(&self) {
        let base_dirs = BaseDirs::new().expect("Failed to get base directory");
        let file_path = base_dirs.home_dir().join("user_phone_context.json");

        if let Ok(json_data) = serde_json::to_string(self) {
            if let Err(err) = fs::write(&file_path, json_data) {
                eprintln!("Error saving context: {:?}", err);
            }
        }
    }
}
