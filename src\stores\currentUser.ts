import { invoke } from "@tauri-apps/api/core";
import { defineStore } from "pinia";
import { EmailContext, PhoneCallContext, User } from "../types";
import { CalUser } from "../models/user";
import { calUsersService } from "../services/cal-api/user-cal-service";
import auth from "../commands/auth";

export interface UserData {
  user: User; // Define the User interface separately
  calInfo?: CalUser | null;
  refreshToken?: string | null; // oauth2::RefreshToken is usually a string
  expireIn?: number | null; // Duration → number (in ms or seconds, depending on your API)
  accessToken?: string | null; // oauth2::AccessToken is usually a string
  issuedAt?: string | null; // DateTime<Utc> → ISO string
  emailContext?: EmailContext | null; // Define EmailContext separately
  phoneContext?: PhoneCallContext | null; // Define PhoneCallContext separately
  watchExpiration?: number | null; // i64 in ms → number
  historyId?: string | null;
}


interface State {
  currentUser?: User;
  calInfo?: CalUser | null,
  currentEmailContext?: EmailContext,
  currentPhoneContext?: PhoneCallContext;
}

export const useCurrentUserStore = defineStore("currentUser", {
  state: (): State => ({
    currentUser: undefined,
    calInfo: undefined
  }),
  actions: {
    // async init() {
    //   try {
    //     const data: any = await invoke("js2rs", {
    //       message: "get_user",
    //     });

    //     // Debugging output
    //     //  console.log("Raw data received from invoke:", data);

    //     if (!data) {
    //       console.error("No data received from invoke");
    //       return;
    //     }

    //     let me_data;
    //     try {
    //       me_data = JSON.parse(data);
    //       //  console.log("Parsed data:", me_data);
    //     } catch (err) {
    //       console.error("Error parsing data:", err);
    //       return;
    //     }

    //     // Initialize currentUser if undefined
    //     this.currentUser = this.currentUser ?? ({} as User);

    //     //  console.log("Current user before update:", this.currentUser);

    //     // Populate currentUser fields
    //     this.currentUser.id = me_data.id ?? "";
    //     this.currentUser.email = me_data.email ?? "";
    //     this.currentUser.verified_email = me_data.verified_email ?? "";
    //     this.currentUser.name = me_data.name ?? "";
    //     this.currentUser.given_name = me_data.given_name ?? "";
    //     this.currentUser.family_name = me_data.family_name ?? "";
    //     this.currentUser.picture = me_data.picture ?? "";
    //     this.currentUser.refreshToken = me_data.refreshToken ?? "";
    //     this.currentUser.expires_in = me_data.expire_in ?? "";
    //     this.currentUser.access_token = me_data.access_token ?? "";

    //     if (this.currentUser.email) {
    //       const calUser = await calUsersService.getUserByEmail(this.currentUser.email)
    //       //  console.log("USER STORE CALUSER => ", calUser);
    //       if (calUser)
    //         this.calInfo = calUser
    //     }

    //     //  console.log("Current user after update:", this.currentUser, this.calInfo);

    //   } catch (err) {
    //     console.error("Error in init function:", err);
    //   }
    // },
    async init() {
      try {
        const data = await auth.getLocalUserInfo()
        //  console.log("🚀 ~ init ~ data:", data)

        if (!data) {
          console.error("No data received from local storage");
          return CSSFontFeatureValuesRule;
        }
        this.currentUser = data.user
        this.calInfo = data.cal_info
        this.currentEmailContext = data.email_context
        this.currentPhoneContext = data.phone_context
        return true
      } catch (err) {
        console.error("Error parsing data:", err);
        return false;
      }

    },
  }
});
