<template>
  <Section
    title="Account"
    :editable="true"
    @on-edit="editing = !editing"
    :editing="editing"
    @on-cancel="editing = false"
    @on-save="async () => await SaveCalData()"
  >
    <SectionItem v-for="(item, index) in settingsItems" :key="item.label" :position="index === 0 ? 'first' : ''">
      <SectionValue
        :name="item.label"
        :value="item.value"
        :type="item.type"
        :editing="['email', 'name'].includes(item.label.toLowerCase()) ? false : editing"
        @on-update="(attr, value) => updateCalData(attr, value)"
      />
    </SectionItem>
  </Section>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import Section from "../../../components/ui/Section/Section.vue";
import { useCurrentUserStore } from "../../../stores/currentUser";
import { storeToRefs } from "pinia";
import SectionItem from "../../../components/ui/Section/SectionItem.vue";
import SectionValue from "./SectionValue.vue";
import { CalUser } from "../../../models/user";
import { calUsersService } from "../../../services/cal-api/user-cal-service";
import auth from "../../../commands/auth";

const currentUserStore = useCurrentUserStore();
const { currentUser, calInfo } = storeToRefs(currentUserStore);

const editing = ref(false);

const userKeys = ["email", "name"];
const calInfoKeys = ["timeZone", "weekStart", "locale"];

const calEditData = ref<Partial<CalUser>>();

function updateCalData(attr: string, value: string) {
  calEditData.value = {
    ...calEditData.value,
    [attr]: value,
  };
}

watch(calEditData, (value) => {
  console.log("Cal Edit Data", value);
});

// Helper to format the label
const formatLabel = (key: string) => {
  return key
    .replace(/([A-Z])/g, " $1") // insert space before capitals
    .replace(/^./, (str) => str.toUpperCase()) // capitalize first letter
    .trim();
};

// Build the settings items as a combined list
const settingsItems = computed(() => {
  const items = [];

  if (currentUser && currentUser.value) {
    for (const key of userKeys) {
      if (currentUser.value[key as keyof typeof currentUser.value] !== undefined) {
        items.push({
          label: formatLabel(key),
          value: currentUser.value[key as keyof typeof currentUser.value],
        });
      }
    }
  }

  if (calInfo && calInfo.value) {
    for (const key of calInfoKeys) {
      if (calInfo.value[key as keyof typeof calInfo.value] !== undefined) {
        items.push({
          label: formatLabel(key),
          value: calInfo.value[key as keyof typeof calInfo.value],
          type: key.toLowerCase(),
        });
      }
    }
  }

  return items;
});

async function SaveCalData() {
  if (calEditData.value) {
    const result = await calUsersService.updateCurrentUser(calEditData.value);
    console.log("🚀 ~ SaveCalData ~ result:", result);
    if (result) {
      const res = await auth.updateCalInfo(result);
      console.log("🚀 ~ SaveCalData ~ res:", res);
    }
  }
  editing.value = false;
}

onMounted(() => {
  console.log("Loaded settings:", settingsItems.value);
});
</script>
