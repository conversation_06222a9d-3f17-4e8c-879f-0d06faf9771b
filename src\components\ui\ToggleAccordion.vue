<template>
  <div class="p-5 rounded-lg border-2 border-primary-200 flex flex-col gap-2">
    <div class="flex justify-between items-center">
      <div class="flex flex-col flex-1">
        <div class="font-semibold text-primary-700">{{ props.title }}</div>
        <div class="text-xs font-semibold text-slate-700" v-if="props.subTitle">{{ props.subTitle }}</div>
      </div>
      <div>
        <ToggleButton :id="props.id" :value="isOpen" @changed="(value) => (isOpen = value)" />
      </div>
    </div>
    <div
      :class="isOpen && props.canOpen ? 'h-auto px-2 py-4' : 'h-0 p-0'"
      class="flex flex-col overflow-hidden rounded-md bg-primary-200/40 transition-all duration-300"
    >
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import ToggleButton from "./ToggleButton.vue";

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  subTitle: {
    type: String,
  },
  open: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    required: true,
  },
  canOpen: {
    type: Boolean,
    default: true,
  },
});

const isOpen = ref(props.open);

const emit = defineEmits<{
  (e: "onToggle", value: boolean): void;
}>();

watch(isOpen, (value) => {
  emit("onToggle", value);
});
</script>

<style scoped></style>
