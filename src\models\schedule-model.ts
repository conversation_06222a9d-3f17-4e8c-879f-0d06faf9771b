import { CalAvailability } from "./availability-model"

export interface Schedule {
  title: string,
  description?: string,
  time: string,
  color?: string
}


export interface CalSchedule {
  id?: number,
  userId?: number,
  name: string,
  timeZone: string,
  availability: CalAvailability[]
}


export const defaultSelSchedule: CalSchedule = {
  availability: [],
  name: "",
  timeZone: "",
  userId: 0,
  id: 0,
};
