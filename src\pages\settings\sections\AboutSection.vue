<template>
  <Section title="About">
    <SectionItem v-for="(item, index) in links" :key="item.name" :position="index == 0 ? 'first' : ''">
      <div
        class="size-full flex items-center justify-between text-slate-600 cursor-pointer hover:text-slate-800"
        @click="async () => await open(item.url)"
      >
        <div class="flex items-center justify-center gap-1 text-sm">
          <Icon :icon="item.icon" class="size-6" />
          <div>{{ item.name }}</div>
        </div>
        <div>
          <ChevronRightIcon class="size-6" />
        </div>
      </div>
    </SectionItem>
    <SectionItem key="logout" :position="''">
      <div
        class="size-full flex items-center justify-between text-red-600 cursor-pointer hover:text-red-500"
        @click="async () => await auth.logout()"
      >
        <div class="flex items-center justify-center gap-1 text-sm">
          <Icon icon="material-symbols-light:logout-rounded" class="size-6" />
          <div>Logout</div>
        </div>
        <div>
          <ChevronRightIcon class="size-6" />
        </div>
      </div>
    </SectionItem>
  </Section>
</template>

<script setup lang="ts">
import { Icon } from "@iconify/vue/dist/iconify.js";
import Section from "../../../components/ui/Section/Section.vue";
import SectionItem from "../../../components/ui/Section/SectionItem.vue";
import { ChevronRightIcon } from "@heroicons/vue/20/solid";
import { open } from "@tauri-apps/plugin-shell";
import auth from "../../../commands/auth";

const links = [
  {
    name: "Help Center",
    url: "",
    icon: "material-symbols-light:help-outline",
  },
  {
    name: "Term of Use",
    url: "https://oway.life/termsofuse",
    icon: "material-symbols-light:tag-rounded",
  },
  {
    name: "Privacy Policy",
    url: "https://oway.life/privacypolicy",
    icon: "material-symbols-light:privacy-tip-outline-rounded",
  },
];
</script>
