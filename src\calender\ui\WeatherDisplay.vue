<template>
  <div class="size-full flex flex-col justify-start items-center">
    <div class="w-full h-full h-[calc(100%-24px)]x p-1 flex justify-around items-center grow">
      <div
        v-for="weather in weatherStore.data?.forecast.forecastday"
        class="w-48 h-auto flex flex-col justify-start items-start gap- vdrop-shadow-sm"
      >
        <!-- <div class="w-full flex justify-center">
          <div class="text-[8px] py-[1px] px-2 bg-secondary-400 text-white rounded-b-md">
            {{ todayIs(weather.date) }}
          </div>
        </div> -->
        <div class="flex justify-center items-center">
          <div class="size-16">
            <img :src="weather.day.condition.icon" alt="v" />
          </div>
          <div class="flex flex-col">
            <div class="font-poppins">
              <div class="text-sm font-semibold">{{ todayIs(weather.date) }}</div>
              <div class="font-bold text-2xl leading-6x">{{ weather.day.avgtemp_c }}°C</div>
              <p class="text-xs text-dark-400">{{ weather.day.condition.text }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="w-full flex justify-between px-2 text-xs py-1 text-dark-600 h-6">
      <div>{{ data?.location.name }},{{ data?.location.country }}</div>
      <div>{{ data?.location.localtime }}</div>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { useWeatherStore } from "../../stores/weatherStore";

// const data = ref<WeatherForecast>();

const weatherStore = useWeatherStore();

function todayIs(dateString: string) {
  const inputDate = new Date(dateString);
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  // Reset time part for accurate date comparison
  inputDate.setHours(0, 0, 0, 0);
  today.setHours(0, 0, 0, 0);
  tomorrow.setHours(0, 0, 0, 0);

  if (inputDate.getTime() === today.getTime()) {
    return "Today";
  } else if (inputDate.getTime() === tomorrow.getTime()) {
    return "Tomorrow";
  } else {
    // If not today or tomorrow, return the original date string
    return dateString;
  }
}

// onMounted(async () => {
//   // const pos = localStorage.getItem("gps") ? JSON.parse(localStorage.getItem("gps") ?? "") : null;
//   // const weather = await WeatherService.getWeather(pos ? `${pos.latitude},${pos.longitude}` : "algeria", 5);
//   // //  console.log("Weather", weather);
//   // data.value = weather?.data as WeatherForecast;
// });
</script>

<style scoped></style>
