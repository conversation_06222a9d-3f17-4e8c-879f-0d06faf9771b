use crate::commands::email_category_commands;
use crate::models::app_data::AppData;
use crate::models::email_category::NewEmailCategory;
use crate::models::user_data::User;
use crate::models::user_data::UserData;
use crate::services::emails_service;
use dotenv::dotenv;
use oauth2::basic::BasicTokenType;
use reqwest::Client;
// use rust_bert::pipelines::summarization::SummarizationModel;
use crate::commands::email_commands;
use crate::services::email_channel::get_email_sender;
use chrono::{ DateTime, Utc };
use serde_json::json;
use serde_json::Value;
use std::collections::HashMap;
use std::error::Error;
use tauri::{ Manager, RunEvent, WindowEvent };
use tokio::spawn;
use tokio::sync::Mutex;
use tokio::sync::MutexGuard;
use tracing::{ error, info, warn };

use crate::emails_service::save_email_summary;
use crate::enhancedFunctions::email_priority::categorize_email_from_metadata;
use crate::google_api_functions::fetch_emails::EmailSummary;
use crate::models::email::Email;
use crate::models::email_category::EmailCategory;
use chrono::NaiveDateTime;
use regex::Regex;

// use rust_bert::pipelines::ner::NERModel;
// use rust_bert::pipelines::sentiment::{SentimentModel, SentimentPolarity};

use oauth2::{
    basic::BasicClient,
    reqwest::async_http_client,
    revocation::StandardRevocableToken,
    AccessToken,
    AuthUrl,
    AuthorizationCode,
    ClientId,
    ClientSecret,
    CsrfToken,
    EmptyExtraTokenFields,
    PkceCodeChallenge,
    RedirectUrl,
    RefreshToken,
    RevocationUrl,
    Scope,
    StandardTokenResponse,
    TokenResponse,
    TokenUrl,
};

pub async fn parse_email_metadata(
    email_response: &Value
    // summarization_model: Arc<Mutex<SummarizationModel>>,
    // sentiment_model: Arc<Mutex<SentimentModel>>,
    // ner_model: Arc<Mutex<NERModel>>,
    // blacklisted_domains: &HashSet<String>, // Add blacklisted_domains parameter
) -> Result<Email, Box<dyn Error>> {
    // println!("Email Response: {}", email_response);
    let id = email_response["id"].as_str().ok_or("ID not found in email response")?.to_string();

    let headers = email_response["payload"]["headers"]
        .as_array()
        .ok_or("Headers not found in email response")?;

    let mut subject = String::new();
    let mut from = String::new();
    let mut to = String::new();
    let mut cc = String::new();
    let mut bcc = String::new();
    let mut date = NaiveDateTime::from_timestamp_opt(0, 0).unwrap();

    for header in headers {
        let header_name = header["name"].as_str().unwrap_or("Unknown");
        let header_value = header["value"].as_str().unwrap_or("Unknown");

        match header_name {
            "Subject" => {
                subject = header_value.to_string();
            }
            "From" => {
                from = header_value.to_string();
            }
            "To" => {
                to = header_value.to_string();
            }
            "Cc" => {
                cc = header_value.to_string();
            }
            "Bcc" => {
                bcc = header_value.to_string();
            }
            "Date" => {
                if let Ok(parsed_date) = chrono::DateTime::parse_from_rfc2822(header_value) {
                    date = parsed_date.naive_utc();
                }
            }
            _ => {}
        }
    }

    if from.is_empty() {
        return Err("From not found in email response".into());
    }

    let sender_domain = from.split('@').nth(1).unwrap_or("").to_string();

    let email_data =
        json!({
        "labelIds": email_response["labelIds"].as_array()
                      .map(|arr| arr.iter().map(|v| v.to_string()).collect::<Vec<String>>())
                      .unwrap_or_else(Vec::new), // Default to an empty array of strings if null
        "snippet": email_response["snippet"].as_str().unwrap_or("").to_lowercase(), // Default to an empty string if null
        "subject": subject.to_lowercase(),
        "sender_domain": sender_domain,
        "threadId": email_response["threadId"].as_str().unwrap_or(""), // Default to an empty string if null
        "attachments": email_response["payload"]["parts"].as_array()
                        .map(|arr| arr.iter().map(|v| v.to_string()).collect::<Vec<String>>())
                        .unwrap_or_else(Vec::new), // Default to an empty array of strings if null
    });

    let email_data_hashmap = match convert_json_to_hashmap_async(&email_data).await {
        Ok(map) => map,
        Err(e) => {
            eprintln!("Failed to convert JSON to HashMap: {}", e);
            return Err(Box::new(std::io::Error::new(std::io::ErrorKind::Other, e)));
            // Return an error
        }
    };
    // Pass the model to categorize_email_from_metadata
    let email_info: HashMap<String, Option<String>> = categorize_email_from_metadata(
        email_data_hashmap
        // summarization_model.clone(),
        // sentiment_model.clone(),
        // ner_model.clone(),
    ).await;

    let main_domain = extract_main_domain(
        &from.split('@').nth(1).unwrap_or("").trim_end_matches('>').to_string()
    );
    println!("Extracted main domain: {}", main_domain);
    let full_domain = extract_full_domain(
        &from.split('@').nth(1).unwrap_or("").trim_end_matches('>').to_string()
    );
    // Assign child_domain: if full_domain equals main_domain, assign empty string, otherwise assign full_domain
    let child_domain = if Some(full_domain.clone()) == Some(main_domain.clone()) {
        // "".to_string() // Empty string if full_domain equals main_domain
        full_domain.clone() // Full domain if they are not equal
    } else {
        full_domain.clone() // Full domain if they are not equal
    };

    // Create or update the email category based on the email's domain and other info
    email_category_commands::new_email_category(
        full_domain.clone(), // Provide a default empty string if None
        child_domain,
        extract_name(&from.clone()),
        extract_name(&from.clone()),
        main_domain.clone(),
        from.clone(),
        email_info
            .get("tags")
            .and_then(|v| v.as_deref())
            .unwrap_or("")
            .to_string(),
        Some(
            email_info
                .get("description")
                .and_then(|v| v.as_deref())
                .unwrap_or("")
                .to_string()
        ),
        Some(
            email_info
                .get("priority")
                .and_then(|v| v.as_deref().and_then(|s| s.parse::<i64>().ok()))
                .unwrap_or(0) as i32
        ),
        Some(
            email_info
                .get("color")
                .and_then(|v| v.as_deref())
                .unwrap_or("")
                .to_string()
        ),
        Some(
            if
                email_response["labelIds"]
                    .as_array()
                    .map_or(false, |labels| labels.contains(&json!("UNREAD")))
            {
                1
            } else {
                0
            }
        ),
        main_domain.clone(),
        Some(chrono::Utc::now().naive_utc().to_string()),
        Some(false)
    );

    // Extracting basic fields
    let id = email_response["id"].as_str().ok_or("ID not found in email response")?.to_string();

    let snippet = email_response["snippet"]
        .as_str()
        .ok_or("Snippet not found in email response")?
        .to_string();

    let headers = email_response["payload"]["headers"]
        .as_array()
        .ok_or("Headers not found in email response")?;

    // Extract specific headers
    let subject = headers
        .iter()
        .find(|header| header["name"] == "Subject")
        .and_then(|header| header["value"].as_str())
        .unwrap_or("")
        .to_string();

    let from = headers
        .iter()
        .find(|header| header["name"] == "From")
        .and_then(|header| header["value"].as_str())
        .unwrap_or("")
        .to_string();

    let to = headers
        .iter()
        .find(|header| header["name"] == "To")
        .and_then(|header| header["value"].as_str())
        .unwrap_or("")
        .to_string();

    let cc = headers
        .iter()
        .find(|header| header["name"] == "Cc")
        .and_then(|header| header["value"].as_str())
        .map(|s| s.to_string());

    let bcc = headers
        .iter()
        .find(|header| header["name"] == "Bcc")
        .and_then(|header| header["value"].as_str())
        .map(|s| s.to_string());

    // Parse the date from the header
    let date_str = headers
        .iter()
        .find(|header| header["name"] == "Date")
        .and_then(|header| header["value"].as_str())
        .unwrap_or("");

    let date = NaiveDateTime::parse_from_str(date_str, "%a, %d %b %Y %H:%M:%S %z")
        .or_else(|_| NaiveDateTime::parse_from_str(date_str, "%Y-%m-%dT%H:%M:%S.%fZ"))
        .map_err(|_| "Invalid date format in email response")?;

    // Extract other payload details
    let labels = email_response["labelIds"]
        .as_array()
        .map(|labels| {
            labels
                .iter()
                .filter_map(|l| l.as_str().map(|s| s.to_string()))
                .collect()
        })
        .unwrap_or_else(Vec::new);

    let attachments = email_response["payload"]["parts"]
        .as_array()
        .map(|parts| {
            parts
                .iter()
                .filter_map(|part| part["filename"].as_str().map(|s| s.to_string()))
                .collect()
        })
        .unwrap_or_else(Vec::new);

    let metadata_headers = headers
        .iter()
        .map(|header| serde_json::to_string(header).unwrap_or_default())
        .collect::<Vec<String>>();
    let user_email = extract_user_email(headers).ok_or("User email not found in headers")?;

    // let received_as = if to.contains(user_email) {
    //     "to".to_string()
    // } else if cc.contains(user_email) {
    //     "cc".to_string()
    // } else if bcc.unwrap().contains(user_email) {
    //     "bcc".to_string()
    // } else {
    //     "unknown".to_string()
    // };

    let email_type = if
        email_response["payload"]["headers"]
            .as_array()
            .map_or(false, |headers| headers.iter().any(|h| h["name"] == "From"))
    {
        "Incoming".to_string()
    } else {
        "Outgoing".to_string()
    };
    let parent_email_id = headers
        .iter()
        .find(|header| header["name"] == "In-Reply-To")
        .and_then(|header| header["value"].as_str())
        .map(|s| s.to_string());
    let meeting_proposed = snippet.contains("meeting") || snippet.contains("Zoom");

    let is_thread_root = match email_response["threadId"].as_str() {
        Some(_) =>
            match
                email_response["payload"]["headers"]
                    .as_array()
                    .and_then(|headers| {
                        headers.iter().find(|header| header["name"] == "In-Reply-To")
                    })
            {
                Some(_) => Some(false), // If "In-Reply-To" header exists, it's not the root.
                None => Some(true), // If "In-Reply-To" header doesn't exist, it's the root.
            }
        None => None, // If no threadId exists, this might not belong to a thread.
    };

    let email = Email {
        id: id.clone(),
        subject: subject.clone(),
        snippet: email_response["snippet"].as_str().unwrap_or("").to_lowercase(),
        from: from.clone(),
        to: serde_json::to_string(&to.split(",").collect::<Vec<_>>())?,
        cc: cc.map(|c|
            serde_json::to_string(&c.split(",").collect::<Vec<_>>()).unwrap_or_default()
        ),
        bcc: bcc.map(|b|
            serde_json::to_string(&b.split(",").collect::<Vec<_>>()).unwrap_or_default()
        ),

        date: date.clone(),
        category: Some(from.split('@').nth(1).unwrap_or("").trim_end_matches('>').to_string()),
        labels: Some(
            email_response["labelIds"]
                .as_array()
                .map(|label_array| {
                    label_array
                        .iter()
                        .filter_map(|label| label.as_str().map(|s| s.to_string()))
                        .collect::<Vec<String>>()
                        .join(", ") // Join the elements with ", " as separator
                })
                .unwrap_or_default()
        ),
        attachments: Some(serde_json::to_string(&attachments)?),
        attachment_types: Some(attachments.join(", ")),
        total_attachment_size: Some(attachments.len() as i32), // Placeholder size
        metadata_headers: Some(serde_json::to_string(&metadata_headers)?),

        email_body_url: email_info
            .get("email_body_url")
            .and_then(|v| v.as_deref())
            .map(|s| s.to_string()),
        is_read: Some(
            !email_response["labelIds"]
                .as_array()
                .map_or(false, |labels| labels.contains(&json!("UNREAD")))
        ),
        thread_id: email_response["threadId"].as_str().map(|s| s.to_string()),

        storage_location: email_info
            .get("storage_location")
            .and_then(|v| v.as_deref())
            .map(|s| s.to_string()),

        is_flagged: Some(
            email_info
                .get("is_flagged")
                .and_then(|v| v.as_deref().and_then(|s| s.parse::<bool>().ok()))
                .unwrap_or(false)
        ),
        urgency_score: email_info
            .get("urgency_score")
            .and_then(|v| v.as_deref().and_then(|s| s.parse::<i64>().ok()))
            .map(|v: i64| v as i32),
        sentiment: email_info
            .get("sentiment")
            .and_then(|v| v.as_deref())
            .map(|s| s.to_string()),
        actionable_items: email_info
            .get("actionable_items")
            .and_then(|v| v.as_deref())
            .map(|arr| {
                let items: Vec<String> = serde_json::from_str(arr).unwrap_or_default();
                serde_json::to_string(&items).unwrap_or_default()
            }),
        language: email_info
            .get("language")
            .and_then(|v| v.as_deref())
            .map(|s| s.to_string()),
        phishing_risk: email_info
            .get("phishing_risk")
            .and_then(|v| v.as_deref())
            .map(|s| s.to_string()),
        sender_reputation: email_info
            .get("sender_reputation")
            .and_then(|v| v.as_deref())
            .map(|s| s.to_string()),

        // Newly added fields:
        /*
        DROP TABLE IF EXISTS __diesel_schema_migrations;
        DROP TABLE IF EXISTS meetings;
        DROP TABLE IF EXISTS assistants;
        DROP TABLE IF EXISTS messages;
        DROP TABLE IF EXISTS email_categories;
        DROP TABLE IF EXISTS sessions;
        DROP TABLE IF EXISTS emails;
        DROP TABLE IF EXISTS tasks;
        DROP TABLE IF EXISTS subtasks;
        DROP TABLE IF EXISTS important_domains;
        Drop TABLE IF EXISTS drafts;
        DROP TABLE IF EXISTS email_logs;
                 */
        thread_summary: email_info
            .get("thread_summary")
            .and_then(|v| v.as_deref())
            .map(|s| s.to_string()),
        priority: email_info
            .get("priority")
            .and_then(|v| v.as_deref())
            .map(|s| s.to_string()),
        main_domain: Some(main_domain),
        full_domain: Some(full_domain),
        process_flag: Some(match
            email_info
                .get("process_flag")
                .and_then(|v| v.as_deref())
                .unwrap_or("")
        {
            "true" => true, // If the string is "true", return true
            "false" => false, // If the string is "false", return false
            _ => false, // Default to false if the value is not recognized
        }),
        email_type: Some("Incoming".to_string()),
        is_thread_root: is_thread_root,
        received_as: Some("to".to_string()), // Example hardcoded for now
        parent_email_id: parent_email_id,
        read_receipt_url: Some("".to_string()),
        reply_suggestion: Some("".to_string()),
        follow_up_date: Some(chrono::Utc::now().naive_utc()),
        meeting_proposed: Some(snippet.contains("meeting")),
        meeting_link: Some("".to_string()),
        is_delegated: Some(false),
        task_status: Some("".to_string()),
        auto_reply_sent: Some(false),
        flagged_keywords: Some("".to_string()),
        attachments_downloaded: Some(false),
        shared_with: Some("".to_string()),
        analytics_score: Some(60),
        response_time: Some(60),
        ai_generated: Some(false),
        is_send: Some(false),
        source_app: Some("Gmail".to_string()),
        created_at: Some(chrono::Utc::now().naive_utc()),
        updated_at: Some(chrono::Utc::now().naive_utc()),
    };
    if let Some(sender) = get_email_sender() {
        if let Err(e) = sender.send(email.clone()).await {
            eprintln!("❌ Failed to enqueue email: {}", e);
        } else {
            // let email_summary = EmailSummary {
            //     id: email.id.to_string().clone(),
            //     user_id: "zoziezo123".to_string(),
            //     subject: subject.clone().to_string(),
            //     snippet: "This is a sample insert into shard_ah".to_string(),
            //     thread_id: "thread_1".to_string(),
            //     date: Utc::now().to_rfc3339(),
            //     category: "needs_reply".to_string(),
            //     is_read: false,
            //     shard: "".to_string(),
            // };

            // // Spawn and log any errors
            // spawn(async move {
            //     if let Err(err) = save_email_summary(email_summary).await {
            //         eprintln!("🔥 Failed to send email summary to server: {:?}", err);
            //     }
            // });
            // println!("✅ Email {} enqueued for background processing", email.id);
        }
    } else {
        eprintln!("❌ Global email sender not initialized");
    }
    // emails_service::store_new_email(&email);
    // if let Err(e) = email_tx.send(email.clone()).await {
    // eprintln!("❌ Failed to enqueue email: {}", e);
    Ok(email)
}

fn extract_user_email(headers: &Vec<Value>) -> Option<String> {
    headers
        .iter()
        .find(
            |header| (header["name"] == "To" || header["name"] == "Cc" || header["name"] == "Bcc")
        )
        .and_then(|header| header["value"].as_str())
        .map(|email_list| {
            // Extract the first email from the list if multiple are present
            email_list
                .split(',')
                .map(|s| s.trim().to_string())
                .collect::<Vec<String>>()
                .first()
                .cloned()
        })
        .flatten()
}
// Helper function to extract main domain
fn extract_main_domain(email_or_domain: &str) -> String {
    let parts: Vec<&str> = email_or_domain.split('@').collect();
    let domain = if parts.len() > 1 { parts[1] } else { email_or_domain };
    let domain_parts: Vec<&str> = domain.split('.').collect();

    if domain_parts.len() >= 2 {
        let main_domain =
            domain_parts[domain_parts.len() - 2].to_string() + "." + domain_parts.last().unwrap();
        main_domain
    } else {
        domain.to_string()
    }
}

// Helper function to extract full domain (subdomain.domain.com)
fn extract_full_domain(email_or_domain: &str) -> String {
    let parts: Vec<&str> = email_or_domain.split('@').collect();
    if parts.len() > 1 {
        parts[1].to_string() // Return full domain part
    } else {
        email_or_domain.to_string() // It's already a domain
    }
}

async fn convert_json_to_hashmap_async(json: &Value) -> Result<HashMap<String, String>, String> {
    let mut map = HashMap::new();

    if let Some(obj) = json.as_object() {
        for (key, value) in obj {
            if value.is_null() {
                // println!("Skipping key: {} because the value is null", key);
                map.insert(key.clone(), String::new());
            } else if let Some(value_str) = value.as_str() {
                // println!("Inserting key: {} with value: {}", key, value_str);
                map.insert(key.clone(), value_str.to_string());
            } else {
                // println!("Inserting key: {} with value: {}", key, value.to_string());
                map.insert(key.clone(), value.to_string());
            }
        }
        Ok(map)
    } else {
        let error_message = format!("Error: Provided JSON is not an object. JSON: {:?}", json);
        Err(error_message)
    }
}

fn extract_name_company_domain(email: &str) -> (String, String, String) {
    println!("Inside extract_name_company_domain and extract company name \n {:#?}", email);

    // Regex to match the name before the email address and the domain within the email address
    let re = Regex::new(
        r"^(.*?)(?:\s*<)?([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})(?:>)?$"
    ).unwrap();

    if let Some(caps) = re.captures(email) {
        let name = caps
            .get(1)
            .map_or("", |m| m.as_str())
            .trim()
            .to_string();
        let domain = caps
            .get(3)
            .map_or("", |m| m.as_str())
            .to_string();
        let company = domain.split('.').next().unwrap_or("").to_string();
        return (name, company, domain);
    }

    ("Unknown".to_string(), "Unknown".to_string(), "unknown.com".to_string())
}

fn extract_name(from: &str) -> String {
    // Split by '<' and take the first part
    let name_part = from.split('<').next().unwrap_or("").trim();

    // Remove surrounding quotes if present
    let name = if name_part.starts_with('"') && name_part.ends_with('"') {
        name_part.trim_matches('"').to_string()
    } else {
        name_part.to_string()
    };

    name
}

// Function to store email metadata
fn store_email_metadata(email: &Email) -> Result<(), Box<dyn std::error::Error>> {
    // Insert the email metadata into the database
    emails_service::store_new_email(&email);

    Ok(())
}
