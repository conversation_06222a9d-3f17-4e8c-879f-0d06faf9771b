use crate::google_api_functions::refresh_access_token::refresh_access_token;
use crate::models::app_data::AppData;
use crate::models::user_data::User;
use crate::models::user_data::UserData;
use chrono::{ DateTime, Utc };
use dotenv::dotenv;
use oauth2::basic::BasicTokenType;
use reqwest::Client;
use serde_json::json;
use std::error::Error;
use std::sync::Arc;
use std::time::{ Duration, Instant };
use tauri::{ Manager, RunEvent, WindowEvent };
use tokio::sync::Mutex;
use tokio::sync::MutexGuard;
use tokio::sync::RwLock;
use tracing::{ error, info, warn };

use oauth2::{
    basic::BasicClient,
    reqwest::async_http_client,
    revocation::StandardRevocableToken,
    AccessToken,
    AuthUrl,
    AuthorizationCode,
    ClientId,
    ClientSecret,
    CsrfToken,
    EmptyExtraTokenFields,
    PkceCodeChallenge,
    RedirectUrl,
    RefreshToken,
    RevocationUrl,
    Scope,
    StandardTokenResponse,
    TokenResponse,
    TokenUrl,
};

pub async fn check_and_refresh_token_unread(
    app_data: Arc<RwLock<AppData>>
    // Match this with fetch_unread_emails
) -> Result<(), String> {
    let mut exprie_in_read: Option<Duration> = None;
    let mut issued_at_read: Option<DateTime<Utc>> = None;
    let mut refresh_token: Option<String> = None;
    let mut access_token: Option<String> = None;

    {
        let app_data_arc = app_data.read().await; // Acquire read lock
        let user_data = app_data_arc.user_data.read().await;

        exprie_in_read = user_data.expire_in.clone(); // Clone if `Duration` is needed later
        issued_at_read = user_data.issued_at.clone();
        refresh_token = user_data.refresh_token.as_ref().map(|t| t.secret().clone());
        access_token = user_data.access_token.as_ref().map(|t| t.secret().clone());
    } // Lock is released here automatically
    println!("Checking if access token needs refreshing...");
    if
        let (Some(issued_at), Some(expire_in), Some(refresh_token)) = (
            issued_at_read,
            exprie_in_read,
            refresh_token.clone(),
        )
    {
        println!(
            "My access tokent did exprie lets create a new access token \n {:#?}",
            refresh_token.clone()
        );
        let expiry_time = issued_at + expire_in;
        if chrono::Utc::now() >= expiry_time {
            println!("Access token expired, refreshing...");

            // Step 3: Refresh the token outside the lock scope
            match refresh_access_token(&refresh_token).await {
                Ok(new_token) => {
                    // Step 4: Acquire write lock to update user data
                    let mut app_data_write = app_data.write().await;
                    let mut user_data = app_data_write.user_data.write().await;

                    user_data.access_token = Some(oauth2::AccessToken::new(new_token));
                    user_data.set_issued_at(); // Update issued_at time
                    user_data.save_me(); // Save changes
                    access_token = user_data.access_token.as_ref().map(|t| t.secret().clone());

                    println!("Access token refreshed and updated.");
                }
                Err(err) => {
                    println!("Failed to refresh access token: {:?}", err);
                }
            }
        }
    }
    // if let (Some(issued_at), Some(expire_in)) = (user_data.issued_at, user_data.expire_in) {
    //     let expiry_time = issued_at + expire_in;

    //     // Check if the access token is expired
    //     if chrono::Utc::now() >= expiry_time {
    //         println!("Access token expired. Refreshing...");

    //         // Call the refresh_access_token function
    //         let new_token_result =
    //             refresh_access_token(&user_data.refresh_token.as_ref().unwrap().secret()).await;

    //         if let Ok(new_token) = new_token_result {
    //             println!("New access token obtained: {:#?}", new_token);

    //             // Update the access token
    //             user_data.access_token = Some(new_token);

    //             // Assuming you need to update issued_at manually
    //             user_data.issued_at = Some(chrono::Utc::now());

    //             // If save_me is needed, ensure it's implemented
    //             // user_data.save_me(); // Implement this method if necessary
    //         } else {
    //             println!(
    //                 "Failed to refresh the access token: {:#?}",
    //                 new_token_result.as_ref().err() // Use `.as_ref()` to borrow the error without moving
    //             );
    //             return Err(format!(
    //                 "Failed to refresh token: {}",
    //                 new_token_result.err().unwrap() // This `.unwrap()` is safe because we're inside the `else` block
    //             ));
    //         }
    //     } else {
    //         println!("Access token is still valid.");
    //     }
    // } else {
    //     println!("No issued_at or expire_in found in user_data.");
    // }

    Ok(())
}
