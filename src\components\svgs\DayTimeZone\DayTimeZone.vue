<template>
  <div class="relative group">
    <div ref="drawing" class=""></div>
    <component
      v-if="timePos"
      :is="timePos.icon"
      class="peer absolute size-5 cursor-pointer z-50"
      :style="{ left: timePos.x + 'px', top: timePos.y - 25 + 'px', color: timePos.color }"
    />

    <div
      v-if="timePos"
      class="absolute group-hover:flex hidden text-yellow-500 bg-black/80 p-[1px] px-[1.5px] text-[10px] rounded justify-center items-center font-semibold"
      :style="{ left: timePos.x + 20 + 'px', top: timePos.y - 22 + 'px' }"
    >
      {{ timePos.value }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import { SVG } from "@svgdotjs/svg.js";
import { parseTimeString } from "../../../utils/utils";
import { MoonIcon, SunIcon } from "@heroicons/vue/24/solid";
import { useWeatherStore } from "../../../stores/weatherStore";
import { useCurrentUserStore } from "../../../stores/currentUser";

// const props = defineProps({
//   time: { type: String, required: true },
//   theme: { type: String, default: "dark" },
// });

const weatherStore = useWeatherStore();
const session = useCurrentUserStore();

const drawing = ref();
const timePos = ref<{
  x: number;
  y: number;
  icon: any;
  color: string;
  value: string;
}>();

function drawSunArc(width: number, height: number, radius: number, sunriseTime: string, sunsetTime: string) {
  // Clear previous SVG
  drawing.value.innerHTML = "";

  // Create the SVG canvas
  const draw = SVG().addTo(drawing.value).size(width, height);

  // Define start (00:00) and end points (24:00)
  let startX = 50;
  let startY = height - 30;
  let endX = width - 50;
  let endY = height - 30;
  let midX = (startX + endX) / 2;
  let midY = startY - radius;

  // Convert time (hh:mm AM/PM) to decimal hours
  function timeToDecimal(time: string) {
    let [hours, minutes] = time.split(/[: ]/).map(Number);
    if (time.includes("PM") && hours !== 12) hours += 12;
    if (time.includes("AM") && hours === 12) hours = 0;
    return hours + minutes / 60;
  }

  // Convert times to 24-hour format
  let sunriseHour = timeToDecimal(sunriseTime);
  let sunsetHour = timeToDecimal(sunsetTime);

  // Calculate positions in percentage (0 = midnight, 1 = next midnight)
  let sunrisePos = sunriseHour / 24;
  let sunsetPos = sunsetHour / 24;

  //  console.log("(", sunrisePos, sunsetPos, ")");

  // Dynamic gradient based on sunrise & sunset
  let gradient = draw.gradient("linear", function (add) {
    add.stop(0, "#16166B"); // Midnight
    add.stop(0.1, "#16166Baa"); // Midnight
    add.stop(sunrisePos * 0.9, "#EFA514aa"); // Before sunrise
    add.stop(sunrisePos, "#72A3D7"); // Sunrise
    add.stop(sunsetPos, "#72A3D7aa"); // Noon - Sunset
    // add.stop(sunsetPos * 0.9, "#EFA514bb"); // Noon - Sunset
    add.stop(sunsetPos * 1, "#EFA514"); // After sunset
    add.stop(1, "#16166B"); // Midnight
  });

  // Create the curved path
  let p = `M ${startX} ${startY} Q ${midX} ${midY}, ${endX} ${endY}`;
  const path = draw.path(p).stroke({ width: 6, color: gradient.url(), linecap: "round" }).fill("none");

  // let sun = draw.circle(16).fill("orange").stroke({ width: 2, color: "red" });

  // Function to update sun position based on current time
  function updateSunPosition() {
    let now = new Date();
    let currentHour = now.getHours() + now.getMinutes() / 60;
    let progress = currentHour / 24; // Convert to percentage of 24-hour cycle
    const length = path.length();
    const p = path.node.getPointAtLength(progress * length);
    //  console.log("$$$$", now, progress, length, p, session.calInfo?.timeZone);
    const formatter = new Intl.DateTimeFormat("en-US", {
      timeZone: session.calInfo?.timeZone ? session.calInfo?.timeZone : "America/New_York",
      hour: "numeric",
      minute: "numeric",
      second: "numeric",
      hour12: true,
    });
    timePos.value = {
      x: p.x,
      y: p.y,
      icon: currentHour > sunriseHour && currentHour < sunsetHour + 1 ? SunIcon : MoonIcon,
      color: currentHour > sunriseHour && currentHour < sunsetHour + 1 ? "#EFA514" : "#16166B",
      value: formatter.format(now),
    };
    // Move the sun smoothly
    // sun.animate(1000).center(p.x, p.y - 17); //.center(sunX, sunY);
  }

  // Initial update
  updateSunPosition();

  // Update sun position every `updateInterval` ms
  setInterval(updateSunPosition, 15000);
}

watch(timePos, (value) => {
  //  console.log("TimePos", value);
});

function draw() {
  if (
    weatherStore.data.forecast &&
    weatherStore.data.forecast.forecastday &&
    weatherStore.data.forecast.forecastday[0]
  ) {
    let weather = weatherStore.data.forecast.forecastday[0];
    //  console.log("Weather", weather.astro);
    drawSunArc(400, 100, 50, weather.astro.sunrise, weather.astro.sunset);
  }
}

weatherStore.$subscribe(() => {
  draw();
});

onMounted(() => {
  draw();
});
</script>

<style scoped></style>
