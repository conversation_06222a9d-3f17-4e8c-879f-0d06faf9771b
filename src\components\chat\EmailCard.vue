<template>
  <div
    :class="[
      'w-full px-2 py-3 rounded',
      !email.is_read
        ? 'bg-primary-800/10 hover:bg-primary-800/20 border-l-4 border-primary-800 hover:shadow-sm'
        : 'bg-primary-600/10 hover:bg-primary-600/20 font-normal',
    ]"
  >
    <div
      class="group flex items-start justify-between gap-2 w-full h-full"
      @click="() => emit('viewEmail', emails as Email[])"
    >
      <!-- Checkbox for selecting individual emails -->
      <div
        @click="
          (e) => {
            e.stopPropagation();
            e.stopImmediatePropagation();
            isSelected ? emit('unselectEmail', emails) : emit('selectEmail', emails);
          }
        "
        class="pt-1"
      >
        <div class="size-5 rounded-full border border-base-500 cursor-pointer" :class="{ hidden: isSelected }"></div>
        <div
          :class="isSelected ? 'flex' : 'hidden'"
          class="peer-checkedx:flex justify-center items-center hiddenx size-5 rounded-full border-[1.5px] border-dark-400 bg-dark-400 text-white cursor-pointer"
        >
          <CheckIcon class="size-3" />
        </div>
      </div>
      <div class="w-full flex flex-col gap-1">
        <div class="flex justify-between items-center">
          <h3
            class="text-md text-base-500 group-hover:text-dark-400"
            :class="email.is_read ? 'font-bold' : 'font-semibold'"
          >
            {{ email.subject ? email.subject.replace(/"/g, "") : "( NO SUBJECT )" }}
          </h3>
          <!-- <div v-if="email.is_read">
            <EnvelopeIcon class="w-5 h-5 text-gray-500" />
          </div> -->
        </div>
        <div
          v-if="email.thread_summary && email.storage_location != ''"
          class="h-0 group-hover:h-16 max-h-40 overflow-auto custom-scrollbar transition-all duration-300 bg-primary-500/70 text-dark-700 rounded"
        >
          <div class="p-2">
            <p class="text-[10px] text-dark-500">Summary</p>
            <p class="text-xs text-dark-400">
              {{ email.thread_summary }}
            </p>
          </div>
        </div>
        <div class="flex text-sm justify-between text-slate-600 mt-1">
          <div class="flex gap-4">
            <div
              v-if="emails.length > 1"
              :title="`There is ${emails.length - 1} replys`"
              class="flex items-center gap-2 font-semibold"
            >
              <RectangleStackIcon class="size-4" />
              <div>{{ emails.length - 1 }}</div>
            </div>
          </div>
          <span v-if="email.is_read" class="text-sm text-gray-400">
            {{ formatGmailDate(email.date) }}
          </span>
          <span v-else class="text-sm text-gray-400">{{ formatGmailDate(emails[0].date.toString()) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onUpdated, ref, watch, computed } from "vue";
import { Email } from "../../types";
import { CheckIcon, RectangleStackIcon, EnvelopeIcon } from "@heroicons/vue/24/outline";

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  selected: {
    type: Boolean,
    default: false,
  },
});

const emails = ref<Email[]>((props.data as Email[]) ?? []);
const isSelected = computed(() => props.selected);

const emit = defineEmits(["selectEmail", "unselectEmail", "viewEmail"]);
const email = computed<Email>(() => {
  const unreadEmails = emails.value.filter((email: Email) => !email.is_read);
  return unreadEmails.length > 0 ? unreadEmails[0] : emails.value[0];
});

function formatGmailDate(dateString: string) {
  const now = new Date();
  const emailDate = new Date(dateString);

  const timeDiff = now.getTime() - emailDate.getTime();
  const oneDay = 24 * 60 * 60 * 1000; // milliseconds in a day

  // If the email was today, show the time like "12:22 AM"
  if (timeDiff < oneDay && now.getDate() === emailDate.getDate()) {
    return emailDate.toLocaleTimeString(undefined, { hour: "2-digit", minute: "2-digit" });
  }

  // If it was yesterday, show "Yesterday"
  if (timeDiff < 2 * oneDay && now.getDate() - emailDate.getDate() === 1) {
    return "Yesterday";
  }

  // If it's within the same year, show the month and day like "Sep 5"
  if (now.getFullYear() === emailDate.getFullYear()) {
    return emailDate.toLocaleDateString(undefined, { month: "short", day: "numeric" });
  }

  // Otherwise, show the full date like "Sep 5, 2023"
  return emailDate.toLocaleDateString(undefined, { year: "numeric", month: "short", day: "numeric" });
}
</script>

<style scoped></style>
