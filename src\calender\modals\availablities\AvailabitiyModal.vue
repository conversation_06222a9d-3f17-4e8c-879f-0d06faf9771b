<template>
  <div class="size-full flex justify-between items-center pb-2">
    <SideBar
      :schedules="schedules"
      @close="() => emit('close')"
      @select-schedule="(s:CalSchedule)=>selectSchedule(s)"
      @delete-schedule="(s:CalSchedule)=>deleteSchedule(s)"
      @new-schedule="
        selSchedule = defaultSelSchedule;
        editableSch = { ...defaultSelSchedule };
      "
    />

    <div class="w-3/4 h-full py-2 px-4 flex flex-col gap-4 overflow-y-auto custom-scrollbar bg-primary-400/20">
      <!-- Header Section -->
      <div
        class="w-full flex justify-between items-start bg-primary p-4 rounded-lg shadow-sm border border-primary-300"
      >
        <div class="flex-1 h-full">
          <div
            class="h-full flex gap-3 items-center group"
            v-if="!nameOnEdit && editableSch?.name"
            @dblclick="nameOnEdit = true"
          >
            <h1 class="text-2xl font-bold text-base-500 capitalize">{{ editableSch?.name }}</h1>
            <button
              class="group-hover:flex hidden p-1.5 rounded-lg bg-primary-200 hover:bg-primary-300 cursor-pointer transition-colors duration-200"
              @click="nameOnEdit = true"
            >
              <PencilIcon class="w-4 h-4 text-secondary-600" />
            </button>
            <span
              class="text-xs bg-highlight-500 text-primary px-2 py-1 rounded-full font-medium"
              v-if="session.calInfo?.defaultScheduleId === editableSch.id"
            >
              Default
            </span>
          </div>
          <div v-else class="flex items-end">
            <div class="relative">
              <p class="text-secondary-600 mb-1 text-sm font-semibold">Configure your weekly availability schedule</p>
              <div
                class="flex items-center rounded-lg overflow-hidden border border-primary-400 focus:border-secondary-600 transition-all duration-200"
              >
                <input
                  class="h-11 bg-primary border-none px-3 text-xl font-bold text-base-500 outline-none"
                  type="text"
                  v-model="editableSch.name"
                  name="name"
                  autofocus
                  @keydown.enter="nameOnEdit = false"
                  placeholder="Schedule Name"
                />
                <div class="flex items-center border-1">
                  <button
                    class="h-11 px-1 text-accent-600 bg-primary-400 hover:bg-accent-100 transition-colors duration-200"
                    @click="nameOnEdit = false"
                  >
                    <CheckIcon class="size-5" />
                  </button>
                  <button
                    class="h-11 px-1 text-accent-600 bg-primary-400 hover:bg-accent-100 transition-colors duration-200"
                    @click="
                      editableSch.name = selSchedule.name;
                      nameOnEdit = false;
                    "
                  >
                    <XMarkIcon class="size-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="w-60 ml-4">
          <label class="block text-sm font-medium text-secondary-700 mb-1">Time Zone</label>
          <TimeZoneSelect :value="editableSch.timeZone" @select="(value:string)=> editableSch.timeZone = value" />
        </div>
      </div>
      <!-- Weekly Schedule Section -->
      <div class="bg-primary rounded-lg shadow-sm border border-primary-300 p-4">
        <h2 class="text-lg font-semibold text-base-500 mb-4">Weekly Availability</h2>
        <div class="space-y-2">
          <div
            class="group bg-primary-200 hover:bg-primary-300 border border-primary-400 rounded-lg p-2.5 transition-all duration-200"
            v-for="(value, index) of DAYS"
            :key="`${editableSch.name}-${index}`"
            :class="{ 'bg-secondary-100 border-secondary-300': dayActive(index) }"
          >
            <!-- Day Header -->
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center gap-3">
                <ToggleButton
                  :id="`${value}-${index}`"
                  :value="dayActive(index)"
                  @changed="(val:boolean) => ToggleDayState(index, val)"
                />
                <h3 class="text-sm font-medium text-base-500">{{ value }}</h3>
              </div>
              <div v-if="dayActive(index)" class="text-xs text-secondary-600">
                {{ availability.days[index]?.length || 0 }} slot{{
                  (availability.days[index]?.length || 0) !== 1 ? "s" : ""
                }}
              </div>
            </div>

            <!-- Time Slots -->
            <div v-if="dayActive(index)" class="space-y-1.5 ml-8">
              <div
                class="flex items-center gap-2.5 p-2 bg-primary rounded-lg border border-primary-400"
                v-if="availability.days[index]"
                v-for="(av, i) of availability.days[index]"
                :key="i"
              >
                <div class="flex items-center gap-2.5">
                  <TimeSelector
                    :value="av.startTime"
                    @select="(value:string)=>{
                      modifyTime(index,av.startTime,av.endTime,value,av.endTime)
                    }"
                  />
                  <span class="text-secondary-500 font-medium">to</span>
                  <TimeSelector
                    :value="av.endTime"
                    @select="(value:string)=>{
                      modifyTime(index,av.startTime,av.endTime,av.startTime,value)
                    }"
                  />
                </div>
                <div class="flex gap-1.5 ml-auto">
                  <button
                    v-if="i <= 0"
                    @click="createNewTime(index)"
                    class="p-1.5 bg-secondary-200 text-secondary-700 hover:bg-secondary-300 rounded-lg transition-colors duration-200"
                    title="Add another time slot"
                  >
                    <PlusIcon class="w-4 h-4" />
                  </button>
                  <button
                    v-if="i > 0"
                    @click="removeTime(index, av.startTime, av.endTime)"
                    class="p-1.5 bg-accent-200 text-accent-700 hover:bg-accent-300 rounded-lg transition-colors duration-200"
                    title="Remove this time slot"
                  >
                    <TrashIcon class="w-4 h-4" />
                  </button>
                </div>
              </div>

              <!-- Default time slot when day is active but no times set -->
              <div
                v-if="!availability.days[index]"
                class="flex items-center gap-2.5 p-2 bg-primary rounded-lg border border-primary-400"
              >
                <div class="flex items-center gap-2.5">
                  <TimeSelector :value="'09:00 AM'" />
                  <span class="text-secondary-500 font-medium">to</span>
                  <TimeSelector :value="'05:00 PM'" />
                </div>
                <button
                  @click="createNewTime(index)"
                  class="p-1.5 bg-secondary-200 text-secondary-700 hover:bg-secondary-300 rounded-lg transition-colors duration-200 ml-auto"
                  title="Add time slot"
                >
                  <PlusIcon class="w-4 h-4" />
                </button>
              </div>
            </div>

            <!-- Unavailable message -->
            <div v-else class="ml-8 text-secondary-500 italic text-xs">Not available on this day</div>
          </div>
        </div>
      </div>
      <!-- Action Buttons -->
      <div class="bg-primary rounded-lg shadow-sm border border-primary-300 p-4">
        <div class="flex justify-between items-center">
          <div class="text-sm text-secondary-600">
            <span v-if="selSchedule.id === 0">Create a new schedule with your availability preferences</span>
            <span v-else>Save changes to update your availability schedule</span>
          </div>
          <div class="flex gap-2">
            <button
              @click="setScheduleAsDefault"
              v-if="session.calInfo?.defaultScheduleId !== editableSch.id && selSchedule.id !== 0"
              class="flex items-center gap-2 px-3 py-2 bg-primary-200 text-secondary-700 rounded-lg hover:bg-primary-300 transition-colors duration-200 font-medium"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              Set as Default
            </button>
            <button
              :key="state"
              @click="() => (selSchedule.id === 0 ? createSchedule() : saveSchedule())"
              :disabled="state === 'loading'"
              class="flex items-center gap-2 px-4 py-2 bg-highlight-500 text-primary rounded-lg hover:bg-highlight-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium min-w-[100px] justify-center"
            >
              <div v-if="state === 'none'" class="flex items-center gap-2">
                <svg v-if="selSchedule.id === 0" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
                  ></path>
                </svg>
                {{ selSchedule.id == 0 ? "Create Schedule" : "Save Changes" }}
              </div>
              <div v-else-if="state === 'loading'" class="flex items-center gap-2">
                <ArrowPathIcon class="w-4 h-4 animate-spin" />
                {{ selSchedule.id == 0 ? "Creating..." : "Saving..." }}
              </div>
              <div v-else-if="state === 'done'" class="flex items-center gap-2">
                <CheckIcon class="w-4 h-4" />
                {{ selSchedule.id == 0 ? "Created!" : "Saved!" }}
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { CalSchedule, defaultSelSchedule } from "../../../models/schedule-model";
import { calSchedulesService } from "../../../services/schedules-cal-service";
import { useCurrentUserStore } from "../../../stores/currentUser";
import { ArrowPathIcon, CheckIcon, PencilIcon, PlusIcon, TrashIcon, XMarkIcon } from "@heroicons/vue/24/outline";
import ToggleButton from "../../../components/ui/ToggleButton.vue";
import {
  addDayToAvailability,
  AvailabilityChanges,
  AvailGroups,
  compareScheduleAvailabilities,
  groupAvailability,
  manageDayAvailability,
  moveDayAvailability,
  removeDayFromAvailability,
  timeToISOString,
} from "../../../utils/availability-utils";
import { CalAvailability, DAYS } from "../../../models/availability-model";
import TimeSelector from "../../../components/ui/TimeSelector.vue";
import { calAvailabilityService } from "../../../services/availablity-cal-service";
import { ask } from "@tauri-apps/plugin-dialog";
import { calUsersService } from "../../../services/user-cal-service";
import SideBar from "./SideBar.vue";
import TimeZoneSelect from "../../../components/ui/TimeZoneSelect.vue";
// import { DAYS } from "../../models/availability-model";

const emit = defineEmits(["close"]);

const schedules = ref<CalSchedule[]>([]);
const selSchedule = ref<CalSchedule>(defaultSelSchedule);
const session = useCurrentUserStore();
const nameOnEdit = ref(false);
const editableSch = ref<CalSchedule>(defaultSelSchedule);
const availability = computed<AvailGroups>(() => {
  //  console.log("SELECTED SCHEDULE", editableSch.value);
  if (editableSch.value) return groupAvailability(editableSch.value.availability);
  return { days: {}, dates: {} };
});

async function getSchedules() {
  if (session.calInfo?.id) schedules.value = (await calSchedulesService.getUserSchedules(session.calInfo?.id)) ?? [];
  console.log("🚀 ~ getSchedules ~ schedules.value:", schedules.value);
}

function selectSchedule(s: CalSchedule) {
  selSchedule.value = s;
  editableSch.value = { ...s };
}

function ToggleDayState(day: number, active: boolean) {
  const result = manageDayAvailability(editableSch.value, day, active);
  // //  console.log(result);
  editableSch.value = result;
}

function createNewTime(day: number) {
  const result = addDayToAvailability(editableSch.value, day, "none", "none");
  //  console.log("Create Time =>", result);
  editableSch.value = result;
}

function removeTime(day: number, start: string, end: string) {
  const result = removeDayFromAvailability(editableSch.value, day, start, end);
  //  console.log("Create Time =>", result);
  editableSch.value = result;
}

function modifyTime(day: number, oldStart: string, oldEnd: string, newStart: string, newEnd: string) {
  const result = moveDayAvailability(editableSch.value, day, oldStart, oldEnd, newStart, newEnd);
  //  console.log("Create Time =>", result);
  editableSch.value = result;
}

function dayActive(index: number) {
  const active = Object.keys(availability.value.days ?? []).includes(index.toString());
  // //  console.log(`${index} Day ${DAYS[index]} is Active ? ${active}`);
  return active;
}

const state = ref<"none" | "loading" | "done">("none");

async function deleteSchedule(sch: CalSchedule) {
  const cnf = await ask("This action cannot be reverted. Are you sure?", {
    title: "Delete schedule",
  });
  if (cnf && sch.id) {
    await calSchedulesService.deleteSchedule(sch.id);
    await getSchedules();
  }
}

async function setScheduleAsDefault() {
  await calUsersService.updateCurrentUser({ defaultScheduleId: editableSch.value.id }).then(async () => {
    // await session.updateCurrentCalUser();
    await getSchedules();
    editableSch.value = { ...editableSch.value };
  });
}

async function saveSchedule() {
  state.value = "loading";
  const changes: AvailabilityChanges = compareScheduleAvailabilities(selSchedule.value, editableSch.value);
  //  console.log("###### START ######", changes);
  changes.toCreate.forEach(async (change) => {
    state.value = "loading";
    let obj: CalAvailability = {
      ...change,
      endTime: timeToISOString(change.endTime),
      startTime: timeToISOString(change.startTime),
    };
    await calAvailabilityService.createAvailability(obj);
    state.value = "done";
  });

  changes.toUpdate.forEach(async (change) => {
    state.value = "loading";
    let obj: CalAvailability = {
      ...change,
      endTime: timeToISOString(change.endTime),
      startTime: timeToISOString(change.startTime),
    };
    delete obj.date;
    delete obj.eventTypeId;
    await calAvailabilityService.updateAvailability(obj.id!, obj);
    state.value = "done";
  });

  changes.toRemove.forEach(async (change) => {
    state.value = "loading";
    await calAvailabilityService.removeAvailability(change.id!);
    state.value = "done";
  });

  if (editableSch.value.name !== selSchedule.value.name || editableSch.value.timeZone !== selSchedule.value.timeZone) {
    state.value = "loading";
    await calSchedulesService.updateSchedule(selSchedule.value.id!, {
      name: editableSch.value.name ?? selSchedule.value.name,
      timeZone: editableSch.value.timeZone ?? selSchedule.value.timeZone,
    });
    state.value = "done";
  }

  setTimeout(async () => {
    state.value = "none";
    await getSchedules();
  }, 2000);
  //  console.log("###### END ######");
}

async function createSchedule() {
  //  console.log("New Schedule:");
  //  console.log("Name:", editableSch.value.name);
  //  console.log("Time Zone:", editableSch.value.timeZone);
  //  console.log("Calendar Info ID:", session.calInfo?.id);

  // Combined output for clarity
  //  console.log("New Schedule (Combined):", editableSch.value.name && editableSch.value.timeZone && session.calInfo?.id);

  if (editableSch.value.name && editableSch.value.timeZone && session.calInfo?.id) {
    state.value = "loading";
    const newSch = await calSchedulesService.createSchedule({
      name: editableSch.value.name,
      timeZone: editableSch.value.timeZone,
      userId: session.calInfo?.id,
    });
    //  console.log("New Schedule", newSch);
    if (newSch) {
      //  console.log("YAY");
      editableSch.value.id = newSch.id;
      selSchedule.value = newSch;
      //editableSch.value.availability = editableSch.value.availability.map((av) => ({ ...av, scheduleId: newSch.id }));
      for (const av of newSch.availability) {
        if (av.id) await calAvailabilityService.removeAvailability(av.id);
      }
      //  console.log("editable ====>", editableSch.value.availability, "schID", newSch.id);
      for (const av of editableSch.value.availability) {
        const obj: CalAvailability = {
          ...av,
          scheduleId: newSch.id,
          startTime: timeToISOString(av.startTime),
          endTime: timeToISOString(av.endTime),
        };
        //  console.log("AV=>", obj);
        await calAvailabilityService.createAvailability(obj);
        //  console.log("RES=>", res);
      }
    }
    state.value = "done";
    setTimeout(async () => {
      state.value = "none";
      await getSchedules();
    }, 2000);
  }
}

watch(
  selSchedule,
  () => {
    // Handle schedule selection changes if needed
  },
  { deep: true }
);

onMounted(async () => {
  await getSchedules();
});
</script>

<style scoped></style>
