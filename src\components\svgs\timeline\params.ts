const startX = 0; // left margin
const startStraight = 50; // length of straight line at start
const endStraight = 50; // length of straight line at end
const waveWidth = 220; // horizontal width for each hour segment
const yMid = 300; // baseline for the curved section
const amplitude = 160; // vertical deviation of each curve
const marginLabel = 20; // extra distance for placing the hour label

export {
  startX, startStraight, endStraight, waveWidth,
  yMid, amplitude, marginLabel
}
