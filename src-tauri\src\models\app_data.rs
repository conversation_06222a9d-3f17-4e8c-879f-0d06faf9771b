use crate::models::user_data::UserData;
use tokio::sync::Mutex;

use crate::models::phone_context::PhoneCallContext;
use crate::models::user_perference::EmailContext;
use std::sync::Arc;
use tokio::sync::Notify;
use tokio::sync::RwLock;
use tokio_util::sync::CancellationToken;
// use tokio::sync::RwLock;

/// # AppData
/// is managed via the tauri app
pub struct AppData {
    pub user_data: Arc<RwLock<UserData>>,
    pub logged_in: RwLock<bool>,
}
