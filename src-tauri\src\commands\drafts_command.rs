use crate::{
    models::draft::Draft,
    services::{drafts_services, tasks_services},
};

#[tauri::command]
pub fn task_draft(task_id: i32) -> Option<Draft> {
    drafts_services::get_draft_task(task_id)
}

#[tauri::command]
pub fn thread_drafts(thread_id: String) -> Vec<Draft> {
    drafts_services::get_thread_drafts(thread_id)
}

#[tauri::command]
pub fn thread_waiting_drafts(thread_id: String) -> Vec<Draft> {
    drafts_services::get_thread_waiting_drafts(thread_id)
}

#[tauri::command]
pub fn waiting_drafts() -> Vec<Draft> {
    drafts_services::get_waiting_drafts()
}

#[tauri::command]
pub fn draft_status(draft_id: String, status: &str) -> Result<(), String> {
    drafts_services::update_draft_status(draft_id.clone(), status);
    let draft = drafts_services::get_draft(draft_id);
    match draft {
        Some(d) => match d.task_id {
            Some(task_id) => {
                tasks_services::update_task_status(task_id, "completed");
                Ok(())
            }
            None => Err("Draft not associated with any task!".into()),
        },
        None => Err("Error Updating task status".into()),
    }
}
