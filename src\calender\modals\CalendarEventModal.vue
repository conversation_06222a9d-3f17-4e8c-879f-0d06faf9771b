<template>
  <div class="absolute size-full flex justify-center items-center text-dark-800">
    <div class="w-1/2 min-h-40 gap-2 flex flex-col rounded-md drop-shadow h-auto bg-primary-200">
      <div class="flex justify-between items-center p-1">
        <div>
          <div
            class="text-[10px] px-1.5 py-0.5 rounded-lg"
            :class="{
              'text-green-50 bg-green-600': event.booking.status === 'ACCEPTED',
              'text-red-50 bg-red-600': ['CANCELLED', 'REJECTED'].includes(event.booking.status ?? ''),
              'text-orange-50 bg-orange-600': event.booking.status === 'PENDING',
            }"
          >
            {{ event.booking.status === "PENDING" ? "WAITING CONFIRMATION" : event.booking.status }}
          </div>
        </div>
        <button
          class="text-secondary-600 hover:text-red-600 transition-colors duration-200"
          @click="() => emit('close')"
        >
          <XMarkIcon class="size-5" />
        </button>
      </div>
      <div v-if="event.booking" class="flex flex-col p-4 pt-0 gap-2">
        <div class="flex items-center gap-2">
          <div class="flex justify-center items-center size-9 bg-secondary-100 text-secondary-500 rounded-full">
            <BookmarkIcon class="size-6" />
          </div>
          <div>{{ event.title }}</div>
        </div>
        <div class="flex items-start gap-2 pt-1">
          <div class="flex justify-center items-center size-9 bg-secondary-100 text-secondary-500 rounded-full">
            <ClockIcon class="size-6" />
          </div>
          <div>
            <p>{{ when.date }}</p>
            <p>{{ when.startTime }} - {{ when.endTime }}</p>
            <div class="flex items-center text-sm gap-1">
              <GlobeAltIcon class="size-4" />
              <p>{{ event.booking.user.timeZone }}</p>
            </div>
          </div>
        </div>

        <div class="flex items-start gap-2 pt-1">
          <div class="flex justify-center items-center size-9 bg-secondary-100 text-secondary-500 rounded-full">
            <UserIcon class="size-6" />
          </div>
          <div class="flex flex-col gap-1">
            <div class="flex flex-col">
              <div class="flex gap-2 items-center">
                <div>{{ event.booking.user.name }}</div>
                <div class="py-0.5 font-semibold rounded-md text-xs px-2 bg-dark-400 text-white">Host</div>
              </div>
              <div class="text-xs">{{ event.booking.user.email }}</div>
            </div>
            <div class="flex flex-col" v-for="person of event.booking.attendees">
              <div class="flex gap-2 items-center">
                <div>{{ person.name ? person.name : person.email }}</div>
                <div class="py-0.5 font-semibold rounded-md text-xs px-2 bg-blue-500 text-white">attendee</div>
              </div>
              <div class="text-xs">{{ person.email }}</div>
            </div>
          </div>
        </div>
        <div class="flex items-center gap-2" v-if="event.booking.responses?.location.optionValue">
          <div class="flex justify-center items-center size-9 bg-secondary-100 text-secondary-500 rounded-full">
            <MapPinIcon class="size-6" />
          </div>
          <div>
            {{ event.booking.responses?.location.optionValue }}
          </div>
        </div>
        <div class="flex items-center gap-2" v-if="event.booking.description">
          <div class="flex justify-center items-center size-9 bg-secondary-100 text-secondary-500 rounded-full">
            <ChatBubbleBottomCenterTextIcon class="size-6" />
          </div>
          <div>{{ event.booking.description }}</div>
        </div>
      </div>
      <div class="py-1 px-4 flex justify-end">
        <div class="w-full flex justify-center items-center gap-5" v-if="event.booking.status === 'PENDING'">
          <button
            @click="rejectBooking"
            class="w-32 bg-red-500 text-white hover:opacity-80 rounded-md py-0.5 transition-all duration-200"
          >
            <l-ring
              v-if="isLoading === 'REJECTED'"
              size="16"
              stroke="2"
              bg-opacity="0"
              speed="2"
              color="#f0f0f0"
            ></l-ring>
            <div v-else>REJECT</div>
          </button>
          <button
            @click="acceptBooking"
            class="w-32 bg-green-500 text-white hover:opacity-80 rounded-md py-0.5 transition-all duration-200"
          >
            <l-ring
              v-if="isLoading === 'ACCEPTED'"
              size="16"
              stroke="2"
              bg-opacity="0"
              speed="2"
              color="#f0f0f0"
            ></l-ring>
            <div v-else>ACCEPT</div>
          </button>
        </div>
        <div class="w-full flex justify-center items-center gap-5" v-else-if="event.booking.status === 'ACCEPTED'">
          <button
            @click="cancelBooking"
            class="w-40 bg-red-500 text-white hover:opacity-80 rounded-md py-0.5 transition-all duration-200"
          >
            <l-ring
              v-if="isLoading === 'CANCELLED'"
              size="16"
              stroke="2"
              bg-opacity="0"
              speed="2"
              color="#f0f0f0"
            ></l-ring>
            <div v-else>CANCEL EVENT</div>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  BookmarkIcon,
  ChatBubbleBottomCenterTextIcon,
  ClockIcon,
  GlobeAltIcon,
  MapPinIcon,
  UserIcon,
  XMarkIcon,
} from "@heroicons/vue/24/outline";
import { computed, ref } from "vue";
import { SelEvent } from "../PendingTab.vue";
import { calBookingsService } from "../../services/bookings-cal-service";
import { BookingStatus } from "../../models/booking-model";
import { ring } from "ldrs";

ring.register();

interface FormattedDateTime {
  date: string;
  startTime: string;
  endTime: string;
}

function formatBookingDateTime(startTime: string, endTime: string): FormattedDateTime {
  const formatDate = (date: Date): string => {
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
  };

  const startDate = new Date(startTime);
  const endDate = new Date(endTime);

  return {
    date: formatDate(startDate),
    startTime: formatTime(startDate),
    endTime: formatTime(endDate),
  };
}

const props = defineProps({
  event: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(["close", "refresh"]);

const event = ref<SelEvent>((props.event as SelEvent) ?? null);
const when = computed(() => formatBookingDateTime(event.value.booking.startTime, event.value.booking.endTime));

const isLoading = ref<BookingStatus | null>();

async function acceptBooking() {
  if (event.value.booking.id) {
    isLoading.value = "ACCEPTED";
    const newBooking = await calBookingsService.changeBookingStatus(event.value.booking.id, "ACCEPTED");
    //  console.log("New Booking ", newBooking);
    if (newBooking && newBooking.id === event.value.booking.id) {
      event.value = {
        ...event.value,
        booking: {
          ...newBooking,
          user: event.value.booking.user,
        },
      };
    }
    emit("refresh");
    isLoading.value = null;
  }
}

async function rejectBooking() {
  if (event.value.booking.id) {
    isLoading.value = "REJECTED";
    const newBooking = await calBookingsService.changeBookingStatus(event.value.booking.id, "REJECTED");
    //  console.log("New Booking ", newBooking);
    if (newBooking && newBooking.id === event.value.booking.id) {
      event.value = {
        ...event.value,
        booking: {
          ...newBooking,
          user: event.value.booking.user,
        },
      };
    }
    emit("refresh");
    isLoading.value = null;
  }
}

async function cancelBooking() {
  if (event.value.booking.id) {
    isLoading.value = "CANCELLED";
    const newBooking = await calBookingsService.changeBookingStatus(event.value.booking.id, "CANCELLED");
    //  console.log("New Booking ", newBooking);
    if (newBooking && newBooking.id === event.value.booking.id) {
      event.value = {
        ...event.value,
        booking: {
          ...newBooking,
          user: event.value.booking.user,
        },
      };
    }
    emit("refresh");
    isLoading.value = null;
  }
}
</script>

<style scoped></style>
