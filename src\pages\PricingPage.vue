<template>

<div class="max-w-xl mx-auto pt-[50px]">
    <PageHeader title="Pricing" />
  </div>
  <div class="bg-beig">
  <div class="pt-[10px] bg-beige flex items-center justify-center">
    <div class="max-w-6xl mx-auto text-center">
      <!-- <h1 class="text-4xl font-bold text-gray-800 mb-4">Pricing</h1> -->
      <p class="text-gray-500 mb-10">Simple & Predictable pricing. No Surprises.</p>

      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Personal Plan -->
        <div class="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h2 class="text-lg font-medium text-gray-500 mb-4">Personal</h2>
          <h3 class="text-3xl font-bold text-gray-800 mb-4">Free</h3>
          <ul class="text-left space-y-2 text-gray-600">
            <li class="flex items-center">
              <CheckIcon class="w-5 h-5 inline mr-2"/>
              Lifetime free
            </li>
            <li class="flex items-center">
              <CheckIcon class="w-5 h-5 inline mr-2"/>
              Up to 3 users
            </li>
            <li class="flex items-center">
              <CheckIcon class="w-5 h-5 inline mr-2"/>
              Unlimited Pages
            </li>
            <li class="flex items-center">
              <CheckIcon class="w-5 h-5 inline mr-2"/>
              Astro Sub domain
            </li>
            <li class="flex items-center">
              <CheckIcon class="w-5 h-5 inline mr-2"/>
              Basic Integrations
            </li>
            <li class="flex items-center">
              <CheckIcon class="w-5 h-5 inline mr-2"/>
              Community Support
            </li>
          </ul>
          <button class="mt-8 px-6 py-2 border border-gray-800 text-gray-800 rounded-full font-medium hover:bg-gray-800 hover:text-white transition">Get Started</button>
        </div>

        <!-- Startup Plan -->
        <div class="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h2 class="text-lg font-medium text-gray-500 mb-4">Startup</h2>
          <h3 class="text-3xl font-bold text-gray-800 mb-4">$19</h3>
          <ul class="text-left space-y-2 text-gray-600">
            <li class="flex items-center">
              <CheckIcon class="w-5 h-5 inline mr-2"/>
              All Free Features
            </li>
            <li class="flex items-center">
              <CheckIcon class="w-5 h-5 inline mr-2"/>
              Up to 20 users
            </li>
            <li class="flex items-center">
              <CheckIcon class="w-5 h-5 inline mr-2"/>
              20 Custom domains
            </li>
            <li class="flex items-center">
              <CheckIcon class="w-5 h-5 inline mr-2"/>
              Unlimited Collaborators
            </li>
            <li class="flex items-center">
              <CheckIcon class="w-5 h-5 inline mr-2"/>
              Advanced Integrations
            </li>
            <li class="flex items-center">
              <CheckIcon class="w-5 h-5 inline mr-2"/>
              Priority Support
            </li>
          </ul>
          <button class="mt-8 px-6 py-2 bg-gray-800 text-white rounded-full font-medium hover:bg-gray-900 transition">Get Started</button>
        </div>

        <!-- Enterprise Plan -->
        <div class="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h2 class="text-lg font-medium text-gray-500 mb-4">Enterprise</h2>
          <h3 class="text-3xl font-bold text-gray-800 mb-4">Custom</h3>
          <ul class="text-left space-y-2 text-gray-600">
            <li class="flex items-center">
              <CheckIcon class="w-5 h-5 inline mr-2"/>
              All Pro Features
            </li>
            <li class="flex items-center">
              <CheckIcon class="w-5 h-5 inline mr-2"/>
              Unlimited Custom domains
            </li>
            <li class="flex items-center">
              <CheckIcon class="w-5 h-5 inline mr-2"/>
              99.99% Uptime SLA
            </li>
            <li class="flex items-center">
              <CheckIcon class="w-5 h-5 inline mr-2"/>
              SAML & SSO Integration
            </li>
            <li class="flex items-center">
              <CheckIcon class="w-5 h-5 inline mr-2"/>
              Dedicated Account Manager
            </li>
            <li class="flex items-center">
              <CheckIcon class="w-5 h-5 inline mr-2"/>
              24/7 Phone Support
            </li>
          </ul>
          <button class="mt-8 px-6 py-2 border border-gray-800 text-gray-800 rounded-full font-medium hover:bg-gray-800 hover:text-white transition">Contact us</button>
        </div>
      </div>
    </div>
  </div>
</div>
</template>

<script setup lang="ts">
import { CheckIcon } from '@heroicons/vue/24/outline';
import PageHeader from "../components/PageHeader.vue";

</script>

<style>
  .bg-beige {
    background-color: #ede6dc;
  }
</style>