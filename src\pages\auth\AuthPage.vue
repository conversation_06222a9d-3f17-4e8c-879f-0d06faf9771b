<template>
  <div class="size-full flex flex-row items-center justify-center bg-gray-100x relative overflow-hidden app-background">
    <!-- Background Icons -->
    <!-- Background Icons -->
    <!-- Background Icons -->
    <div class="absolute inset-0 z-0">
      <!-- <PERSON> Shapes -->
      <svg
        class="w-16 h-16 opacity-10 absolute left-5 top-10"
        fill="#36C5F0"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
      >
        <path
          d="M12 2.25A9.75 9.75 0 1021.75 12 9.76 9.76 0 0012 2.25zm0 17.5a7.75 7.75 0 117.75-7.75 7.76 7.76 0 01-7.75 7.75z"
        />
      </svg>
      <svg
        class="w-12 h-12 opacity-10 absolute left-20 top-24"
        fill="#2EB67D"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
      >
        <path
          d="M12 2.25A9.75 9.75 0 1021.75 12 9.76 9.76 0 0012 2.25zm0 17.5a7.75 7.75 0 117.75-7.75 7.76 7.76 0 01-7.75 7.75z"
        />
      </svg>
      <svg
        class="w-20 h-20 opacity-10 absolute right-10 top-1/4"
        fill="#ECB22E"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
      >
        <path
          d="M12 2.25A9.75 9.75 0 1021.75 12 9.76 9.76 0 0012 2.25zm0 17.5a7.75 7.75 0 117.75-7.75 7.76 7.76 0 01-7.75 7.75z"
        />
      </svg>
      <svg
        class="w-16 h-16 opacity-10 absolute left-10 bottom-1/4"
        fill="#E01E5A"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
      >
        <path
          d="M12 2.25A9.75 9.75 0 1021.75 12 9.76 9.76 0 0012 2.25zm0 17.5a7.75 7.75 0 117.75-7.75 7.76 7.76 0 01-7.75 7.75z"
        />
      </svg>
      <svg
        class="w-12 h-12 opacity-10 absolute right-20 bottom-1/3"
        fill="#36C5F0"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
      >
        <path
          d="M12 2.25A9.75 9.75 0 1021.75 12 9.76 9.76 0 0012 2.25zm0 17.5a7.75 7.75 0 117.75-7.75 7.76 7.76 0 01-7.75 7.75z"
        />
      </svg>
      <svg
        class="w-10 h-10 opacity-10 absolute right-5 bottom-10"
        fill="#2EB67D"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
      >
        <path
          d="M12 2.25A9.75 9.75 0 1021.75 12 9.76 9.76 0 0012 2.25zm0 17.5a7.75 7.75 0 117.75-7.75 7.76 7.76 0 01-7.75 7.75z"
        />
      </svg>

      <!-- Square Shapes -->
      <svg
        class="w-14 h-14 opacity-10 absolute left-28 top-10"
        fill="#ECB22E"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
      >
        <rect width="24" height="24" rx="4" />
      </svg>
      <svg
        class="w-12 h-12 opacity-10 absolute right-14 top-20"
        fill="#E01E5A"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
      >
        <rect width="24" height="24" rx="4" />
      </svg>
      <svg
        class="w-20 h-20 opacity-10 absolute left-8 bottom-10"
        fill="#36C5F0"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
      >
        <rect width="24" height="24" rx="4" />
      </svg>
      <svg
        class="w-12 h-12 opacity-10 absolute right-8 bottom-1/4"
        fill="#2EB67D"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
      >
        <rect width="24" height="24" rx="4" />
      </svg>
      <svg
        class="w-16 h-16 opacity-10 absolute left-20 bottom-1/3"
        fill="#ECB22E"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
      >
        <rect width="24" height="24" rx="4" />
      </svg>
    </div>
    <div class="flex flex-row size-full">
      <Transition name="fade" mode="out-in">
        <div :key="currentAdsIndex" class="w-1/2 h-full flex flex-col py-5 items-center">
          <div class="size-60 transition-all duration-200">
            <img :src="currentAds.illustration" class="size-full" alt="OWAY Logo" />
          </div>
          <div class="text-2xl font-bold text-primary-800">{{ currentAds.title }}</div>
          <div class="text-center text-sm text-gray-500 mt-2 px-4">
            {{ currentAds.description }}
          </div>
        </div>
      </Transition>

      <div class="w-1/2 z-10 px-4 py-20 h-full">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";

interface ADS {
  illustration: string;
  title: string;
  description: string;
}

const ads: ADS[] = [
  {
    illustration: new URL("@/assets/oway-productivity.svg", import.meta.url).href,
    // illustration: "../assets/oway-productivity.svg",
    title: "Master Your Day",
    description:
      "Stay on top of your schedule, meetings, and emails—all in one smart, streamlined app designed to boost your productivity.",
  },
  {
    // illustration: "../assets/oway-schedule-meeting.svg",
    illustration: new URL("@/assets/oway-schedule-meeting.svg", import.meta.url).href,
    title: "Easy Meeting Scheduling",
    description:
      "Coordinate with others in seconds—find the perfect time, send invites, and keep everyone on track without the back-and-forth.",
  },
  {
    // illustration: "../assets/oway-video-call.svg",
    illustration: new URL("@/assets/oway-video-call.svg", import.meta.url).href,
    title: "AI-Powered Calls",
    description:
      "Meet, talk, and let AI handle the rest—automatic summaries and smart notes so you can stay focused and never miss a detail.",
  },
];

const currentAds = ref(ads[0]);
const currentAdsIndex = ref(0);

const nextAds = () => {
  currentAdsIndex.value = (currentAdsIndex.value + 1) % ads.length;
  currentAds.value = ads[currentAdsIndex.value];
};

onMounted(() => {
  //  console.log("MainLayout mounted()");
  // Uncomment the line below if you want to call on_login on mount
  // on_login();
  setInterval(nextAds, 4000);
});
</script>

<style scoped></style>
