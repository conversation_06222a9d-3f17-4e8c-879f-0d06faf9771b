<template>
  <!-- Render SnoozedEmailsSection if viewing snoozed emails -->
  <SnoozedEmailsSection v-if="isViewingSnoozedEmails" />

  <!-- Render normal EmailsSection for other categories -->
  <div v-else class="flex h-full flex-col overflow-hidden pb-2 bg-primary">
    <div class="w-full flex justify-between items-center px-4 py-1">
      <div class="flex items-center gap-2">
        <button
          class="size-6 rounded pt-0.5"
          :class="{
            'bg-dark-400 text-white': selectedEmails.length > 0 && selectedEmails.length === sortedMessages.length,
          }"
          title="Select All"
          @click="toggleSelectAll"
        >
          <i class="pi pi-list-check"></i>
        </button>
        <span class="block h-6 w-0.5 bg-primary-600/20"></span>
        <div
          @click="toggleSortState"
          class="relative flex flex-col text-base-700 hover:text-primary-800 cursor-pointer transition-all duration-200"
          title="Sort by emails priorities"
        >
          <i v-if="sortState === 'none'" class="pi pi-align-left size-4"></i>
          <i v-if="sortState === 'desc'" class="pi pi-sort-amount-down size-4"></i>
          <i v-if="sortState === 'asc'" class="pi pi-sort-amount-down-alt size-4"></i>
        </div>
        <EmailsFilters
          :key="messages.length"
          :filters="emailsSubCategories"
          :selected-filter="selectedSubCat"
          @on-select="(value:string)=>selectedSubCat = value"
        />
      </div>
      <div class="w-full flex justify-center items-center gap-1">
        <div class="flex items-center justify-center gap-2">
          <button
            @click="goToPreviousPage"
            :disabled="currentPage <= 1"
            class="flex items-center justify-center rounded hover:bg-primary-600/20 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title="Previous page"
          >
            <i class="pi pi-chevron-left text-base-700 text-xs"></i>
          </button>
          <div class="flex items-center gap-2 text-base-700 font-medium text-sm">
            <span>{{ currentPage }}/{{ totalPages }}</span>
          </div>
          <button
            @click="goToNextPage"
            :disabled="currentPage >= totalPages"
            class="flex items-center justify-center rounded hover:bg-primary-600/20 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title="Next page"
          >
            <i class="pi pi-chevron-right text-base-700 text-xs"></i>
          </button>
        </div>
        <div class="flex justify-center items-center gap-1 font-semibold text-xs">
          <span class="text-base-500">•</span>
          <span>{{ emailsCount }} emails</span>
        </div>
      </div>
      <div class="flex gap-4">
        <!-- Mark as Read Button -->
        <button
          @click="handleBulkAction('mark-as-read')"
          type="button"
          title="Mark as read"
          class="flex items-center bg-transparent pb-1 hover:text-blue-600 text-dark-500 font-bold size-7 justify-center rounded focus:outline-none focus:shadow-outline transition-colors duration-150"
        >
          <EnvelopeOpenIcon class="size-5" />
        </button>

        <!-- Mark as Unread Button -->
        <button
          @click="handleBulkAction('mark-as-unread')"
          title="Mark as unread"
          class="flex items-center bg-transparent hover:text-blue-600 text-dark-500 font-bold size-7 justify-center rounded focus:outline-none focus:shadow-outline transition-colors duration-150"
        >
          <!-- Solid Envelope Icon -->
          <EnvelopeIcon class="size-5" />
          <!-- <span class="ml-2">Mark as Unread</span> -->
        </button>

        <button
          @click="handleBulkAction('mark-as-spam')"
          title="Mark as spam"
          class="flex items-center bg-transparent hover:text-orange-600 text-dark-500 font-bold size-7 justify-center rounded focus:outline-none focus:shadow-outline transition-colors duration-150"
        >
          <div class="relative">
            <EnvelopeIcon class="size-5" />
            <ExclamationTriangleIcon class="absolute -bottom-0.5 left-[10px] size-3 text-red-500" />
          </div>
          <!-- <span class="ml-2">Mark as Unread</span> -->
        </button>

        <!-- Delete Button -->
        <button
          @click="handleBulkAction('delete')"
          title="Delete"
          class="flex items-center bg-transparent hover:text-red-600 text-dark-500 font-bold size-7 justify-center rounded focus:outline-none focus:shadow-outline transition-colors duration-150"
        >
          <!-- Solid Trash Icon -->
          <TrashIcon class="size-5" />
          <!-- <span class="ml-2">Delete</span> -->
        </button>
      </div>
    </div>

    <!-- Pagination Controls -->

    <!-- Make sure the container takes up all remaining height and allows scrolling -->
    <div class="w-full h-full relative overflow-y-auto slider-container px-2 custom-scrollbar py-2">
      <ul class="flex flex-col gap-2 items-center">
        <li
          v-for="(emails, index) in sortedMessages"
          :key="`${emails[0].id}-${index}`"
          :class="['relative group w-full drop-shadow-md cursor-pointer transition-all duration-200']"
        >
          <EmailCard
            :selected="!!selectedEmails.find((e) => e === emails[0].id)"
            :data="emails"
            @view-email="(email:Email[])=>selectEmail(email)"
            @select-email="(email:Email[])=>selectedEmails = [...new Set([...selectedEmails,...email.map((e)=>e.id)])]"
            @unselect-email="(email:Email[])=>selectedEmails = selectedEmails.filter((se)=>!email.map((e)=>e.id).includes(se))"
          />
          <!-- <div class="group flex items-start justify-between gap-2 w-full h-full">

            <label :for="`checkbox-${emails[0].id}`" class="pt-1">
              <input
                type="checkbox"
                :id="`checkbox-${emails[0].id}`"
                v-model="selectedEmails"
                :value="emails[0].id"
                class="peer hidden form-checkbox h-4 w-4 text-dark-600 bg-transparent outline-dark-600 cursor-pointer"
              />
              <div class="peer-checked:hidden size-5 rounded-full border border-base-500 cursor-pointer"></div>
              <div
                class="peer-checked:flex justify-center items-center hidden size-5 rounded-full border-[1.5px] border-dark-400 bg-dark-400 text-white cursor-pointer"
              >
                <CheckIcon class="size-3" />
              </div>
            </label>
            <div @click="selectEmail(emails)" class="w-full">
              <div class="flex justify-between items-center">
                <h3 class="text-md font-semibold text-base-500 group-hover:text-dark-400">
                  {{ emails[0].subject.replace(/"/g, "") }}
                </h3>
              </div>
              <div class="flex text-sm justify-between text-slate-600 mt-1">
                <span>Importance: {{ emails[0].priority || "Normal" }}</span>

                <span class="text-sm text-gray-400">{{ formatGmailDate(emails[0].date.toString()) }}</span>
              </div>
            </div>
          </div>
          <div
            class="hidden group-hover:block absolute top-full left-0 bg-primary-100 text-dark-700 p-3 rounded-lg shadow-lg z-50"
          >
            <h4 class="font-semibold">{{ emails[0].snippet }}</h4>
            <p>
              {{ emails[0].thread_summary }}
            </p>
          </div> -->
        </li>
      </ul>

      <!-- Loading indicator for pagination -->
      <div v-if="isLoading" class="flex justify-center items-center py-8">
        <div class="flex items-center gap-2 text-base-600">
          <i class="pi pi-spin pi-spinner"></i>
          <span>Loading emails...</span>
        </div>
      </div>
    </div>
    <div>
      <EmailDetailView
        :key="`${selectedEmail[0]?.id ?? selectedThreadId}`"
        v-if="selectedEmail.length > 0 || selectedThreadId"
        :emails="selectedEmail"
        :thread-id="selectedThreadId"
        :access_token="currentUserStore.currentUser?.access_token"
        @close="closeEmail"
        @go-next="getNextEmail"
        @go-previous="getPrevEmail"
      />
    </div>
  </div>
  <!-- End of v-else div -->
</template>
<script setup lang="ts">
import { ref, onMounted, watch, computed } from "vue";
import { Email } from "../../types";
import { invoke } from "@tauri-apps/api/core";
import { useCurrentEmailCategoryStore } from "../../stores/currentSession";
import { useCurrentUserStore } from "../../stores/currentUser";
import EmailDetailView from "./email-page-view/EmailPageView.vue";

import { EnvelopeIcon, TrashIcon, EnvelopeOpenIcon } from "@heroicons/vue/24/outline";
import { ExclamationTriangleIcon } from "@heroicons/vue/24/solid";
import { useEmailSearchStore } from "../../stores/searchEmailStore";
import EmailCard from "./EmailCard.vue";

import EmailsFilters from "./EmailsFilters.vue";
import { EmailsOutput } from "../../models/emails-model";
import { useRoute } from "vue-router";
import SnoozedEmailsSection from "./SnoozedEmailsSection.vue";

const route = useRoute();

const currentEmailCategoryStore = useCurrentEmailCategoryStore();
const currentUserStore = useCurrentUserStore();
const inputDisabled = ref(false);
const messages = ref<Email[][]>([]);
const sortedMessages = ref<Email[][]>([]);
const selectedEmail = ref<Email[]>([]);
const selectedThreadId = ref<string>();
const selectedEmails = ref<string[]>([]); // Initialize as a reactive ref array
// State variables
const isLoading = ref(false); // Loading state for fetching emails
const emailsPerPage = 20; // Number of emails to load per patch
const sortState = ref<"none" | "asc" | "desc">("none");
const emailsCount = ref(0);

// Computed properties for pagination
const currentPage = computed(() => currentEmailCategoryStore.getCurrentPage());
const totalPages = computed(() => Math.ceil(emailsCount.value / emailsPerPage));

// Check if we're viewing snoozed emails
const isViewingSnoozedEmails = computed(
  () => currentEmailCategoryStore.currentEmailCategory?.category_id === "snoozed_emails"
);

function getEmailDomain(input: string): string {
  let email: string;

  // Check if the input contains angle brackets (e.g., "name <<EMAIL>>")
  const emailMatch = input.match(/<([^>]+)>/);

  if (emailMatch && emailMatch[1]) {
    // Extract the email from within the angle brackets
    email = emailMatch[1];
  } else {
    // Assume the input is already an email
    email = input.trim(); // Trim any extra spaces
  }

  // Extract the domain part from the email
  const domain = email.split("@")[1];

  if (!domain) {
    throw new Error("Invalid email format");
  }

  return domain;
}

const emailsSubCategories = computed(() => {
  return ["all", ...new Set(sortedMessages.value.map((email) => getEmailDomain(email[0].from).replace(/"/g, "")))];
});

const selectedSubCat = ref<"all" | string>("all");
const previousCategoryId = ref<string | undefined>();

watch(selectedSubCat, async (value) => {
  console.log("Emails sub-category =>", value);
  currentEmailCategoryStore.resetPageForCategory();
  selectedEmails.value = []; // Clear selection when subcategory changes
  await loadCurrentPageEmails();
});

const priorityOrder: { [key: string]: number } = {
  "Low Priority": 1,
  "Normal Priority": 2,
  "High Priority": 3,
};

const emailSearchStore = useEmailSearchStore();

emailSearchStore.$subscribe(async (_, state) => {
  if (state.searchQuery) {
    currentEmailCategoryStore.resetPageForCategory();
    await loadCurrentPageEmails();
  }
});

function toggleSortState() {
  switch (sortState.value) {
    case "none":
      sortState.value = "desc";
      break;
    case "desc":
      sortState.value = "asc";
      break;
    case "asc":
      sortState.value = "none";
      break;
  }
  // sort();
}

watch(sortState, async (value) => {
  // console.log("Sorting =>", value);
  if (value == "none") {
    // Reset to initial state and reload
    currentEmailCategoryStore.resetPageForCategory();
    await loadInitialEmails();
  } else {
    sort(value);
  }
});

watch(selectedEmails, (value) => {
  // console.log("SELECTED EMAILS =>", value);
});

function sort(value: "asc" | "desc" | "none") {
  const values = messages.value;
  function desc(a: number, b: number) {
    return b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;
  }

  function asc(a: number, b: number) {
    return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;
  }
  switch (value) {
    case "none":
    // Sort by latest date first if no priority sorting is selected
    // sortedMessages.value = [...messages.value].sort(
    //   (a, b) => new Date(b[0].date).getTime() - new Date(a[0].date).getTime()
    // );
    // console.log("Sorted by latest date first", sortedMessages.value);
    // break;
    case "asc":
      values.sort((a, b) => asc(priorityOrder[a[0].priority ?? "Normal"], priorityOrder[b[0].priority ?? "Normal"]));
      sortedMessages.value = [...new Set(values)];
      break;
    case "desc":
      values.sort((a, b) => desc(priorityOrder[a[0].priority ?? "Normal"], priorityOrder[b[0].priority ?? "Normal"]));
      sortedMessages.value = [...new Set(values)];
      break;
  }
}

// const selectedEmails = ref([]);
const selectEmail = (email: Email[]) => {
  selectedEmail.value = email;
  selectedThreadId.value = email[0].thread_id;
};

function getNextEmail() {
  // console.log("getNextEmail email model:", currentUserStore.currentUser?.access_token);

  // console.log("selectedEmail.value email model:", selectedEmail.value);

  if (selectedEmail.value) {
    // console.log("selectedEmail.value email model:", selectedEmail.value);
    // console.log("sortedMessages.value email model:", sortedMessages.value);

    const selIndexRaw = sortedMessages.value.findIndex((m) => m[0].id == selectedEmail.value[0].id);
    // console.log("Target ID:", selectedEmail.value[0]?.id);
    // console.log(
    //   "All IDs:",
    //   sortedMessages.value.map((m) => m[0]?.id)
    // );
    const selIndex = Math.abs(selIndexRaw); // or -selIndex; // ✅ forces non-negative
    // console.log("selIndex email model:     ", selIndexRaw);
    // console.log("selIndex email model:     ", selIndex);
    // console.log(
    //   "seltected email model:    sortedMessages.value[selIndex < sortedMessages.value.length - 1 ? selIndex + 1 : selIndex] "
    // );

    selectedEmail.value = sortedMessages.value[selIndex < sortedMessages.value.length - 1 ? selIndex + 1 : selIndex];

    //  console.log("selectedEmail.value email model:", selectedEmail.value);
  }
}
function getPrevEmail() {
  //  console.log("getPrevEmail email model:", currentUserStore.currentUser?.access_token);

  if (selectedEmail.value) {
    const selIndex = sortedMessages.value.findIndex((m) => m[0].id == selectedEmail.value[0].id);
    selectedEmail.value = sortedMessages.value[selIndex > 0 ? selIndex - 1 : selIndex];
  }
}

const closeEmail = () => {
  //  console.log("close email model:", currentUserStore.currentUser?.access_token);

  selectedEmail.value = [];
  selectedThreadId.value = undefined;
};

// Pagination navigation functions
const goToPreviousPage = async () => {
  if (currentPage.value > 1) {
    currentEmailCategoryStore.decrementPageForCurrentCategory();
    selectedEmails.value = []; // Clear selection when changing page
    await loadCurrentPageEmails();
  }
};

const goToNextPage = async () => {
  if (currentPage.value < totalPages.value) {
    currentEmailCategoryStore.incrementPageForCurrentCategory();
    selectedEmails.value = []; // Clear selection when changing page
    await loadCurrentPageEmails();
  }
};

// Handling bulk actions
const handleBulkAction = (action: string) => {
  switch (action) {
    case "mark-as-read":
      markEmailsAsRead();
      break;
    case "mark-as-unread":
      markEmailsAsUnread();
      break;
    case "mark-as-spam":
      markEmailsAsSpam();
      break;
    case "delete":
      deleteEmails();
      break;
  }
};

const markEmailsAsRead = async () => {
  try {
    //  console.log("Marking emails as read:", selectedEmails.value);

    // Step 1: Update local state
    selectedEmails.value.forEach((emailId) => {
      const email = messages.value.find((e) => e[0].id === emailId);
      if (email) email.forEach((e) => (e.is_read = true));
    });

    // Step 2: Mark emails as read in the database
    await invoke("mark_emails_as_read", { emailIds: selectedEmails.value });
    //  console.log("Emails marked as read in the database.");

    // Step 3: Mark emails as read in Gmail via API
    await markEmailsAsReadInGmail(selectedEmails.value);
    //  console.log("Emails marked as read in Gmail.");
  } catch (error) {
    console.error("Error marking emails as read:", error);
  }
};

const markEmailsAsReadInGmail = async (emailIds: string[]) => {
  try {
    for (const emailId of emailIds) {
      await invoke("mark_email_as_read_in_gmail", { emailId });
    }
    //  console.log("Emails successfully marked as read in Gmail.");
  } catch (error) {
    console.error("Failed to mark emails as read in Gmail:", error);
  }
};

const markEmailsAsUnread = async () => {
  try {
    //  console.log("Marking emails as unread:", selectedEmails.value);

    // Step 1: Update local state
    selectedEmails.value.forEach((emailId) => {
      const email = messages.value.find((e) => e[0].id === emailId);
      if (email) email.forEach((e) => (e.is_read = false));
    });

    // Step 2: Mark emails as unread in the database
    await invoke("mark_emails_as_unread", { emailIds: selectedEmails.value });
    //  console.log("Emails marked as unread in the database.");

    // Step 3: Mark emails as unread in Gmail via API
    await markEmailsAsUnreadInGmail(selectedEmails.value);
    //  console.log("Emails marked as unread in Gmail.");
  } catch (error) {
    console.error("Error marking emails as unread:", error);
  }
};

const markEmailsAsUnreadInGmail = async (emailIds: string[]) => {
  try {
    for (const emailId of emailIds) {
      await invoke("mark_email_as_unread_in_gmail", { emailId });
    }
    //  console.log("Emails successfully marked as unread in Gmail.");
  } catch (error) {
    console.error("Failed to mark emails as unread in Gmail:", error);
  }
};

const markEmailsAsSpam = async () => {
  try {
    //  console.log("Marking emails as spam:", selectedEmails.value);

    // Step 1: Update local state (optional, depends on how you display spam emails)
    messages.value = messages.value.filter((email) => !selectedEmails.value.includes(email[0].id));

    // Step 2: Mark emails as spam in the database
    await invoke("mark_emails_as_spam", { emailIds: selectedEmails.value });
    //  console.log("Emails marked as spam in the database.");

    // Step 3: Mark emails as spam in Gmail via API
    await markEmailsAsSpamInGmail(selectedEmails.value);
    //  console.log("Emails marked as spam in Gmail.");
  } catch (error) {
    console.error("Error marking emails as spam:", error);
  }
};

const markEmailsAsSpamInGmail = async (emailIds: string[]) => {
  try {
    for (const emailId of emailIds) {
      await invoke("mark_email_as_spam_in_gmail", { emailId });
    }
    //  console.log("Emails successfully marked as spam in Gmail.");
  } catch (error) {
    console.error("Failed to mark emails as spam in Gmail:", error);
  }
};

const deleteEmails = async () => {
  try {
    //  console.log("Deleting emails:", selectedEmails.value);

    // Step 1: Remove emails from the local state
    messages.value = messages.value.filter((email) => !selectedEmails.value.includes(email[0].id));

    // Step 2: Delete emails from the database
    await invoke("delete_emails", { emailIds: selectedEmails.value });
    //  console.log("Emails deleted from the database.");

    // Step 3: Delete emails from Gmail via API
    await deleteEmailsFromGmail(selectedEmails.value);
    //  console.log("Emails deleted from Gmail.");

    // Step 4: Check if the email category is empty and delete it
    const { length } = await listEmails(); // Fetch remaining emails
    emailsCount.value = length; // Fetch remaining emails
    if (length === 0) {
      await deleteEmailCategory(currentEmailCategoryStore.currentEmailCategory?.id);
      //  console.log("Email category deleted.");
    }
  } catch (error) {
    console.error("Error deleting emails:", error);
  }
};

// Function to delete emails from Gmail
const deleteEmailsFromGmail = async (emailIds: string[]) => {
  try {
    for (const emailId of emailIds) {
      await invoke("delete_email_from_gmail", { emailId });
    }
    //  console.log("Emails successfully deleted from Gmail.");
  } catch (error) {
    console.error("Failed to delete emails from Gmail:", error);
  }
};

// Function to delete the email category from the database
const deleteEmailCategory = async (categoryId: string | undefined) => {
  try {
    await invoke("delete_email_category", { categoryId });
    //  console.log("Email category deleted from the database.");
  } catch (error) {
    console.error("Failed to delete email category:", error);
  }
};

// Function to load emails for current page
const loadCurrentPageEmails = async () => {
  if (isLoading.value) return;

  isLoading.value = true;

  try {
    // Reset state for current page
    messages.value = [];
    sortedMessages.value = [];

    // Load current page
    const output = await listEmails();
    emailsCount.value = output.length;

    if (output.data && output.data.length > 0) {
      // Map the raw data to Email objects
      const newEmails = output.data.map((emails: any) =>
        emails.map((email: any) => ({
          id: email.id,
          subject: email.subject || "",
          snippet: email.snippet || "",
          from: email.from || "",
          to: email.to || "",
          cc: email.cc || "",
          bcc: email.bcc || "",
          date: new Date(email.date),
          category: email.category || "",
          labels: email.labels || "",
          attachments: email.attachments || "",
          attachment_types: email.attachment_types || "",
          total_attachment_size: email.total_attachment_size || 0,
          metadata_headers: email.metadata_headers || "",
          email_body_url: email.email_body_url || "",
          is_read: email.is_read || false,
          thread_id: email.thread_id || "",
          thread_summary: email.thread_summary || "",
          importance: email.importance || 0,
          bucket: email.bucket || "",
          priority: email.priority || "",
          urgency_score: email.urgency_score || 0,
          sentiment: email.sentiment || "",
          actionable_items: email.actionable_items || "",
          language: email.language || "",
          phishing_risk: email.phishing_risk || "",
          sender_reputation: email.sender_reputation || "",
          main_domain: email.main_domain || "",
          full_domain: email.full_domain || "",
          custom_headers: email.custom_headers || "",
          storage_location: email.storage_location || "",
          is_flagged: email.is_flagged || false,
        }))
      );

      messages.value = newEmails;
      sortedMessages.value = newEmails;
      console.log(`📧 Loaded ${newEmails.length} email groups for page ${currentPage.value}`);
    }
  } catch (error) {
    console.error("❌ Failed to load emails:", error);
  } finally {
    isLoading.value = false;
  }
};

// Function to load initial emails (reset and load first page)
const loadInitialEmails = async () => {
  currentEmailCategoryStore.resetPageForCategory();
  await loadCurrentPageEmails();
};

onMounted(async () => {
  console.log("close email model:", currentEmailCategoryStore.currentEmailCategory?.category_id);

  // Initialize previous category ID
  const currentCategoryId =
    currentEmailCategoryStore.currentEmailCategory?.category_id || currentEmailCategoryStore.currentEmailCategory?.id;
  previousCategoryId.value = currentCategoryId;

  if (currentEmailCategoryStore.currentEmailCategory?.category_id) {
    await loadCurrentPageEmails();
  } else {
    inputDisabled.value = true;
  }

  const emailId = route.query.emailId;
  if (emailId) selectedThreadId.value = emailId.toString();
});

currentEmailCategoryStore.$subscribe(async (_, state) => {
  console.log("Current Email Category Store changed:", state);

  if (!state.currentEmailCategory?.id) {
    messages.value = [];
    sortedMessages.value = [];
    inputDisabled.value = true;
    previousCategoryId.value = undefined;
    return;
  }

  // Only reset pagination if the category actually changed (not just pagination within same category)
  const currentCategoryId = state.currentEmailCategory?.category_id || state.currentEmailCategory?.id;
  const categoryChanged = previousCategoryId.value !== currentCategoryId;

  if (categoryChanged) {
    console.log("📧 Email category changed from", previousCategoryId.value, "to", currentCategoryId);
    currentEmailCategoryStore.resetPageForCategory();
    selectedSubCat.value = "all";
    selectedEmails.value = []; // Clear selection when category changes
    previousCategoryId.value = currentCategoryId;
  }

  inputDisabled.value = false;
  await loadCurrentPageEmails();
});

// Remove unused functions - keeping only what's needed

function listEmails(): Promise<EmailsOutput> {
  const page = currentEmailCategoryStore.getCurrentPage();

  return invoke("list_emails", {
    categoryId: currentEmailCategoryStore.currentEmailCategory?.category_id,
    fullChildDomains: currentEmailCategoryStore.currentEmailCategory?.child_full_domains,
    page: page,
    perPage: emailsPerPage,
    searchQuery: emailSearchStore.searchQuery ?? undefined,
    filters: selectedSubCat.value !== "all" ? { sub_category: selectedSubCat.value } : undefined,
  });
}

const toggleSelectAll = () => {
  if (sortedMessages.value.length === 0) {
    // No emails to select
    selectedEmails.value = [];
    return;
  }

  if (selectedEmails.value.length > 0 && selectedEmails.value.length === sortedMessages.value.length) {
    // If all emails are selected, deselect all
    selectedEmails.value = [];
  } else {
    // Otherwise, select all emails
    selectedEmails.value = sortedMessages.value.map((email) => email[0].id);
  }
};
</script>
<style scoped>
.custom-teal {
  background-color: #274a53;
}

.slider-indicator {
  margin-right: 10px; /* Adjust the value to move the indicator */
}
</style>
