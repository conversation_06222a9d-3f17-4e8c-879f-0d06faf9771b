@tailwind base;
@tailwind components;
@tailwind utilities;

/* @plugin "@iconify/tailwind4"; */

.custom-scrollbar::-webkit-scrollbar {
  @apply w-1.5 /* Width of the scrollbar */;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-primary-400 rounded-md cursor-pointer; /* Rounded edges for the scrollbar handle */
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  @apply bg-primary-500 /* Color of the scrollbar handle on hover */;
}

.custom-scrollbar::-webkit-scrollbar-track {
  @apply bg-primary-300/20 rounded-md /* Background color of the scrollbar track */;
}

.sx__view-container {
  @apply bg-white rounded-md;
}

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }
}

/* Custom scrollbar styles */
